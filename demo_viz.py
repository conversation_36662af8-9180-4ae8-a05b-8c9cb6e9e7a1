import librosa
import librosa.display
import matplotlib.pyplot as plt
import os

# 1. Load (librosa luôn trả về float32 và mono)
y, sr = librosa.load('dataset/fan/id_00/abnormal/00000000.wav', sr=None)

# 2. Vẽ waveform
plt.figure(figsize=(12, 4))
librosa.display.waveshow(y, sr=sr)
plt.xlabel('Time (s)')
plt.ylabel('Amplitude')
plt.title('Waveform via librosa')
plt.tight_layout()
# plt.show()
plt.savefig(os.path.join("demo_viz.png"), dpi=300)
plt.close()
