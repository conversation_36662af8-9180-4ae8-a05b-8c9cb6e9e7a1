{"version": 3, "sources": ["index.css", "App.module.css", "App.css"], "names": [], "mappings": "AAAA,UAEE,WACF,CAEA,KACE,QAAS,CACT,kHACyD,CACzD,kCAAmC,CACnC,iCAAkC,CAClC,gCACF,CAEA,EACE,qBACF,CChBA,gBACE,UAAW,CACX,WACF,CAKA,yBACE,eAAgB,CAChB,eAAiB,CACjB,iBAAkB,CAClB,wBACF,CAEA,wBACE,+BAAgC,CAChC,iBAAkB,CAClB,qBAAuB,CACvB,eACF,CAEA,2BACE,eAAgB,CAChB,QAAS,CACT,SACF,CAEA,8BACE,aAAc,CACd,oBAAqB,CACrB,eAAgB,CAChB,eAAgB,CAChB,YAAa,CACb,cACF,CAEA,sCACE,UAAY,CACZ,eAAgB,CAChB,+BACF,CAEA,yBACE,qBAAuB,CACvB,cACF,CAEA,gDAEE,2BAA4B,CAC5B,YAAa,CACb,kBACF,CAEA,iCACE,SAAU,CACV,gBACF,CAEA,wCACE,eAAiB,CACjB,aAAc,CACd,mBACF,CAEA,uCACE,aACF,CAEA,sCACE,WAAY,CACZ,iBAAkB,CAClB,UACF,CAEA,mBAGE,eAAgB,CAEhB,aAAc,CACd,YAAa,CAGb,uBAAgB,CAAhB,oBAAgB,CAAhB,eAAgB,CAChB,aACF,CAEA,qCAZE,eAAgB,CAGhB,sBAAuB,CAGvB,WAAgC,CAAhC,+BAAgC,CAChC,aAcF,CATA,kBAEE,eAAgB,CAKhB,SAAU,CACV,QACF,CAEA,0BACE,WACF,CAEA,gBACE,aACF,CAEA,yBACE,YACF,CAEA,gBACE,wBAAyB,CACzB,eAAiB,CACjB,iBAAkB,CAClB,eAAgB,CAChB,aAAc,CACd,iBAAkB,CAClB,eAAgB,CAChB,oBAAqB,CACrB,cACF,CAEA,uBACE,eAAgB,CAChB,gBACF,CAEA,yBACE,aACF,CAEA,+BACE,gCAAqC,CACrC,wBACF,CAEA,uBACE,wBAAyB,CACzB,UACF,CAEA,6BACE,wBACF,CAEA,gBACE,aACF,CAEA,oBACE,gBAAiB,CACjB,iBAAkB,CAClB,UAAW,CACX,kBAAmB,CACnB,sBAAuB,CACvB,YACF,CAEA,kBACE,WAAY,CACZ,YAAa,CACb,eAAiB,CACjB,iBAAkB,CAClB,sCAA+C,CAC/C,sBAAwB,CACxB,iBACF,CAEA,iCACE,eAAgB,CAChB,+BAAgC,CAChC,aAAc,CACd,kBAAmB,CACnB,kBACF,CAEA,2BACE,UAAY,CACZ,mBACF,CAEA,0BACE,YAAa,CACb,kBAAmB,CACnB,sBACF,CAEA,0BACE,aAAc,CACd,QACF,CAEA,mCACE,WACF,CAEA,oBACE,YACF,CAEA,oDACE,aACF,CAEA,qCACE,YAAa,CACb,UACF,CAEA,uCACE,iBACF,CAEA,6BACE,aAAc,CACd,YACF,CAEA,kCACE,UAAW,CACX,iBAAkB,CAClB,aACF,CAEA,oCACE,gBACF,CAEA,4BACE,wBACF,CAEA,kCACE,qCACF,CAEA,iCACE,wBACF,CAEA,2BACE,wBACF,CAEA,4BACE,kBACF,CAEA,6BACE,qBACF,CAEA,8BACE,iBAAkB,CAClB,oBAAqB,CACrB,iBAAkB,CAClB,WACF,CAEA,2BACE,cAAe,CACf,eAAgB,CAChB,0BAA+B,CAC/B,iBAAkB,CAClB,iBAAkB,CAClB,iBAAkB,CAClB,UAAY,CACZ,WAAY,CACZ,eAAgB,CAChB,eAAgB,CAChB,iBAAkB,CAClB,UAAW,CACX,WAAY,CACZ,QAAS,CACT,iBACF,CAEA,iCACE,cAAe,CACf,WAAY,CACZ,iBAAkB,CAClB,QAAS,CACT,QAAS,CACT,gBAAiB,CAGjB,4BAAqE,CAArE,gCACF,CAEA,+DACE,kBACF,CAEA,6CACE,mBAAoB,CACpB,6BAA8B,CAC9B,SAAU,CACV,iBACF,CAEA,mCACE,eACF,CAEA,qCACE,oBACF,CAEA,2CACE,oBAAqB,CACrB,SACF,CACA,iCACE,oBAAqB,CACrB,iBAAkB,CAClB,WACF,CAEA,2CACE,kBACF,CAEA,2CACE,kBACF,CAEA,oBACE,oBAAqB,CACrB,UAAW,CACX,WACF,CAEA,0BACE,WAAY,CACZ,aAAc,CACd,UAAW,CACX,WAAY,CACZ,UAAW,CACX,iBAAkB,CAElB,gCAAqD,CAArD,kBAAqD,CAArD,gBAAqD,CACrD,yDAAuC,CAAvC,iDACF,CAEA,sCACE,GACE,sBACF,CACA,GACE,uBACF,CACF,CAPA,8BACE,GACE,sBACF,CACA,GACE,uBACF,CACF,CAEA,oCACE,YACF,CAEA,yBACE,aAAc,CACd,UAAW,CACX,cAAe,CACf,eACF,CAEA,gCACE,aAAc,CACd,UACF,CAEA,4BACE,UAAW,CACX,+BAAgC,CAChC,aACF,CCvXA,YACE,iBAAkB,CAClB,mBAAoB,CACpB,wBAAyB,CACzB,iBAAkB,CAGlB,cAAe,CACf,eAAgB,CAGhB,WACF,CAEA,uBACE,oBACF,CAEA,sBACE,cACF,CAEA,0BACE,oBAAqB,CACrB,qBAAsB,CACtB,kBAAmB,CACnB,eAAgB,CAChB,wBAAyB,CACzB,iBAAkB,CAClB,kBAAmB,CAGnB,iBAAkB,CAClB,mBACF,CAEA,gCACE,eAAgB,CAChB,UAAW,CACX,eACF,CAEA,gEAEE,oBACF,CAEA,oBACE,oBAAqB,CAGrB,eAAgB,CAChB,iBAAkB,CAGlB,cACF,CAEA,mCACE,oBAEE,iBACF,CACF,CAEA,0BAEE,cAAe,CAGf,QAAS,CACT,SAAU,CACV,QAAS,CACT,YAAa,CAGb,iBAAkB,CAClB,mBACF,CAEA,qCACE,YACF,CAEA,yBACE,eAAgB,CAChB,iBAAkB,CAClB,QAAS,CACT,MAAO,CACP,UACF,CAEA,mCACE,yBACE,WACF,CACF,CAEA,4BACE,eAAgB,CAChB,SAAU,CACV,eAAgB,CAChB,8BAAqC,CACrC,wBAAyB,CACzB,iBAAkB,CAClB,mCACF,CAEA,4BACE,4BAA6B,CAC7B,eACF,CAEA,iCACE,yBAA0B,CAC1B,eAAgB,CAChB,eACF,CAEA,kCACE,cAAe,CACf,eACF,CAEA,sCACE,kBACF,CAEA,wCACE,UAAY,CACZ,WACF", "file": "main.fac91593.chunk.css", "sourcesContent": ["html,\nbody {\n  height: 100%;\n}\n\nbody {\n  margin: 0;\n  font-family: \"Segoe UI\", \"Roboto\", \"Oxygen\", \"Ubuntu\", \"Cantarell\",\n    \"Fira Sans\", \"Droid Sans\", \"Helvetica Neue\", sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  background-color: rgba(0, 0, 0, 0.05);\n}\n\n* {\n  box-sizing: border-box;\n}\n", ".app {\n  width: 100%;\n  height: 100%;\n}\n\n.header {\n}\n\n.header__name {\n  font-size: 1.5em;\n  font-weight: bold;\n  padding: 16px 32px;\n  text-transform: uppercase;\n}\n\n.header__nav {\n  border-bottom: solid 1px #ccd0d5;\n  padding-left: 32px;\n  background-color: white;\n  padding-top: 4px;\n}\n\n.header__nav ul {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n}\n\n.header__nav__item {\n  color: #606770;\n  display: inline-block;\n  font-size: 1.2em;\n  line-height: 2em;\n  margin: 0 8px;\n  padding: 0 12px;\n}\n\n.header__nav__item--active {\n  color: black;\n  font-weight: 600;\n  border-bottom: solid 4px #ee4c2c;\n}\n\n.filter-panel {\n  background-color: white;\n  padding: 0 24px;\n}\n\n.filter-panel,\n.viz__panel {\n  align-content: space-between;\n  display: flex;\n  flex-direction: row;\n}\n\n.filter-panel__column {\n  width: 33%;\n  padding: 12px 8px;\n}\n\n.filter-panel__column__title {\n  font-weight: bold;\n  color: #1c1e21;\n  padding-bottom: 12px;\n}\n\n.filter-panel__column__body {\n  color: #606770;\n}\n\n.filter-panel__column--end {\n  flex-grow: 1;\n  align-self: center;\n  width: auto;\n}\n\n.select {\n  background: none;\n  border: none;\n  border-radius: 0;\n  text-align-last: center;\n  padding: 0 8px;\n  margin: 0 4px;\n  border-bottom: solid 1px #1c1e21;\n  font-size: 1em;\n  appearance: none;\n  color: #1c1e21;\n}\n\n.input {\n  background: none;\n  box-shadow: none;\n  border: none;\n  font-size: 1em;\n  border-bottom: solid 1px #1c1e21;\n  text-align-last: center;\n  padding: 0;\n  margin: 0;\n}\n\n.input--narrow {\n  width: 100px;\n}\n\n.row {\n  display: block;\n}\n\n.row--padding {\n  margin: 8px 0;\n}\n\n.btn {\n  border: solid 1px #ee4c2c;\n  background: white;\n  text-align: center;\n  font-weight: 600;\n  font-size: 1em;\n  border-radius: 4px;\n  padding: 6px 8px;\n  display: inline-block;\n  cursor: pointer;\n}\n\n.btn--large {\n  font-size: 1.1em;\n  padding: 8px 10px;\n}\n\n.btn--outline {\n  color: #ee4c2c;\n}\n\n.btn--outline:hover {\n  background-color: rgba(0, 0, 0, 0.05);\n  border: solid 1px #ee4c2c;\n}\n\n.btn--solid {\n  background-color: #ee4c2c;\n  color: white;\n}\n\n.btn--solid:hover {\n  background-color: #d7725e;\n}\n\n.viz {\n  display: block;\n}\n\n.loading {\n  margin-top: 150px;\n  position: absolute;\n  width: 100%;\n  align-items: center;\n  justify-content: center;\n  display: flex;\n}\n\n.panel {\n  margin: 16px;\n  padding: 24px;\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0px 3px 6px 0px rgba(0, 0, 0, 0.18);\n  transition: opacity 0.2s; /* for loading */\n  overflow-y: scroll;\n}\n\n.panel__column__title {\n  font-weight: 700;\n  border-bottom: 2px solid #c1c1c1;\n  color: #1c1e21;\n  padding-bottom: 2px;\n  margin-bottom: 15px;\n}\n\n.panel--loading {\n  opacity: 0.5;\n  pointer-events: none; /* disables all interactions inside panel */\n}\n\n.panel--center {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.panel__column {\n  padding: 0 8px;\n  flex: 1;\n}\n\n.panel__column--stretch {\n  flex-grow: 3;\n}\n\n.gallery {\n  display: flex;\n}\n\n.gallery__item + .gallery__item {\n  padding: 0 8px;\n}\n\n.gallery__item__image img {\n  height: 200px;\n  width: auto;\n}\n\n.gallery__item__description {\n  text-align: center;\n}\n\n.bar-chart__group {\n  padding: 2px 0;\n  display: flex;\n}\n\n.bar-chart__group__bar {\n  width: 10px;\n  border-radius: 2px;\n  flex-shrink: 0;\n}\n\n.bar-chart__group__title {\n  padding-left: 8px;\n}\n\n.percentage-blue {\n  background-color: #80aaff;\n}\n\n.percentage-light-blue {\n  background-color: rgba(128, 170, 255, 0.6);\n}\n\n.percentage-light-red {\n  background-color: #e79c8d;\n}\n\n.percentage-red {\n  background-color: #d45c43;\n}\n\n.percentage-gray {\n  background: #c6c6c6;\n}\n\n.percentage-white {\n  background-color: auto;\n}\n\n.text-feature-word {\n  position: relative;\n  display: inline-block;\n  border-radius: 2px;\n  padding: 2px;\n}\n\n.tooltip__label {\n  z-index: 999999;\n  max-width: 200px;\n  background: rgba(0, 0, 0, 0.75);\n  position: absolute;\n  text-align: center;\n  border-radius: 4px;\n  color: white;\n  padding: 4px;\n  font-size: 1.1em;\n  font-weight: 600;\n  visibility: hidden;\n  width: 80px;\n  bottom: 100%;\n  left: 50%;\n  margin-left: -40px;\n}\n\n.tooltip__label::after {\n  z-index: 999999;\n  content: \" \";\n  position: absolute;\n  top: 100%;\n  left: 50%;\n  margin-left: -6px;\n  border-style: solid;\n  border-width: 6px;\n  border-color: rgba(0, 0, 0, 0.75) transparent transparent transparent;\n}\n\n.text-feature-word:hover .tooltip__label {\n  visibility: visible;\n}\n\n.general-feature__label-container {\n  display: inline-flex;\n  justify-content: space-between;\n  width: 20%;\n  padding-right: 8px;\n}\n\n.general-feature__label {\n  font-weight: 600;\n}\n\n.general-feature__percent {\n  display: inline-block;\n}\n\n.general-feature__bar-container {\n  display: inline-block;\n  width: 70%;\n}\n.general-feature__bar {\n  display: inline-block;\n  border-radius: 4px;\n  height: 12px;\n}\n\n.general-feature__bar__positive {\n  background: #80aaff;\n}\n\n.general-feature__bar__negative {\n  background: #d45c43;\n}\n\n.spinner {\n  display: inline-block;\n  width: 64px;\n  height: 64px;\n}\n\n.spinner:after {\n  content: \" \";\n  display: block;\n  width: 46px;\n  height: 46px;\n  margin: 1px;\n  border-radius: 50%;\n  border: 5px solid #ee4c2c;\n  border-color: #ee4c2c transparent #ee4c2c transparent;\n  animation: spinner 1.2s linear infinite;\n}\n\n@keyframes spinner {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n\n.visualization-container {\n  display: flex;\n}\n\n.model-number {\n  display: block;\n  height: 2em;\n  font-size: 16px;\n  font-weight: 800;\n}\n\n.model-number-spacer {\n  display: block;\n  height: 2em;\n}\n\n.model-separator {\n  width: 100%;\n  border-bottom: 2px solid #c1c1c1;\n  margin: 10px 0px;\n}\n", ".react-tags {\n  position: relative;\n  padding: 6px 0 0 6px;\n  border: 1px solid #d1d1d1;\n  border-radius: 1px;\n\n  /* shared font styles */\n  font-size: 12px;\n  line-height: 1.2;\n\n  /* clicking anywhere will focus the input */\n  cursor: text;\n}\n\n.react-tags.is-focused {\n  border-color: #b1b1b1;\n}\n\n.react-tags__selected {\n  display: inline;\n}\n\n.react-tags__selected-tag {\n  display: inline-block;\n  box-sizing: border-box;\n  margin: 0 6px 6px 0;\n  padding: 6px 8px;\n  border: 1px solid #d1d1d1;\n  border-radius: 2px;\n  background: #f1f1f1;\n\n  /* match the font styles */\n  font-size: inherit;\n  line-height: inherit;\n}\n\n.react-tags__selected-tag:after {\n  content: \"\\2715\";\n  color: #aaa;\n  margin-left: 8px;\n}\n\n.react-tags__selected-tag:hover,\n.react-tags__selected-tag:focus {\n  border-color: #b1b1b1;\n}\n\n.react-tags__search {\n  display: inline-block;\n\n  /* match tag layout */\n  padding: 7px 2px;\n  margin-bottom: 6px;\n\n  /* prevent autoresize overflowing the container */\n  max-width: 100%;\n}\n\n@media screen and (min-width: 30em) {\n  .react-tags__search {\n    /* this will become the offsetParent for suggestions */\n    position: relative;\n  }\n}\n\n.react-tags__search input {\n  /* prevent autoresize overflowing the container */\n  max-width: 100%;\n\n  /* remove styles and layout from this element */\n  margin: 0;\n  padding: 0;\n  border: 0;\n  outline: none;\n\n  /* match the font styles */\n  font-size: inherit;\n  line-height: inherit;\n}\n\n.react-tags__search input::-ms-clear {\n  display: none;\n}\n\n.react-tags__suggestions {\n  z-index: 9999999;\n  position: absolute;\n  top: 100%;\n  left: 0;\n  width: 100%;\n}\n\n@media screen and (min-width: 30em) {\n  .react-tags__suggestions {\n    width: 100px;\n  }\n}\n\n.react-tags__suggestions ul {\n  margin: 4px -1px;\n  padding: 0;\n  list-style: none;\n  background: rgba(255, 255, 255, 0.95);\n  border: 1px solid #d1d1d1;\n  border-radius: 2px;\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);\n}\n\n.react-tags__suggestions li {\n  border-bottom: 1px solid #ddd;\n  padding: 6px 8px;\n}\n\n.react-tags__suggestions li mark {\n  text-decoration: underline;\n  background: none;\n  font-weight: 600;\n}\n\n.react-tags__suggestions li:hover {\n  cursor: pointer;\n  background: #eee;\n}\n\n.react-tags__suggestions li.is-active {\n  background: #b7cfe0;\n}\n\n.react-tags__suggestions li.is-disabled {\n  opacity: 0.5;\n  cursor: auto;\n}\n"]}