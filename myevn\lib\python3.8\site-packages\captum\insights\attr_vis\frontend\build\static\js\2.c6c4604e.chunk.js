/*! For license information please see 2.c6c4604e.chunk.js.LICENSE.txt */
(this.webpackJsonpfrontend=this.webpackJsonpfrontend||[]).push([[2],[function(e,t,n){"use strict";e.exports=n(49)},,function(e,t,n){var r=n(33),i="object"==typeof self&&self&&self.Object===Object&&self,a=r||i||Function("return this")();e.exports=a},function(e,t){var n=Array.isArray;e.exports=n},function(e,t,n){"use strict";function r(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}n.d(t,"a",(function(){return r}))},function(e,t,n){"use strict";function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}n.d(t,"a",(function(){return r}))},function(e,t,n){"use strict";function r(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function i(e,t,n){return t&&r(e.prototype,t),n&&r(e,n),e}n.d(t,"a",(function(){return i}))},function(e,t,n){"use strict";function r(e){return(r=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function i(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function a(e){return(a="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function o(e,t){return!t||"object"!==a(t)&&"function"!==typeof t?function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e):t}function l(e){return function(){var t,n=r(e);if(i()){var a=r(this).constructor;t=Reflect.construct(n,arguments,a)}else t=n.apply(this,arguments);return o(this,t)}}n.d(t,"a",(function(){return l}))},function(e,t,n){"use strict";function r(e,t){return(r=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function i(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&r(e,t)}n.d(t,"a",(function(){return i}))},function(e,t,n){var r=n(74),i=n(80);e.exports=function(e,t){var n=i(e,t);return r(n)?n:void 0}},function(e,t,n){var r=n(14),i=n(76),a=n(77),o=r?r.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":o&&o in Object(e)?i(e):a(e)}},function(e,t){e.exports=function(e){return null!=e&&"object"==typeof e}},function(e,t,n){var r=n(64),i=n(65),a=n(66),o=n(67),l=n(68);function s(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}s.prototype.clear=r,s.prototype.delete=i,s.prototype.get=a,s.prototype.has=o,s.prototype.set=l,e.exports=s},function(e,t,n){var r=n(31);e.exports=function(e,t){for(var n=e.length;n--;)if(r(e[n][0],t))return n;return-1}},function(e,t,n){var r=n(2).Symbol;e.exports=r},function(e,t,n){var r=n(9)(Object,"create");e.exports=r},function(e,t,n){var r=n(89);e.exports=function(e,t){var n=e.__data__;return r(t)?n["string"==typeof t?"string":"hash"]:n.map}},function(e,t,n){var r=n(27);e.exports=function(e){if("string"==typeof e||r(e))return e;var t=e+"";return"0"==t&&1/e==-1/0?"-0":t}},function(e,t,n){"use strict";function r(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function i(e){return function(e){if(Array.isArray(e))return r(e)}(e)||function(e){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||function(e,t){if(e){if("string"===typeof e)return r(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(n):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?r(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}n.d(t,"a",(function(){return i}))},function(e,t){e.exports=function(e){return e.webpackPolyfill||(e.deprecate=function(){},e.paths=[],e.children||(e.children=[]),Object.defineProperty(e,"loaded",{enumerable:!0,get:function(){return e.l}}),Object.defineProperty(e,"id",{enumerable:!0,get:function(){return e.i}}),e.webpackPolyfill=1),e}},function(e,t,n){var r=n(63),i=n(11);e.exports=function e(t,n,a,o,l){return t===n||(null==t||null==n||!i(t)&&!i(n)?t!==t&&n!==n:r(t,n,a,o,e,l))}},function(e,t,n){var r=n(9)(n(2),"Map");e.exports=r},function(e,t){e.exports=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}},function(e,t,n){var r=n(81),i=n(88),a=n(90),o=n(91),l=n(92);function s(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}s.prototype.clear=r,s.prototype.delete=i,s.prototype.get=a,s.prototype.has=o,s.prototype.set=l,e.exports=s},function(e,t,n){var r=n(109),i=n(116),a=n(40);e.exports=function(e){return a(e)?r(e):i(e)}},function(e,t){e.exports=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991}},function(e,t,n){var r=n(3),i=n(27),a=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,o=/^\w*$/;e.exports=function(e,t){if(r(e))return!1;var n=typeof e;return!("number"!=n&&"symbol"!=n&&"boolean"!=n&&null!=e&&!i(e))||(o.test(e)||!a.test(e)||null!=t&&e in Object(t))}},function(e,t,n){var r=n(10),i=n(11);e.exports=function(e){return"symbol"==typeof e||i(e)&&"[object Symbol]"==r(e)}},function(e,t,n){"use strict";var r=Object.getOwnPropertySymbols,i=Object.prototype.hasOwnProperty,a=Object.prototype.propertyIsEnumerable;function o(e){if(null===e||void 0===e)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(e)}e.exports=function(){try{if(!Object.assign)return!1;var e=new String("abc");if(e[5]="de","5"===Object.getOwnPropertyNames(e)[0])return!1;for(var t={},n=0;n<10;n++)t["_"+String.fromCharCode(n)]=n;if("0123456789"!==Object.getOwnPropertyNames(t).map((function(e){return t[e]})).join(""))return!1;var r={};return"abcdefghijklmnopqrst".split("").forEach((function(e){r[e]=e})),"abcdefghijklmnopqrst"===Object.keys(Object.assign({},r)).join("")}catch(i){return!1}}()?Object.assign:function(e,t){for(var n,l,s=o(e),u=1;u<arguments.length;u++){for(var c in n=Object(arguments[u]))i.call(n,c)&&(s[c]=n[c]);if(r){l=r(n);for(var d=0;d<l.length;d++)a.call(n,l[d])&&(s[l[d]]=n[l[d]])}}return s}},function(e,t,n){e.exports=n(54)()},function(e,t,n){var r=n(12),i=n(69),a=n(70),o=n(71),l=n(72),s=n(73);function u(e){var t=this.__data__=new r(e);this.size=t.size}u.prototype.clear=i,u.prototype.delete=a,u.prototype.get=o,u.prototype.has=l,u.prototype.set=s,e.exports=u},function(e,t){e.exports=function(e,t){return e===t||e!==e&&t!==t}},function(e,t,n){var r=n(10),i=n(22);e.exports=function(e){if(!i(e))return!1;var t=r(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}},function(e,t,n){(function(t){var n="object"==typeof t&&t&&t.Object===Object&&t;e.exports=n}).call(this,n(75))},function(e,t){var n=Function.prototype.toString;e.exports=function(e){if(null!=e){try{return n.call(e)}catch(t){}try{return e+""}catch(t){}}return""}},function(e,t,n){var r=n(93),i=n(96),a=n(97);e.exports=function(e,t,n,o,l,s){var u=1&n,c=e.length,d=t.length;if(c!=d&&!(u&&d>c))return!1;var f=s.get(e),h=s.get(t);if(f&&h)return f==t&&h==e;var p=-1,g=!0,m=2&n?new r:void 0;for(s.set(e,t),s.set(t,e);++p<c;){var v=e[p],y=t[p];if(o)var b=u?o(y,v,p,t,e,s):o(v,y,p,e,t,s);if(void 0!==b){if(b)continue;g=!1;break}if(m){if(!i(t,(function(e,t){if(!a(m,t)&&(v===e||l(v,e,n,o,s)))return m.push(t)}))){g=!1;break}}else if(v!==y&&!l(v,y,n,o,s)){g=!1;break}}return s.delete(e),s.delete(t),g}},function(e,t,n){var r=n(111),i=n(11),a=Object.prototype,o=a.hasOwnProperty,l=a.propertyIsEnumerable,s=r(function(){return arguments}())?r:function(e){return i(e)&&o.call(e,"callee")&&!l.call(e,"callee")};e.exports=s},function(e,t,n){(function(e){var r=n(2),i=n(112),a=t&&!t.nodeType&&t,o=a&&"object"==typeof e&&e&&!e.nodeType&&e,l=o&&o.exports===a?r.Buffer:void 0,s=(l?l.isBuffer:void 0)||i;e.exports=s}).call(this,n(19)(e))},function(e,t){var n=/^(?:0|[1-9]\d*)$/;e.exports=function(e,t){var r=typeof e;return!!(t=null==t?9007199254740991:t)&&("number"==r||"symbol"!=r&&n.test(e))&&e>-1&&e%1==0&&e<t}},function(e,t,n){var r=n(113),i=n(114),a=n(115),o=a&&a.isTypedArray,l=o?i(o):r;e.exports=l},function(e,t,n){var r=n(32),i=n(25);e.exports=function(e){return null!=e&&i(e.length)&&!r(e)}},function(e,t,n){var r=n(22);e.exports=function(e){return e===e&&!r(e)}},function(e,t){e.exports=function(e,t){return function(n){return null!=n&&(n[e]===t&&(void 0!==t||e in Object(n)))}}},function(e,t,n){var r=n(44),i=n(17);e.exports=function(e,t){for(var n=0,a=(t=r(t,e)).length;null!=e&&n<a;)e=e[i(t[n++])];return n&&n==a?e:void 0}},function(e,t,n){var r=n(3),i=n(26),a=n(142),o=n(145);e.exports=function(e,t){return r(e)?e:i(e,t)?[e]:a(o(e))}},function(e,t,n){"use strict";!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE){0;try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}}(),e.exports=n(50)},function(e,t,n){"use strict";var r=n(0),i=n(29),a=n(56),o=n(57),l=n(58),s=13,u=9,c=8,d=38,f=40,h={root:"react-tags",rootFocused:"is-focused",selected:"react-tags__selected",selectedTag:"react-tags__selected-tag",selectedTagName:"react-tags__selected-tag-name",search:"react-tags__search",searchInput:"react-tags__search-input",suggestions:"react-tags__suggestions",suggestionActive:"is-active",suggestionDisabled:"is-disabled"},p=function(e){function t(t){e.call(this,t),this.state={query:"",focused:!1,expandable:!1,selectedIndex:-1,classNames:Object.assign({},h,this.props.classNames)},this.inputEventHandlers={onChange:function(){},onBlur:this.handleBlur.bind(this),onFocus:this.handleFocus.bind(this),onInput:this.handleInput.bind(this),onKeyDown:this.handleKeyDown.bind(this)}}return e&&(t.__proto__=e),t.prototype=Object.create(e&&e.prototype),t.prototype.constructor=t,t.prototype.componentWillReceiveProps=function(e){this.setState({classNames:Object.assign({},h,e.classNames)})},t.prototype.handleInput=function(e){var t=e.target.value;this.props.handleInputChange&&this.props.handleInputChange(t),this.setState({query:t})},t.prototype.handleKeyDown=function(e){var t=this.state,n=t.query,r=t.selectedIndex,i=this.props,a=i.delimiters,o=i.delimiterChars;(a.indexOf(e.keyCode)>-1||o.indexOf(e.key)>-1)&&((n||r>-1)&&e.preventDefault(),this.handleDelimiter()),e.keyCode===c&&0===n.length&&this.props.allowBackspace&&this.deleteTag(this.props.tags.length-1),e.keyCode===d&&(e.preventDefault(),r<=0?this.setState({selectedIndex:this.suggestions.state.options.length-1}):this.setState({selectedIndex:r-1})),e.keyCode===f&&(e.preventDefault(),this.setState({selectedIndex:(r+1)%this.suggestions.state.options.length}))},t.prototype.handleDelimiter=function(){var e=this.state,t=e.query,n=e.selectedIndex;if(t.length>=this.props.minQueryLength){var r=this.suggestions.state.options.findIndex((function(e){return 0===e.name.search(new RegExp("^"+t+"$","i"))})),i=-1===n?r:n;i>-1&&this.suggestions.state.options[i]?this.addTag(this.suggestions.state.options[i]):this.props.allowNew&&this.addTag({name:t})}},t.prototype.handleClick=function(e){document.activeElement!==e.target&&this.input.input.focus()},t.prototype.handleBlur=function(){this.setState({focused:!1,selectedIndex:-1}),this.props.handleBlur&&this.props.handleBlur(),this.props.addOnBlur&&this.handleDelimiter()},t.prototype.handleFocus=function(){this.setState({focused:!0}),this.props.handleFocus&&this.props.handleFocus()},t.prototype.addTag=function(e){e.disabled||("function"!==typeof this.props.handleValidate||this.props.handleValidate(e))&&(this.props.handleAddition(e),this.setState({query:"",selectedIndex:-1}))},t.prototype.deleteTag=function(e){this.props.handleDelete(e),this.props.clearInputOnDelete&&""!==this.state.query&&this.setState({query:""})},t.prototype.render=function(){var e=this,t=this.props.tagComponent||a,n=this.props.tags.map((function(n,i){return r.createElement(t,{key:i,tag:n,classNames:e.state.classNames,onDelete:e.deleteTag.bind(e,i)})})),i=this.state.focused&&this.state.query.length>=this.props.minQueryLength,s=[this.state.classNames.root];return this.state.focused&&s.push(this.state.classNames.rootFocused),r.createElement("div",{className:s.join(" "),onClick:this.handleClick.bind(this)},r.createElement("div",{className:this.state.classNames.selected,"aria-live":"polite","aria-relevant":"additions removals"},n),r.createElement("div",{className:this.state.classNames.search},r.createElement(o,Object.assign({},this.state,{inputAttributes:this.props.inputAttributes,inputEventHandlers:this.inputEventHandlers,ref:function(t){e.input=t},listboxId:"ReactTags-listbox",autofocus:this.props.autofocus,autoresize:this.props.autoresize,expandable:i,placeholder:this.props.placeholder})),r.createElement(l,Object.assign({},this.state,{ref:function(t){e.suggestions=t},listboxId:"ReactTags-listbox",expandable:i,noSuggestionsText:this.props.noSuggestionsText,suggestions:this.props.suggestions,suggestionsFilter:this.props.suggestionsFilter,addTag:this.addTag.bind(this),maxSuggestionsLength:this.props.maxSuggestionsLength}))))},t}(r.Component);p.defaultProps={tags:[],placeholder:"Add new tag",noSuggestionsText:null,suggestions:[],suggestionsFilter:null,autofocus:!0,autoresize:!0,delimiters:[u,s],delimiterChars:[],minQueryLength:2,maxSuggestionsLength:6,allowNew:!1,allowBackspace:!0,tagComponent:null,inputAttributes:{},addOnBlur:!1,clearInputOnDelete:!0},p.propTypes={tags:i.arrayOf(i.object),placeholder:i.string,noSuggestionsText:i.string,suggestions:i.arrayOf(i.object),suggestionsFilter:i.func,autofocus:i.bool,autoresize:i.bool,delimiters:i.arrayOf(i.number),delimiterChars:i.arrayOf(i.string),handleDelete:i.func.isRequired,handleAddition:i.func.isRequired,handleInputChange:i.func,handleFocus:i.func,handleBlur:i.func,handleValidate:i.func,minQueryLength:i.number,maxSuggestionsLength:i.number,classNames:i.object,allowNew:i.bool,allowBackspace:i.bool,tagComponent:i.oneOfType([i.func,i.element]),inputAttributes:i.object,addOnBlur:i.bool,clearInputOnDelete:i.bool},e.exports=p},function(e,t,n){"use strict";(function(e){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Chart",{enumerable:!0,get:function(){return a.default}}),t.defaults=t.Scatter=t.Bubble=t.Polar=t.Radar=t.HorizontalBar=t.Bar=t.Line=t.Pie=t.Doughnut=t.default=void 0;var r=s(n(0)),i=s(n(29)),a=s(n(60)),o=s(n(62)),l=s(n(125));function s(e){return e&&e.__esModule?e:{default:e}}function u(){return(u=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function c(e){return(c="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function d(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function f(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function h(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?f(Object(n),!0).forEach((function(t){k(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):f(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function p(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function g(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function m(e,t,n){return t&&g(e.prototype,t),n&&g(e,n),e}function v(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&y(e,t)}function y(e,t){return(y=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}function b(e){var t=function(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=w(e);if(t){var i=w(this).constructor;n=Reflect.construct(r,arguments,i)}else n=r.apply(this,arguments);return x(this,n)}}function x(e,t){return!t||"object"!==c(t)&&"function"!==typeof t?_(e):t}function _(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function w(e){return(w=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function k(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var S="undefined"!==typeof e&&Object({NODE_ENV:"production",PUBLIC_URL:".",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0})&&"production",T=function(e){v(n,e);var t=b(n);function n(){var e;return p(this,n),k(_(e=t.call(this)),"handleOnClick",(function(t){var n=e.chartInstance,r=e.props,i=r.getDatasetAtEvent,a=r.getElementAtEvent,o=r.getElementsAtEvent,l=r.onElementsClick;i&&i(n.getDatasetAtEvent(t),t),a&&a(n.getElementAtEvent(t),t),o&&o(n.getElementsAtEvent(t),t),l&&l(n.getElementsAtEvent(t),t)})),k(_(e),"ref",(function(t){e.element=t})),e.chartInstance=void 0,e}return m(n,[{key:"componentDidMount",value:function(){this.renderChart()}},{key:"componentDidUpdate",value:function(){if(this.props.redraw)return this.destroyChart(),void this.renderChart();this.updateChart()}},{key:"shouldComponentUpdate",value:function(e){var t=this.props,n=(t.redraw,t.type),r=t.options,i=t.plugins,a=t.legend,l=t.height,s=t.width;if(!0===e.redraw)return!0;if(l!==e.height||s!==e.width)return!0;if(n!==e.type)return!0;if(!(0,o.default)(a,e.legend))return!0;if(!(0,o.default)(r,e.options))return!0;var u=this.transformDataProp(e);return!(0,o.default)(this.shadowDataProp,u)||!(0,o.default)(i,e.plugins)}},{key:"componentWillUnmount",value:function(){this.destroyChart()}},{key:"transformDataProp",value:function(e){var t=e.data;return"function"==typeof t?t(this.element):t}},{key:"memoizeDataProps",value:function(){if(this.props.data){var e=this.transformDataProp(this.props);return this.shadowDataProp=h(h({},e),{},{datasets:e.datasets&&e.datasets.map((function(e){return h({},e)}))}),this.saveCurrentDatasets(),e}}},{key:"checkDatasets",value:function(e){var t="production"!==S&&"prod"!==S,r=this.props.datasetKeyProvider!==n.getLabelAsKey,i=e.length>1;if(t&&i&&!r){var a=!1;e.forEach((function(e){e.label||(a=!0)})),a&&console.error('[react-chartjs-2] Warning: Each dataset needs a unique key. By default, the "label" property on each dataset is used. Alternatively, you may provide a "datasetKeyProvider" as a prop that returns a unique key.')}}},{key:"getCurrentDatasets",value:function(){return this.chartInstance&&this.chartInstance.config.data&&this.chartInstance.config.data.datasets||[]}},{key:"saveCurrentDatasets",value:function(){var e=this;this.datasets=this.datasets||{},this.getCurrentDatasets().forEach((function(t){e.datasets[e.props.datasetKeyProvider(t)]=t}))}},{key:"updateChart",value:function(){var e=this,t=this.props.options,n=this.memoizeDataProps(this.props);if(this.chartInstance){t&&(this.chartInstance.options=a.default.helpers.configMerge(this.chartInstance.options,t));var r=this.getCurrentDatasets(),i=n.datasets||[];this.checkDatasets(r);var o=(0,l.default)(r,this.props.datasetKeyProvider);this.chartInstance.config.data.datasets=i.map((function(t){var n=o[e.props.datasetKeyProvider(t)];if(n&&n.type===t.type&&t.data){n.data.splice(t.data.length),t.data.forEach((function(e,r){n.data[r]=t.data[r]}));t.data;var r=d(t,["data"]);return h(h({},n),r)}return t}));n.datasets;var s=d(n,["datasets"]);this.chartInstance.config.data=h(h({},this.chartInstance.config.data),s),this.chartInstance.update()}}},{key:"renderChart",value:function(){var e=this.props,t=e.options,r=e.legend,i=e.type,l=e.plugins,s=this.element,u=this.memoizeDataProps();"undefined"===typeof r||(0,o.default)(n.defaultProps.legend,r)||(t.legend=r),this.chartInstance=new a.default(s,{type:i,data:u,options:t,plugins:l})}},{key:"destroyChart",value:function(){if(this.chartInstance){this.saveCurrentDatasets();var e=Object.values(this.datasets);this.chartInstance.config.data.datasets=e,this.chartInstance.destroy()}}},{key:"render",value:function(){var e=this.props,t=e.height,n=e.width,i=e.id;return r.default.createElement("canvas",{ref:this.ref,height:t,width:n,id:i,onClick:this.handleOnClick})}}]),n}(r.default.Component);k(T,"getLabelAsKey",(function(e){return e.label})),k(T,"propTypes",{data:i.default.oneOfType([i.default.object,i.default.func]).isRequired,getDatasetAtEvent:i.default.func,getElementAtEvent:i.default.func,getElementsAtEvent:i.default.func,height:i.default.number,legend:i.default.object,onElementsClick:i.default.func,options:i.default.object,plugins:i.default.arrayOf(i.default.object),redraw:i.default.bool,type:function(e,t,n){if(!a.default.controllers[e[t]])return new Error("Invalid chart type `"+e[t]+"` supplied to `"+n+"`.")},width:i.default.number,datasetKeyProvider:i.default.func}),k(T,"defaultProps",{legend:{display:!0,position:"bottom"},type:"doughnut",height:150,width:300,redraw:!1,options:{},datasetKeyProvider:T.getLabelAsKey});var M=T;t.default=M;var C=function(e){v(n,e);var t=b(n);function n(){return p(this,n),t.apply(this,arguments)}return m(n,[{key:"render",value:function(){var e=this;return r.default.createElement(T,u({},this.props,{ref:function(t){return e.chartInstance=t&&t.chartInstance},type:"doughnut"}))}}]),n}(r.default.Component);t.Doughnut=C;var P=function(e){v(n,e);var t=b(n);function n(){return p(this,n),t.apply(this,arguments)}return m(n,[{key:"render",value:function(){var e=this;return r.default.createElement(T,u({},this.props,{ref:function(t){return e.chartInstance=t&&t.chartInstance},type:"pie"}))}}]),n}(r.default.Component);t.Pie=P;var D=function(e){v(n,e);var t=b(n);function n(){return p(this,n),t.apply(this,arguments)}return m(n,[{key:"render",value:function(){var e=this;return r.default.createElement(T,u({},this.props,{ref:function(t){return e.chartInstance=t&&t.chartInstance},type:"line"}))}}]),n}(r.default.Component);t.Line=D;var E=function(e){v(n,e);var t=b(n);function n(){return p(this,n),t.apply(this,arguments)}return m(n,[{key:"render",value:function(){var e=this;return r.default.createElement(T,u({},this.props,{ref:function(t){return e.chartInstance=t&&t.chartInstance},type:"bar"}))}}]),n}(r.default.Component);t.Bar=E;var O=function(e){v(n,e);var t=b(n);function n(){return p(this,n),t.apply(this,arguments)}return m(n,[{key:"render",value:function(){var e=this;return r.default.createElement(T,u({},this.props,{ref:function(t){return e.chartInstance=t&&t.chartInstance},type:"horizontalBar"}))}}]),n}(r.default.Component);t.HorizontalBar=O;var N=function(e){v(n,e);var t=b(n);function n(){return p(this,n),t.apply(this,arguments)}return m(n,[{key:"render",value:function(){var e=this;return r.default.createElement(T,u({},this.props,{ref:function(t){return e.chartInstance=t&&t.chartInstance},type:"radar"}))}}]),n}(r.default.Component);t.Radar=N;var I=function(e){v(n,e);var t=b(n);function n(){return p(this,n),t.apply(this,arguments)}return m(n,[{key:"render",value:function(){var e=this;return r.default.createElement(T,u({},this.props,{ref:function(t){return e.chartInstance=t&&t.chartInstance},type:"polarArea"}))}}]),n}(r.default.Component);t.Polar=I;var A=function(e){v(n,e);var t=b(n);function n(){return p(this,n),t.apply(this,arguments)}return m(n,[{key:"render",value:function(){var e=this;return r.default.createElement(T,u({},this.props,{ref:function(t){return e.chartInstance=t&&t.chartInstance},type:"bubble"}))}}]),n}(r.default.Component);t.Bubble=A;var F=function(e){v(n,e);var t=b(n);function n(){return p(this,n),t.apply(this,arguments)}return m(n,[{key:"render",value:function(){var e=this;return r.default.createElement(T,u({},this.props,{ref:function(t){return e.chartInstance=t&&t.chartInstance},type:"scatter"}))}}]),n}(r.default.Component);t.Scatter=F;var R=a.default.defaults;t.defaults=R}).call(this,n(59))},,function(e,t,n){"use strict";var r=n(28),i="function"===typeof Symbol&&Symbol.for,a=i?Symbol.for("react.element"):60103,o=i?Symbol.for("react.portal"):60106,l=i?Symbol.for("react.fragment"):60107,s=i?Symbol.for("react.strict_mode"):60108,u=i?Symbol.for("react.profiler"):60114,c=i?Symbol.for("react.provider"):60109,d=i?Symbol.for("react.context"):60110,f=i?Symbol.for("react.forward_ref"):60112,h=i?Symbol.for("react.suspense"):60113,p=i?Symbol.for("react.memo"):60115,g=i?Symbol.for("react.lazy"):60116,m="function"===typeof Symbol&&Symbol.iterator;function v(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var y={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},b={};function x(e,t,n){this.props=e,this.context=t,this.refs=b,this.updater=n||y}function _(){}function w(e,t,n){this.props=e,this.context=t,this.refs=b,this.updater=n||y}x.prototype.isReactComponent={},x.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error(v(85));this.updater.enqueueSetState(this,e,t,"setState")},x.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},_.prototype=x.prototype;var k=w.prototype=new _;k.constructor=w,r(k,x.prototype),k.isPureReactComponent=!0;var S={current:null},T=Object.prototype.hasOwnProperty,M={key:!0,ref:!0,__self:!0,__source:!0};function C(e,t,n){var r,i={},o=null,l=null;if(null!=t)for(r in void 0!==t.ref&&(l=t.ref),void 0!==t.key&&(o=""+t.key),t)T.call(t,r)&&!M.hasOwnProperty(r)&&(i[r]=t[r]);var s=arguments.length-2;if(1===s)i.children=n;else if(1<s){for(var u=Array(s),c=0;c<s;c++)u[c]=arguments[c+2];i.children=u}if(e&&e.defaultProps)for(r in s=e.defaultProps)void 0===i[r]&&(i[r]=s[r]);return{$$typeof:a,type:e,key:o,ref:l,props:i,_owner:S.current}}function P(e){return"object"===typeof e&&null!==e&&e.$$typeof===a}var D=/\/+/g,E=[];function O(e,t,n,r){if(E.length){var i=E.pop();return i.result=e,i.keyPrefix=t,i.func=n,i.context=r,i.count=0,i}return{result:e,keyPrefix:t,func:n,context:r,count:0}}function N(e){e.result=null,e.keyPrefix=null,e.func=null,e.context=null,e.count=0,10>E.length&&E.push(e)}function I(e,t,n){return null==e?0:function e(t,n,r,i){var l=typeof t;"undefined"!==l&&"boolean"!==l||(t=null);var s=!1;if(null===t)s=!0;else switch(l){case"string":case"number":s=!0;break;case"object":switch(t.$$typeof){case a:case o:s=!0}}if(s)return r(i,t,""===n?"."+A(t,0):n),1;if(s=0,n=""===n?".":n+":",Array.isArray(t))for(var u=0;u<t.length;u++){var c=n+A(l=t[u],u);s+=e(l,c,r,i)}else if(null===t||"object"!==typeof t?c=null:c="function"===typeof(c=m&&t[m]||t["@@iterator"])?c:null,"function"===typeof c)for(t=c.call(t),u=0;!(l=t.next()).done;)s+=e(l=l.value,c=n+A(l,u++),r,i);else if("object"===l)throw r=""+t,Error(v(31,"[object Object]"===r?"object with keys {"+Object.keys(t).join(", ")+"}":r,""));return s}(e,"",t,n)}function A(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+(""+e).replace(/[=:]/g,(function(e){return t[e]}))}(e.key):t.toString(36)}function F(e,t){e.func.call(e.context,t,e.count++)}function R(e,t,n){var r=e.result,i=e.keyPrefix;e=e.func.call(e.context,t,e.count++),Array.isArray(e)?L(e,r,n,(function(e){return e})):null!=e&&(P(e)&&(e=function(e,t){return{$$typeof:a,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(e,i+(!e.key||t&&t.key===e.key?"":(""+e.key).replace(D,"$&/")+"/")+n)),r.push(e))}function L(e,t,n,r,i){var a="";null!=n&&(a=(""+n).replace(D,"$&/")+"/"),I(e,R,t=O(t,a,r,i)),N(t)}var z={current:null};function j(){var e=z.current;if(null===e)throw Error(v(321));return e}var W={ReactCurrentDispatcher:z,ReactCurrentBatchConfig:{suspense:null},ReactCurrentOwner:S,IsSomeRendererActing:{current:!1},assign:r};t.Children={map:function(e,t,n){if(null==e)return e;var r=[];return L(e,r,null,t,n),r},forEach:function(e,t,n){if(null==e)return e;I(e,F,t=O(null,null,t,n)),N(t)},count:function(e){return I(e,(function(){return null}),null)},toArray:function(e){var t=[];return L(e,t,null,(function(e){return e})),t},only:function(e){if(!P(e))throw Error(v(143));return e}},t.Component=x,t.Fragment=l,t.Profiler=u,t.PureComponent=w,t.StrictMode=s,t.Suspense=h,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=W,t.cloneElement=function(e,t,n){if(null===e||void 0===e)throw Error(v(267,e));var i=r({},e.props),o=e.key,l=e.ref,s=e._owner;if(null!=t){if(void 0!==t.ref&&(l=t.ref,s=S.current),void 0!==t.key&&(o=""+t.key),e.type&&e.type.defaultProps)var u=e.type.defaultProps;for(c in t)T.call(t,c)&&!M.hasOwnProperty(c)&&(i[c]=void 0===t[c]&&void 0!==u?u[c]:t[c])}var c=arguments.length-2;if(1===c)i.children=n;else if(1<c){u=Array(c);for(var d=0;d<c;d++)u[d]=arguments[d+2];i.children=u}return{$$typeof:a,type:e.type,key:o,ref:l,props:i,_owner:s}},t.createContext=function(e,t){return void 0===t&&(t=null),(e={$$typeof:d,_calculateChangedBits:t,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null}).Provider={$$typeof:c,_context:e},e.Consumer=e},t.createElement=C,t.createFactory=function(e){var t=C.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:f,render:e}},t.isValidElement=P,t.lazy=function(e){return{$$typeof:g,_ctor:e,_status:-1,_result:null}},t.memo=function(e,t){return{$$typeof:p,type:e,compare:void 0===t?null:t}},t.useCallback=function(e,t){return j().useCallback(e,t)},t.useContext=function(e,t){return j().useContext(e,t)},t.useDebugValue=function(){},t.useEffect=function(e,t){return j().useEffect(e,t)},t.useImperativeHandle=function(e,t,n){return j().useImperativeHandle(e,t,n)},t.useLayoutEffect=function(e,t){return j().useLayoutEffect(e,t)},t.useMemo=function(e,t){return j().useMemo(e,t)},t.useReducer=function(e,t,n){return j().useReducer(e,t,n)},t.useRef=function(e){return j().useRef(e)},t.useState=function(e){return j().useState(e)},t.version="16.13.1"},function(e,t,n){"use strict";var r=n(0),i=n(28),a=n(51);function o(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}if(!r)throw Error(o(227));function l(e,t,n,r,i,a,o,l,s){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var s=!1,u=null,c=!1,d=null,f={onError:function(e){s=!0,u=e}};function h(e,t,n,r,i,a,o,c,d){s=!1,u=null,l.apply(f,arguments)}var p=null,g=null,m=null;function v(e,t,n){var r=e.type||"unknown-event";e.currentTarget=m(n),function(e,t,n,r,i,a,l,f,p){if(h.apply(this,arguments),s){if(!s)throw Error(o(198));var g=u;s=!1,u=null,c||(c=!0,d=g)}}(r,t,void 0,e),e.currentTarget=null}var y=null,b={};function x(){if(y)for(var e in b){var t=b[e],n=y.indexOf(e);if(!(-1<n))throw Error(o(96,e));if(!w[n]){if(!t.extractEvents)throw Error(o(97,e));for(var r in w[n]=t,n=t.eventTypes){var i=void 0,a=n[r],l=t,s=r;if(k.hasOwnProperty(s))throw Error(o(99,s));k[s]=a;var u=a.phasedRegistrationNames;if(u){for(i in u)u.hasOwnProperty(i)&&_(u[i],l,s);i=!0}else a.registrationName?(_(a.registrationName,l,s),i=!0):i=!1;if(!i)throw Error(o(98,r,e))}}}}function _(e,t,n){if(S[e])throw Error(o(100,e));S[e]=t,T[e]=t.eventTypes[n].dependencies}var w=[],k={},S={},T={};function M(e){var t,n=!1;for(t in e)if(e.hasOwnProperty(t)){var r=e[t];if(!b.hasOwnProperty(t)||b[t]!==r){if(b[t])throw Error(o(102,t));b[t]=r,n=!0}}n&&x()}var C=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),P=null,D=null,E=null;function O(e){if(e=g(e)){if("function"!==typeof P)throw Error(o(280));var t=e.stateNode;t&&(t=p(t),P(e.stateNode,e.type,t))}}function N(e){D?E?E.push(e):E=[e]:D=e}function I(){if(D){var e=D,t=E;if(E=D=null,O(e),t)for(e=0;e<t.length;e++)O(t[e])}}function A(e,t){return e(t)}function F(e,t,n,r,i){return e(t,n,r,i)}function R(){}var L=A,z=!1,j=!1;function W(){null===D&&null===E||(R(),I())}function V(e,t,n){if(j)return e(t,n);j=!0;try{return L(e,t,n)}finally{j=!1,W()}}var Y=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,B=Object.prototype.hasOwnProperty,H={},U={};function q(e,t,n,r,i,a){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=a}var $={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach((function(e){$[e]=new q(e,0,!1,e,null,!1)})),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach((function(e){var t=e[0];$[t]=new q(t,1,!1,e[1],null,!1)})),["contentEditable","draggable","spellCheck","value"].forEach((function(e){$[e]=new q(e,2,!1,e.toLowerCase(),null,!1)})),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach((function(e){$[e]=new q(e,2,!1,e,null,!1)})),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach((function(e){$[e]=new q(e,3,!1,e.toLowerCase(),null,!1)})),["checked","multiple","muted","selected"].forEach((function(e){$[e]=new q(e,3,!0,e,null,!1)})),["capture","download"].forEach((function(e){$[e]=new q(e,4,!1,e,null,!1)})),["cols","rows","size","span"].forEach((function(e){$[e]=new q(e,6,!1,e,null,!1)})),["rowSpan","start"].forEach((function(e){$[e]=new q(e,5,!1,e.toLowerCase(),null,!1)}));var G=/[\-:]([a-z])/g;function Q(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach((function(e){var t=e.replace(G,Q);$[t]=new q(t,1,!1,e,null,!1)})),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach((function(e){var t=e.replace(G,Q);$[t]=new q(t,1,!1,e,"http://www.w3.org/1999/xlink",!1)})),["xml:base","xml:lang","xml:space"].forEach((function(e){var t=e.replace(G,Q);$[t]=new q(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1)})),["tabIndex","crossOrigin"].forEach((function(e){$[e]=new q(e,1,!1,e.toLowerCase(),null,!1)})),$.xlinkHref=new q("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0),["src","href","action","formAction"].forEach((function(e){$[e]=new q(e,1,!1,e.toLowerCase(),null,!0)}));var K=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;function Z(e,t,n,r){var i=$.hasOwnProperty(t)?$[t]:null;(null!==i?0===i.type:!r&&(2<t.length&&("o"===t[0]||"O"===t[0])&&("n"===t[1]||"N"===t[1])))||(function(e,t,n,r){if(null===t||"undefined"===typeof t||function(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,i,r)&&(n=null),r||null===i?function(e){return!!B.call(U,e)||!B.call(H,e)&&(Y.test(e)?U[e]=!0:(H[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=null===n?3!==i.type&&"":n:(t=i.attributeName,r=i.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(i=i.type)||4===i&&!0===n?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}K.hasOwnProperty("ReactCurrentDispatcher")||(K.ReactCurrentDispatcher={current:null}),K.hasOwnProperty("ReactCurrentBatchConfig")||(K.ReactCurrentBatchConfig={suspense:null});var X=/^(.*)[\\\/]/,J="function"===typeof Symbol&&Symbol.for,ee=J?Symbol.for("react.element"):60103,te=J?Symbol.for("react.portal"):60106,ne=J?Symbol.for("react.fragment"):60107,re=J?Symbol.for("react.strict_mode"):60108,ie=J?Symbol.for("react.profiler"):60114,ae=J?Symbol.for("react.provider"):60109,oe=J?Symbol.for("react.context"):60110,le=J?Symbol.for("react.concurrent_mode"):60111,se=J?Symbol.for("react.forward_ref"):60112,ue=J?Symbol.for("react.suspense"):60113,ce=J?Symbol.for("react.suspense_list"):60120,de=J?Symbol.for("react.memo"):60115,fe=J?Symbol.for("react.lazy"):60116,he=J?Symbol.for("react.block"):60121,pe="function"===typeof Symbol&&Symbol.iterator;function ge(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=pe&&e[pe]||e["@@iterator"])?e:null}function me(e){if(null==e)return null;if("function"===typeof e)return e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case ne:return"Fragment";case te:return"Portal";case ie:return"Profiler";case re:return"StrictMode";case ue:return"Suspense";case ce:return"SuspenseList"}if("object"===typeof e)switch(e.$$typeof){case oe:return"Context.Consumer";case ae:return"Context.Provider";case se:var t=e.render;return t=t.displayName||t.name||"",e.displayName||(""!==t?"ForwardRef("+t+")":"ForwardRef");case de:return me(e.type);case he:return me(e.render);case fe:if(e=1===e._status?e._result:null)return me(e)}return null}function ve(e){var t="";do{e:switch(e.tag){case 3:case 4:case 6:case 7:case 10:case 9:var n="";break e;default:var r=e._debugOwner,i=e._debugSource,a=me(e.type);n=null,r&&(n=me(r.type)),r=a,a="",i?a=" (at "+i.fileName.replace(X,"")+":"+i.lineNumber+")":n&&(a=" (created by "+n+")"),n="\n    in "+(r||"Unknown")+a}t+=n,e=e.return}while(e);return t}function ye(e){switch(typeof e){case"boolean":case"number":case"object":case"string":case"undefined":return e;default:return""}}function be(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function xe(e){e._valueTracker||(e._valueTracker=function(e){var t=be(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var i=n.get,a=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(e){r=""+e,a.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function _e(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=be(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function we(e,t){var n=t.checked;return i({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function ke(e,t){var n=null==t.defaultValue?"":t.defaultValue,r=null!=t.checked?t.checked:t.defaultChecked;n=ye(null!=t.value?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function Se(e,t){null!=(t=t.checked)&&Z(e,"checked",t,!1)}function Te(e,t){Se(e,t);var n=ye(t.value),r=t.type;if(null!=n)"number"===r?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===r||"reset"===r)return void e.removeAttribute("value");t.hasOwnProperty("value")?Ce(e,t.type,n):t.hasOwnProperty("defaultValue")&&Ce(e,t.type,ye(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function Me(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!("submit"!==r&&"reset"!==r||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function Ce(e,t,n){"number"===t&&e.ownerDocument.activeElement===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}function Pe(e,t){return e=i({children:void 0},t),(t=function(e){var t="";return r.Children.forEach(e,(function(e){null!=e&&(t+=e)})),t}(t.children))&&(e.children=t),e}function De(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+ye(n),t=null,i=0;i<e.length;i++){if(e[i].value===n)return e[i].selected=!0,void(r&&(e[i].defaultSelected=!0));null!==t||e[i].disabled||(t=e[i])}null!==t&&(t.selected=!0)}}function Ee(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(o(91));return i({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Oe(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(o(92));if(Array.isArray(n)){if(!(1>=n.length))throw Error(o(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:ye(n)}}function Ne(e,t){var n=ye(t.value),r=ye(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=r&&(e.defaultValue=""+r)}function Ie(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}var Ae="http://www.w3.org/1999/xhtml",Fe="http://www.w3.org/2000/svg";function Re(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Le(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?Re(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var ze,je=function(e){return"undefined"!==typeof MSApp&&MSApp.execUnsafeLocalFunction?function(t,n,r,i){MSApp.execUnsafeLocalFunction((function(){return e(t,n)}))}:e}((function(e,t){if(e.namespaceURI!==Fe||"innerHTML"in e)e.innerHTML=t;else{for((ze=ze||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ze.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}}));function We(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}function Ve(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Ye={animationend:Ve("Animation","AnimationEnd"),animationiteration:Ve("Animation","AnimationIteration"),animationstart:Ve("Animation","AnimationStart"),transitionend:Ve("Transition","TransitionEnd")},Be={},He={};function Ue(e){if(Be[e])return Be[e];if(!Ye[e])return e;var t,n=Ye[e];for(t in n)if(n.hasOwnProperty(t)&&t in He)return Be[e]=n[t];return e}C&&(He=document.createElement("div").style,"AnimationEvent"in window||(delete Ye.animationend.animation,delete Ye.animationiteration.animation,delete Ye.animationstart.animation),"TransitionEvent"in window||delete Ye.transitionend.transition);var qe=Ue("animationend"),$e=Ue("animationiteration"),Ge=Ue("animationstart"),Qe=Ue("transitionend"),Ke="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Ze=new("function"===typeof WeakMap?WeakMap:Map);function Xe(e){var t=Ze.get(e);return void 0===t&&(t=new Map,Ze.set(e,t)),t}function Je(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(1026&(t=e).effectTag)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function et(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function tt(e){if(Je(e)!==e)throw Error(o(188))}function nt(e){if(!(e=function(e){var t=e.alternate;if(!t){if(null===(t=Je(e)))throw Error(o(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(null===i)break;var a=i.alternate;if(null===a){if(null!==(r=i.return)){n=r;continue}break}if(i.child===a.child){for(a=i.child;a;){if(a===n)return tt(i),e;if(a===r)return tt(i),t;a=a.sibling}throw Error(o(188))}if(n.return!==r.return)n=i,r=a;else{for(var l=!1,s=i.child;s;){if(s===n){l=!0,n=i,r=a;break}if(s===r){l=!0,r=i,n=a;break}s=s.sibling}if(!l){for(s=a.child;s;){if(s===n){l=!0,n=a,r=i;break}if(s===r){l=!0,r=a,n=i;break}s=s.sibling}if(!l)throw Error(o(189))}}if(n.alternate!==r)throw Error(o(190))}if(3!==n.tag)throw Error(o(188));return n.stateNode.current===n?e:t}(e)))return null;for(var t=e;;){if(5===t.tag||6===t.tag)return t;if(t.child)t.child.return=t,t=t.child;else{if(t===e)break;for(;!t.sibling;){if(!t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}}return null}function rt(e,t){if(null==t)throw Error(o(30));return null==e?t:Array.isArray(e)?Array.isArray(t)?(e.push.apply(e,t),e):(e.push(t),e):Array.isArray(t)?[e].concat(t):[e,t]}function it(e,t,n){Array.isArray(e)?e.forEach(t,n):e&&t.call(n,e)}var at=null;function ot(e){if(e){var t=e._dispatchListeners,n=e._dispatchInstances;if(Array.isArray(t))for(var r=0;r<t.length&&!e.isPropagationStopped();r++)v(e,t[r],n[r]);else t&&v(e,t,n);e._dispatchListeners=null,e._dispatchInstances=null,e.isPersistent()||e.constructor.release(e)}}function lt(e){if(null!==e&&(at=rt(at,e)),e=at,at=null,e){if(it(e,ot),at)throw Error(o(95));if(c)throw e=d,c=!1,d=null,e}}function st(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}function ut(e){if(!C)return!1;var t=(e="on"+e)in document;return t||((t=document.createElement("div")).setAttribute(e,"return;"),t="function"===typeof t[e]),t}var ct=[];function dt(e){e.topLevelType=null,e.nativeEvent=null,e.targetInst=null,e.ancestors.length=0,10>ct.length&&ct.push(e)}function ft(e,t,n,r){if(ct.length){var i=ct.pop();return i.topLevelType=e,i.eventSystemFlags=r,i.nativeEvent=t,i.targetInst=n,i}return{topLevelType:e,eventSystemFlags:r,nativeEvent:t,targetInst:n,ancestors:[]}}function ht(e){var t=e.targetInst,n=t;do{if(!n){e.ancestors.push(n);break}var r=n;if(3===r.tag)r=r.stateNode.containerInfo;else{for(;r.return;)r=r.return;r=3!==r.tag?null:r.stateNode.containerInfo}if(!r)break;5!==(t=n.tag)&&6!==t||e.ancestors.push(n),n=Cn(r)}while(n);for(n=0;n<e.ancestors.length;n++){t=e.ancestors[n];var i=st(e.nativeEvent);r=e.topLevelType;var a=e.nativeEvent,o=e.eventSystemFlags;0===n&&(o|=64);for(var l=null,s=0;s<w.length;s++){var u=w[s];u&&(u=u.extractEvents(r,t,a,i,o))&&(l=rt(l,u))}lt(l)}}function pt(e,t,n){if(!n.has(e)){switch(e){case"scroll":Gt(t,"scroll",!0);break;case"focus":case"blur":Gt(t,"focus",!0),Gt(t,"blur",!0),n.set("blur",null),n.set("focus",null);break;case"cancel":case"close":ut(e)&&Gt(t,e,!0);break;case"invalid":case"submit":case"reset":break;default:-1===Ke.indexOf(e)&&$t(e,t)}n.set(e,null)}}var gt,mt,vt,yt=!1,bt=[],xt=null,_t=null,wt=null,kt=new Map,St=new Map,Tt=[],Mt="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput close cancel copy cut paste click change contextmenu reset submit".split(" "),Ct="focus blur dragenter dragleave mouseover mouseout pointerover pointerout gotpointercapture lostpointercapture".split(" ");function Pt(e,t,n,r,i){return{blockedOn:e,topLevelType:t,eventSystemFlags:32|n,nativeEvent:i,container:r}}function Dt(e,t){switch(e){case"focus":case"blur":xt=null;break;case"dragenter":case"dragleave":_t=null;break;case"mouseover":case"mouseout":wt=null;break;case"pointerover":case"pointerout":kt.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":St.delete(t.pointerId)}}function Et(e,t,n,r,i,a){return null===e||e.nativeEvent!==a?(e=Pt(t,n,r,i,a),null!==t&&(null!==(t=Pn(t))&&mt(t)),e):(e.eventSystemFlags|=r,e)}function Ot(e){var t=Cn(e.target);if(null!==t){var n=Je(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=et(n)))return e.blockedOn=t,void a.unstable_runWithPriority(e.priority,(function(){vt(n)}))}else if(3===t&&n.stateNode.hydrate)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function Nt(e){if(null!==e.blockedOn)return!1;var t=Xt(e.topLevelType,e.eventSystemFlags,e.container,e.nativeEvent);if(null!==t){var n=Pn(t);return null!==n&&mt(n),e.blockedOn=t,!1}return!0}function It(e,t,n){Nt(e)&&n.delete(t)}function At(){for(yt=!1;0<bt.length;){var e=bt[0];if(null!==e.blockedOn){null!==(e=Pn(e.blockedOn))&&gt(e);break}var t=Xt(e.topLevelType,e.eventSystemFlags,e.container,e.nativeEvent);null!==t?e.blockedOn=t:bt.shift()}null!==xt&&Nt(xt)&&(xt=null),null!==_t&&Nt(_t)&&(_t=null),null!==wt&&Nt(wt)&&(wt=null),kt.forEach(It),St.forEach(It)}function Ft(e,t){e.blockedOn===t&&(e.blockedOn=null,yt||(yt=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,At)))}function Rt(e){function t(t){return Ft(t,e)}if(0<bt.length){Ft(bt[0],e);for(var n=1;n<bt.length;n++){var r=bt[n];r.blockedOn===e&&(r.blockedOn=null)}}for(null!==xt&&Ft(xt,e),null!==_t&&Ft(_t,e),null!==wt&&Ft(wt,e),kt.forEach(t),St.forEach(t),n=0;n<Tt.length;n++)(r=Tt[n]).blockedOn===e&&(r.blockedOn=null);for(;0<Tt.length&&null===(n=Tt[0]).blockedOn;)Ot(n),null===n.blockedOn&&Tt.shift()}var Lt={},zt=new Map,jt=new Map,Wt=["abort","abort",qe,"animationEnd",$e,"animationIteration",Ge,"animationStart","canplay","canPlay","canplaythrough","canPlayThrough","durationchange","durationChange","emptied","emptied","encrypted","encrypted","ended","ended","error","error","gotpointercapture","gotPointerCapture","load","load","loadeddata","loadedData","loadedmetadata","loadedMetadata","loadstart","loadStart","lostpointercapture","lostPointerCapture","playing","playing","progress","progress","seeking","seeking","stalled","stalled","suspend","suspend","timeupdate","timeUpdate",Qe,"transitionEnd","waiting","waiting"];function Vt(e,t){for(var n=0;n<e.length;n+=2){var r=e[n],i=e[n+1],a="on"+(i[0].toUpperCase()+i.slice(1));a={phasedRegistrationNames:{bubbled:a,captured:a+"Capture"},dependencies:[r],eventPriority:t},jt.set(r,t),zt.set(r,a),Lt[i]=a}}Vt("blur blur cancel cancel click click close close contextmenu contextMenu copy copy cut cut auxclick auxClick dblclick doubleClick dragend dragEnd dragstart dragStart drop drop focus focus input input invalid invalid keydown keyDown keypress keyPress keyup keyUp mousedown mouseDown mouseup mouseUp paste paste pause pause play play pointercancel pointerCancel pointerdown pointerDown pointerup pointerUp ratechange rateChange reset reset seeked seeked submit submit touchcancel touchCancel touchend touchEnd touchstart touchStart volumechange volumeChange".split(" "),0),Vt("drag drag dragenter dragEnter dragexit dragExit dragleave dragLeave dragover dragOver mousemove mouseMove mouseout mouseOut mouseover mouseOver pointermove pointerMove pointerout pointerOut pointerover pointerOver scroll scroll toggle toggle touchmove touchMove wheel wheel".split(" "),1),Vt(Wt,2);for(var Yt="change selectionchange textInput compositionstart compositionend compositionupdate".split(" "),Bt=0;Bt<Yt.length;Bt++)jt.set(Yt[Bt],0);var Ht=a.unstable_UserBlockingPriority,Ut=a.unstable_runWithPriority,qt=!0;function $t(e,t){Gt(t,e,!1)}function Gt(e,t,n){var r=jt.get(t);switch(void 0===r?2:r){case 0:r=Qt.bind(null,t,1,e);break;case 1:r=Kt.bind(null,t,1,e);break;default:r=Zt.bind(null,t,1,e)}n?e.addEventListener(t,r,!0):e.addEventListener(t,r,!1)}function Qt(e,t,n,r){z||R();var i=Zt,a=z;z=!0;try{F(i,e,t,n,r)}finally{(z=a)||W()}}function Kt(e,t,n,r){Ut(Ht,Zt.bind(null,e,t,n,r))}function Zt(e,t,n,r){if(qt)if(0<bt.length&&-1<Mt.indexOf(e))e=Pt(null,e,t,n,r),bt.push(e);else{var i=Xt(e,t,n,r);if(null===i)Dt(e,r);else if(-1<Mt.indexOf(e))e=Pt(i,e,t,n,r),bt.push(e);else if(!function(e,t,n,r,i){switch(t){case"focus":return xt=Et(xt,e,t,n,r,i),!0;case"dragenter":return _t=Et(_t,e,t,n,r,i),!0;case"mouseover":return wt=Et(wt,e,t,n,r,i),!0;case"pointerover":var a=i.pointerId;return kt.set(a,Et(kt.get(a)||null,e,t,n,r,i)),!0;case"gotpointercapture":return a=i.pointerId,St.set(a,Et(St.get(a)||null,e,t,n,r,i)),!0}return!1}(i,e,t,n,r)){Dt(e,r),e=ft(e,r,null,t);try{V(ht,e)}finally{dt(e)}}}}function Xt(e,t,n,r){if(null!==(n=Cn(n=st(r)))){var i=Je(n);if(null===i)n=null;else{var a=i.tag;if(13===a){if(null!==(n=et(i)))return n;n=null}else if(3===a){if(i.stateNode.hydrate)return 3===i.tag?i.stateNode.containerInfo:null;n=null}else i!==n&&(n=null)}}e=ft(e,r,n,t);try{V(ht,e)}finally{dt(e)}return null}var Jt={animationIterationCount:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},en=["Webkit","ms","Moz","O"];function tn(e,t,n){return null==t||"boolean"===typeof t||""===t?"":n||"number"!==typeof t||0===t||Jt.hasOwnProperty(e)&&Jt[e]?(""+t).trim():t+"px"}function nn(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),i=tn(n,t[n],r);"float"===n&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}Object.keys(Jt).forEach((function(e){en.forEach((function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Jt[t]=Jt[e]}))}));var rn=i({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function an(e,t){if(t){if(rn[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(o(137,e,""));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(o(60));if("object"!==typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(o(61))}if(null!=t.style&&"object"!==typeof t.style)throw Error(o(62,""))}}function on(e,t){if(-1===e.indexOf("-"))return"string"===typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var ln=Ae;function sn(e,t){var n=Xe(e=9===e.nodeType||11===e.nodeType?e:e.ownerDocument);t=T[t];for(var r=0;r<t.length;r++)pt(t[r],e,n)}function un(){}function cn(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function dn(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function fn(e,t){var n,r=dn(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=dn(r)}}function hn(){for(var e=window,t=cn();t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=cn((e=t.contentWindow).document)}return t}function pn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}var gn=null,mn=null;function vn(e,t){switch(e){case"button":case"input":case"select":case"textarea":return!!t.autoFocus}return!1}function yn(e,t){return"textarea"===e||"option"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var bn="function"===typeof setTimeout?setTimeout:void 0,xn="function"===typeof clearTimeout?clearTimeout:void 0;function _n(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break}return e}function wn(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var kn=Math.random().toString(36).slice(2),Sn="__reactInternalInstance$"+kn,Tn="__reactEventHandlers$"+kn,Mn="__reactContainere$"+kn;function Cn(e){var t=e[Sn];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Mn]||n[Sn]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=wn(e);null!==e;){if(n=e[Sn])return n;e=wn(e)}return t}n=(e=n).parentNode}return null}function Pn(e){return!(e=e[Sn]||e[Mn])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function Dn(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(o(33))}function En(e){return e[Tn]||null}function On(e){do{e=e.return}while(e&&5!==e.tag);return e||null}function Nn(e,t){var n=e.stateNode;if(!n)return null;var r=p(n);if(!r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(o(231,t,typeof n));return n}function In(e,t,n){(t=Nn(e,n.dispatchConfig.phasedRegistrationNames[t]))&&(n._dispatchListeners=rt(n._dispatchListeners,t),n._dispatchInstances=rt(n._dispatchInstances,e))}function An(e){if(e&&e.dispatchConfig.phasedRegistrationNames){for(var t=e._targetInst,n=[];t;)n.push(t),t=On(t);for(t=n.length;0<t--;)In(n[t],"captured",e);for(t=0;t<n.length;t++)In(n[t],"bubbled",e)}}function Fn(e,t,n){e&&n&&n.dispatchConfig.registrationName&&(t=Nn(e,n.dispatchConfig.registrationName))&&(n._dispatchListeners=rt(n._dispatchListeners,t),n._dispatchInstances=rt(n._dispatchInstances,e))}function Rn(e){e&&e.dispatchConfig.registrationName&&Fn(e._targetInst,null,e)}function Ln(e){it(e,An)}var zn=null,jn=null,Wn=null;function Vn(){if(Wn)return Wn;var e,t,n=jn,r=n.length,i="value"in zn?zn.value:zn.textContent,a=i.length;for(e=0;e<r&&n[e]===i[e];e++);var o=r-e;for(t=1;t<=o&&n[r-t]===i[a-t];t++);return Wn=i.slice(e,1<t?1-t:void 0)}function Yn(){return!0}function Bn(){return!1}function Hn(e,t,n,r){for(var i in this.dispatchConfig=e,this._targetInst=t,this.nativeEvent=n,e=this.constructor.Interface)e.hasOwnProperty(i)&&((t=e[i])?this[i]=t(n):"target"===i?this.target=r:this[i]=n[i]);return this.isDefaultPrevented=(null!=n.defaultPrevented?n.defaultPrevented:!1===n.returnValue)?Yn:Bn,this.isPropagationStopped=Bn,this}function Un(e,t,n,r){if(this.eventPool.length){var i=this.eventPool.pop();return this.call(i,e,t,n,r),i}return new this(e,t,n,r)}function qn(e){if(!(e instanceof this))throw Error(o(279));e.destructor(),10>this.eventPool.length&&this.eventPool.push(e)}function $n(e){e.eventPool=[],e.getPooled=Un,e.release=qn}i(Hn.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=Yn)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=Yn)},persist:function(){this.isPersistent=Yn},isPersistent:Bn,destructor:function(){var e,t=this.constructor.Interface;for(e in t)this[e]=null;this.nativeEvent=this._targetInst=this.dispatchConfig=null,this.isPropagationStopped=this.isDefaultPrevented=Bn,this._dispatchInstances=this._dispatchListeners=null}}),Hn.Interface={type:null,target:null,currentTarget:function(){return null},eventPhase:null,bubbles:null,cancelable:null,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:null,isTrusted:null},Hn.extend=function(e){function t(){}function n(){return r.apply(this,arguments)}var r=this;t.prototype=r.prototype;var a=new t;return i(a,n.prototype),n.prototype=a,n.prototype.constructor=n,n.Interface=i({},r.Interface,e),n.extend=r.extend,$n(n),n},$n(Hn);var Gn=Hn.extend({data:null}),Qn=Hn.extend({data:null}),Kn=[9,13,27,32],Zn=C&&"CompositionEvent"in window,Xn=null;C&&"documentMode"in document&&(Xn=document.documentMode);var Jn=C&&"TextEvent"in window&&!Xn,er=C&&(!Zn||Xn&&8<Xn&&11>=Xn),tr=String.fromCharCode(32),nr={beforeInput:{phasedRegistrationNames:{bubbled:"onBeforeInput",captured:"onBeforeInputCapture"},dependencies:["compositionend","keypress","textInput","paste"]},compositionEnd:{phasedRegistrationNames:{bubbled:"onCompositionEnd",captured:"onCompositionEndCapture"},dependencies:"blur compositionend keydown keypress keyup mousedown".split(" ")},compositionStart:{phasedRegistrationNames:{bubbled:"onCompositionStart",captured:"onCompositionStartCapture"},dependencies:"blur compositionstart keydown keypress keyup mousedown".split(" ")},compositionUpdate:{phasedRegistrationNames:{bubbled:"onCompositionUpdate",captured:"onCompositionUpdateCapture"},dependencies:"blur compositionupdate keydown keypress keyup mousedown".split(" ")}},rr=!1;function ir(e,t){switch(e){case"keyup":return-1!==Kn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"blur":return!0;default:return!1}}function ar(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var or=!1;var lr={eventTypes:nr,extractEvents:function(e,t,n,r){var i;if(Zn)e:{switch(e){case"compositionstart":var a=nr.compositionStart;break e;case"compositionend":a=nr.compositionEnd;break e;case"compositionupdate":a=nr.compositionUpdate;break e}a=void 0}else or?ir(e,n)&&(a=nr.compositionEnd):"keydown"===e&&229===n.keyCode&&(a=nr.compositionStart);return a?(er&&"ko"!==n.locale&&(or||a!==nr.compositionStart?a===nr.compositionEnd&&or&&(i=Vn()):(jn="value"in(zn=r)?zn.value:zn.textContent,or=!0)),a=Gn.getPooled(a,t,n,r),i?a.data=i:null!==(i=ar(n))&&(a.data=i),Ln(a),i=a):i=null,(e=Jn?function(e,t){switch(e){case"compositionend":return ar(t);case"keypress":return 32!==t.which?null:(rr=!0,tr);case"textInput":return(e=t.data)===tr&&rr?null:e;default:return null}}(e,n):function(e,t){if(or)return"compositionend"===e||!Zn&&ir(e,t)?(e=Vn(),Wn=jn=zn=null,or=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return er&&"ko"!==t.locale?null:t.data;default:return null}}(e,n))?((t=Qn.getPooled(nr.beforeInput,t,n,r)).data=e,Ln(t)):t=null,null===i?t:null===t?i:[i,t]}},sr={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function ur(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!sr[e.type]:"textarea"===t}var cr={change:{phasedRegistrationNames:{bubbled:"onChange",captured:"onChangeCapture"},dependencies:"blur change click focus input keydown keyup selectionchange".split(" ")}};function dr(e,t,n){return(e=Hn.getPooled(cr.change,e,t,n)).type="change",N(n),Ln(e),e}var fr=null,hr=null;function pr(e){lt(e)}function gr(e){if(_e(Dn(e)))return e}function mr(e,t){if("change"===e)return t}var vr=!1;function yr(){fr&&(fr.detachEvent("onpropertychange",br),hr=fr=null)}function br(e){if("value"===e.propertyName&&gr(hr))if(e=dr(hr,e,st(e)),z)lt(e);else{z=!0;try{A(pr,e)}finally{z=!1,W()}}}function xr(e,t,n){"focus"===e?(yr(),hr=n,(fr=t).attachEvent("onpropertychange",br)):"blur"===e&&yr()}function _r(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return gr(hr)}function wr(e,t){if("click"===e)return gr(t)}function kr(e,t){if("input"===e||"change"===e)return gr(t)}C&&(vr=ut("input")&&(!document.documentMode||9<document.documentMode));var Sr={eventTypes:cr,_isInputEventSupported:vr,extractEvents:function(e,t,n,r){var i=t?Dn(t):window,a=i.nodeName&&i.nodeName.toLowerCase();if("select"===a||"input"===a&&"file"===i.type)var o=mr;else if(ur(i))if(vr)o=kr;else{o=_r;var l=xr}else(a=i.nodeName)&&"input"===a.toLowerCase()&&("checkbox"===i.type||"radio"===i.type)&&(o=wr);if(o&&(o=o(e,t)))return dr(o,n,r);l&&l(e,i,t),"blur"===e&&(e=i._wrapperState)&&e.controlled&&"number"===i.type&&Ce(i,"number",i.value)}},Tr=Hn.extend({view:null,detail:null}),Mr={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Cr(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=Mr[e])&&!!t[e]}function Pr(){return Cr}var Dr=0,Er=0,Or=!1,Nr=!1,Ir=Tr.extend({screenX:null,screenY:null,clientX:null,clientY:null,pageX:null,pageY:null,ctrlKey:null,shiftKey:null,altKey:null,metaKey:null,getModifierState:Pr,button:null,buttons:null,relatedTarget:function(e){return e.relatedTarget||(e.fromElement===e.srcElement?e.toElement:e.fromElement)},movementX:function(e){if("movementX"in e)return e.movementX;var t=Dr;return Dr=e.screenX,Or?"mousemove"===e.type?e.screenX-t:0:(Or=!0,0)},movementY:function(e){if("movementY"in e)return e.movementY;var t=Er;return Er=e.screenY,Nr?"mousemove"===e.type?e.screenY-t:0:(Nr=!0,0)}}),Ar=Ir.extend({pointerId:null,width:null,height:null,pressure:null,tangentialPressure:null,tiltX:null,tiltY:null,twist:null,pointerType:null,isPrimary:null}),Fr={mouseEnter:{registrationName:"onMouseEnter",dependencies:["mouseout","mouseover"]},mouseLeave:{registrationName:"onMouseLeave",dependencies:["mouseout","mouseover"]},pointerEnter:{registrationName:"onPointerEnter",dependencies:["pointerout","pointerover"]},pointerLeave:{registrationName:"onPointerLeave",dependencies:["pointerout","pointerover"]}},Rr={eventTypes:Fr,extractEvents:function(e,t,n,r,i){var a="mouseover"===e||"pointerover"===e,o="mouseout"===e||"pointerout"===e;if(a&&0===(32&i)&&(n.relatedTarget||n.fromElement)||!o&&!a)return null;(a=r.window===r?r:(a=r.ownerDocument)?a.defaultView||a.parentWindow:window,o)?(o=t,null!==(t=(t=n.relatedTarget||n.toElement)?Cn(t):null)&&(t!==Je(t)||5!==t.tag&&6!==t.tag)&&(t=null)):o=null;if(o===t)return null;if("mouseout"===e||"mouseover"===e)var l=Ir,s=Fr.mouseLeave,u=Fr.mouseEnter,c="mouse";else"pointerout"!==e&&"pointerover"!==e||(l=Ar,s=Fr.pointerLeave,u=Fr.pointerEnter,c="pointer");if(e=null==o?a:Dn(o),a=null==t?a:Dn(t),(s=l.getPooled(s,o,n,r)).type=c+"leave",s.target=e,s.relatedTarget=a,(n=l.getPooled(u,t,n,r)).type=c+"enter",n.target=a,n.relatedTarget=e,c=t,(r=o)&&c)e:{for(u=c,o=0,e=l=r;e;e=On(e))o++;for(e=0,t=u;t;t=On(t))e++;for(;0<o-e;)l=On(l),o--;for(;0<e-o;)u=On(u),e--;for(;o--;){if(l===u||l===u.alternate)break e;l=On(l),u=On(u)}l=null}else l=null;for(u=l,l=[];r&&r!==u&&(null===(o=r.alternate)||o!==u);)l.push(r),r=On(r);for(r=[];c&&c!==u&&(null===(o=c.alternate)||o!==u);)r.push(c),c=On(c);for(c=0;c<l.length;c++)Fn(l[c],"bubbled",s);for(c=r.length;0<c--;)Fn(r[c],"captured",n);return 0===(64&i)?[s]:[s,n]}};var Lr="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t},zr=Object.prototype.hasOwnProperty;function jr(e,t){if(Lr(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++)if(!zr.call(t,n[r])||!Lr(e[n[r]],t[n[r]]))return!1;return!0}var Wr=C&&"documentMode"in document&&11>=document.documentMode,Vr={select:{phasedRegistrationNames:{bubbled:"onSelect",captured:"onSelectCapture"},dependencies:"blur contextmenu dragend focus keydown keyup mousedown mouseup selectionchange".split(" ")}},Yr=null,Br=null,Hr=null,Ur=!1;function qr(e,t){var n=t.window===t?t.document:9===t.nodeType?t:t.ownerDocument;return Ur||null==Yr||Yr!==cn(n)?null:("selectionStart"in(n=Yr)&&pn(n)?n={start:n.selectionStart,end:n.selectionEnd}:n={anchorNode:(n=(n.ownerDocument&&n.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:n.anchorOffset,focusNode:n.focusNode,focusOffset:n.focusOffset},Hr&&jr(Hr,n)?null:(Hr=n,(e=Hn.getPooled(Vr.select,Br,e,t)).type="select",e.target=Yr,Ln(e),e))}var $r={eventTypes:Vr,extractEvents:function(e,t,n,r,i,a){if(!(a=!(i=a||(r.window===r?r.document:9===r.nodeType?r:r.ownerDocument)))){e:{i=Xe(i),a=T.onSelect;for(var o=0;o<a.length;o++)if(!i.has(a[o])){i=!1;break e}i=!0}a=!i}if(a)return null;switch(i=t?Dn(t):window,e){case"focus":(ur(i)||"true"===i.contentEditable)&&(Yr=i,Br=t,Hr=null);break;case"blur":Hr=Br=Yr=null;break;case"mousedown":Ur=!0;break;case"contextmenu":case"mouseup":case"dragend":return Ur=!1,qr(n,r);case"selectionchange":if(Wr)break;case"keydown":case"keyup":return qr(n,r)}return null}},Gr=Hn.extend({animationName:null,elapsedTime:null,pseudoElement:null}),Qr=Hn.extend({clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Kr=Tr.extend({relatedTarget:null});function Zr(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}var Xr={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Jr={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},ei=Tr.extend({key:function(e){if(e.key){var t=Xr[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=Zr(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?Jr[e.keyCode]||"Unidentified":""},location:null,ctrlKey:null,shiftKey:null,altKey:null,metaKey:null,repeat:null,locale:null,getModifierState:Pr,charCode:function(e){return"keypress"===e.type?Zr(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?Zr(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),ti=Ir.extend({dataTransfer:null}),ni=Tr.extend({touches:null,targetTouches:null,changedTouches:null,altKey:null,metaKey:null,ctrlKey:null,shiftKey:null,getModifierState:Pr}),ri=Hn.extend({propertyName:null,elapsedTime:null,pseudoElement:null}),ii=Ir.extend({deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:null,deltaMode:null}),ai={eventTypes:Lt,extractEvents:function(e,t,n,r){var i=zt.get(e);if(!i)return null;switch(e){case"keypress":if(0===Zr(n))return null;case"keydown":case"keyup":e=ei;break;case"blur":case"focus":e=Kr;break;case"click":if(2===n.button)return null;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":e=Ir;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":e=ti;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":e=ni;break;case qe:case $e:case Ge:e=Gr;break;case Qe:e=ri;break;case"scroll":e=Tr;break;case"wheel":e=ii;break;case"copy":case"cut":case"paste":e=Qr;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":e=Ar;break;default:e=Hn}return Ln(t=e.getPooled(i,t,n,r)),t}};if(y)throw Error(o(101));y=Array.prototype.slice.call("ResponderEventPlugin SimpleEventPlugin EnterLeaveEventPlugin ChangeEventPlugin SelectEventPlugin BeforeInputEventPlugin".split(" ")),x(),p=En,g=Pn,m=Dn,M({SimpleEventPlugin:ai,EnterLeaveEventPlugin:Rr,ChangeEventPlugin:Sr,SelectEventPlugin:$r,BeforeInputEventPlugin:lr});var oi=[],li=-1;function si(e){0>li||(e.current=oi[li],oi[li]=null,li--)}function ui(e,t){li++,oi[li]=e.current,e.current=t}var ci={},di={current:ci},fi={current:!1},hi=ci;function pi(e,t){var n=e.type.contextTypes;if(!n)return ci;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i,a={};for(i in n)a[i]=t[i];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=a),a}function gi(e){return null!==(e=e.childContextTypes)&&void 0!==e}function mi(){si(fi),si(di)}function vi(e,t,n){if(di.current!==ci)throw Error(o(168));ui(di,t),ui(fi,n)}function yi(e,t,n){var r=e.stateNode;if(e=t.childContextTypes,"function"!==typeof r.getChildContext)return n;for(var a in r=r.getChildContext())if(!(a in e))throw Error(o(108,me(t)||"Unknown",a));return i({},n,{},r)}function bi(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||ci,hi=di.current,ui(di,e),ui(fi,fi.current),!0}function xi(e,t,n){var r=e.stateNode;if(!r)throw Error(o(169));n?(e=yi(e,t,hi),r.__reactInternalMemoizedMergedChildContext=e,si(fi),si(di),ui(di,e)):si(fi),ui(fi,n)}var _i=a.unstable_runWithPriority,wi=a.unstable_scheduleCallback,ki=a.unstable_cancelCallback,Si=a.unstable_requestPaint,Ti=a.unstable_now,Mi=a.unstable_getCurrentPriorityLevel,Ci=a.unstable_ImmediatePriority,Pi=a.unstable_UserBlockingPriority,Di=a.unstable_NormalPriority,Ei=a.unstable_LowPriority,Oi=a.unstable_IdlePriority,Ni={},Ii=a.unstable_shouldYield,Ai=void 0!==Si?Si:function(){},Fi=null,Ri=null,Li=!1,zi=Ti(),ji=1e4>zi?Ti:function(){return Ti()-zi};function Wi(){switch(Mi()){case Ci:return 99;case Pi:return 98;case Di:return 97;case Ei:return 96;case Oi:return 95;default:throw Error(o(332))}}function Vi(e){switch(e){case 99:return Ci;case 98:return Pi;case 97:return Di;case 96:return Ei;case 95:return Oi;default:throw Error(o(332))}}function Yi(e,t){return e=Vi(e),_i(e,t)}function Bi(e,t,n){return e=Vi(e),wi(e,t,n)}function Hi(e){return null===Fi?(Fi=[e],Ri=wi(Ci,qi)):Fi.push(e),Ni}function Ui(){if(null!==Ri){var e=Ri;Ri=null,ki(e)}qi()}function qi(){if(!Li&&null!==Fi){Li=!0;var e=0;try{var t=Fi;Yi(99,(function(){for(;e<t.length;e++){var n=t[e];do{n=n(!0)}while(null!==n)}})),Fi=null}catch(n){throw null!==Fi&&(Fi=Fi.slice(e+1)),wi(Ci,Ui),n}finally{Li=!1}}}function $i(e,t,n){return 1073741821-(1+((1073741821-e+t/10)/(n/=10)|0))*n}function Gi(e,t){if(e&&e.defaultProps)for(var n in t=i({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}var Qi={current:null},Ki=null,Zi=null,Xi=null;function Ji(){Xi=Zi=Ki=null}function ea(e){var t=Qi.current;si(Qi),e.type._context._currentValue=t}function ta(e,t){for(;null!==e;){var n=e.alternate;if(e.childExpirationTime<t)e.childExpirationTime=t,null!==n&&n.childExpirationTime<t&&(n.childExpirationTime=t);else{if(!(null!==n&&n.childExpirationTime<t))break;n.childExpirationTime=t}e=e.return}}function na(e,t){Ki=e,Xi=Zi=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(e.expirationTime>=t&&(Eo=!0),e.firstContext=null)}function ra(e,t){if(Xi!==e&&!1!==t&&0!==t)if("number"===typeof t&&1073741823!==t||(Xi=e,t=1073741823),t={context:e,observedBits:t,next:null},null===Zi){if(null===Ki)throw Error(o(308));Zi=t,Ki.dependencies={expirationTime:0,firstContext:t,responders:null}}else Zi=Zi.next=t;return e._currentValue}var ia=!1;function aa(e){e.updateQueue={baseState:e.memoizedState,baseQueue:null,shared:{pending:null},effects:null}}function oa(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,baseQueue:e.baseQueue,shared:e.shared,effects:e.effects})}function la(e,t){return(e={expirationTime:e,suspenseConfig:t,tag:0,payload:null,callback:null,next:null}).next=e}function sa(e,t){if(null!==(e=e.updateQueue)){var n=(e=e.shared).pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}}function ua(e,t){var n=e.alternate;null!==n&&oa(n,e),null===(n=(e=e.updateQueue).baseQueue)?(e.baseQueue=t.next=t,t.next=t):(t.next=n.next,n.next=t)}function ca(e,t,n,r){var a=e.updateQueue;ia=!1;var o=a.baseQueue,l=a.shared.pending;if(null!==l){if(null!==o){var s=o.next;o.next=l.next,l.next=s}o=l,a.shared.pending=null,null!==(s=e.alternate)&&(null!==(s=s.updateQueue)&&(s.baseQueue=l))}if(null!==o){s=o.next;var u=a.baseState,c=0,d=null,f=null,h=null;if(null!==s)for(var p=s;;){if((l=p.expirationTime)<r){var g={expirationTime:p.expirationTime,suspenseConfig:p.suspenseConfig,tag:p.tag,payload:p.payload,callback:p.callback,next:null};null===h?(f=h=g,d=u):h=h.next=g,l>c&&(c=l)}else{null!==h&&(h=h.next={expirationTime:1073741823,suspenseConfig:p.suspenseConfig,tag:p.tag,payload:p.payload,callback:p.callback,next:null}),as(l,p.suspenseConfig);e:{var m=e,v=p;switch(l=t,g=n,v.tag){case 1:if("function"===typeof(m=v.payload)){u=m.call(g,u,l);break e}u=m;break e;case 3:m.effectTag=-4097&m.effectTag|64;case 0:if(null===(l="function"===typeof(m=v.payload)?m.call(g,u,l):m)||void 0===l)break e;u=i({},u,l);break e;case 2:ia=!0}}null!==p.callback&&(e.effectTag|=32,null===(l=a.effects)?a.effects=[p]:l.push(p))}if(null===(p=p.next)||p===s){if(null===(l=a.shared.pending))break;p=o.next=l.next,l.next=s,a.baseQueue=o=l,a.shared.pending=null}}null===h?d=u:h.next=f,a.baseState=d,a.baseQueue=h,os(c),e.expirationTime=c,e.memoizedState=u}}function da(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(null!==i){if(r.callback=null,r=i,i=n,"function"!==typeof r)throw Error(o(191,r));r.call(i)}}}var fa=K.ReactCurrentBatchConfig,ha=(new r.Component).refs;function pa(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:i({},t,n),e.memoizedState=n,0===e.expirationTime&&(e.updateQueue.baseState=n)}var ga={isMounted:function(e){return!!(e=e._reactInternalFiber)&&Je(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternalFiber;var r=ql(),i=fa.suspense;(i=la(r=$l(r,e,i),i)).payload=t,void 0!==n&&null!==n&&(i.callback=n),sa(e,i),Gl(e,r)},enqueueReplaceState:function(e,t,n){e=e._reactInternalFiber;var r=ql(),i=fa.suspense;(i=la(r=$l(r,e,i),i)).tag=1,i.payload=t,void 0!==n&&null!==n&&(i.callback=n),sa(e,i),Gl(e,r)},enqueueForceUpdate:function(e,t){e=e._reactInternalFiber;var n=ql(),r=fa.suspense;(r=la(n=$l(n,e,r),r)).tag=2,void 0!==t&&null!==t&&(r.callback=t),sa(e,r),Gl(e,n)}};function ma(e,t,n,r,i,a,o){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,a,o):!t.prototype||!t.prototype.isPureReactComponent||(!jr(n,r)||!jr(i,a))}function va(e,t,n){var r=!1,i=ci,a=t.contextType;return"object"===typeof a&&null!==a?a=ra(a):(i=gi(t)?hi:di.current,a=(r=null!==(r=t.contextTypes)&&void 0!==r)?pi(e,i):ci),t=new t(n,a),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=ga,e.stateNode=t,t._reactInternalFiber=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=a),t}function ya(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&ga.enqueueReplaceState(t,t.state,null)}function ba(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs=ha,aa(e);var a=t.contextType;"object"===typeof a&&null!==a?i.context=ra(a):(a=gi(t)?hi:di.current,i.context=pi(e,a)),ca(e,n,i,r),i.state=e.memoizedState,"function"===typeof(a=t.getDerivedStateFromProps)&&(pa(e,t,a,n),i.state=e.memoizedState),"function"===typeof t.getDerivedStateFromProps||"function"===typeof i.getSnapshotBeforeUpdate||"function"!==typeof i.UNSAFE_componentWillMount&&"function"!==typeof i.componentWillMount||(t=i.state,"function"===typeof i.componentWillMount&&i.componentWillMount(),"function"===typeof i.UNSAFE_componentWillMount&&i.UNSAFE_componentWillMount(),t!==i.state&&ga.enqueueReplaceState(i,i.state,null),ca(e,n,i,r),i.state=e.memoizedState),"function"===typeof i.componentDidMount&&(e.effectTag|=4)}var xa=Array.isArray;function _a(e,t,n){if(null!==(e=n.ref)&&"function"!==typeof e&&"object"!==typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(o(309));var r=n.stateNode}if(!r)throw Error(o(147,e));var i=""+e;return null!==t&&null!==t.ref&&"function"===typeof t.ref&&t.ref._stringRef===i?t.ref:((t=function(e){var t=r.refs;t===ha&&(t=r.refs={}),null===e?delete t[i]:t[i]=e})._stringRef=i,t)}if("string"!==typeof e)throw Error(o(284));if(!n._owner)throw Error(o(290,e))}return e}function wa(e,t){if("textarea"!==e.type)throw Error(o(31,"[object Object]"===Object.prototype.toString.call(t)?"object with keys {"+Object.keys(t).join(", ")+"}":t,""))}function ka(e){function t(t,n){if(e){var r=t.lastEffect;null!==r?(r.nextEffect=n,t.lastEffect=n):t.firstEffect=t.lastEffect=n,n.nextEffect=null,n.effectTag=8}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function i(e,t){return(e=Ms(e,t)).index=0,e.sibling=null,e}function a(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.effectTag=2,n):r:(t.effectTag=2,n):n}function l(t){return e&&null===t.alternate&&(t.effectTag=2),t}function s(e,t,n,r){return null===t||6!==t.tag?((t=Ds(n,e.mode,r)).return=e,t):((t=i(t,n)).return=e,t)}function u(e,t,n,r){return null!==t&&t.elementType===n.type?((r=i(t,n.props)).ref=_a(e,t,n),r.return=e,r):((r=Cs(n.type,n.key,n.props,null,e.mode,r)).ref=_a(e,t,n),r.return=e,r)}function c(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Es(n,e.mode,r)).return=e,t):((t=i(t,n.children||[])).return=e,t)}function d(e,t,n,r,a){return null===t||7!==t.tag?((t=Ps(n,e.mode,r,a)).return=e,t):((t=i(t,n)).return=e,t)}function f(e,t,n){if("string"===typeof t||"number"===typeof t)return(t=Ds(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case ee:return(n=Cs(t.type,t.key,t.props,null,e.mode,n)).ref=_a(e,null,t),n.return=e,n;case te:return(t=Es(t,e.mode,n)).return=e,t}if(xa(t)||ge(t))return(t=Ps(t,e.mode,n,null)).return=e,t;wa(e,t)}return null}function h(e,t,n,r){var i=null!==t?t.key:null;if("string"===typeof n||"number"===typeof n)return null!==i?null:s(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case ee:return n.key===i?n.type===ne?d(e,t,n.props.children,r,i):u(e,t,n,r):null;case te:return n.key===i?c(e,t,n,r):null}if(xa(n)||ge(n))return null!==i?null:d(e,t,n,r,null);wa(e,n)}return null}function p(e,t,n,r,i){if("string"===typeof r||"number"===typeof r)return s(t,e=e.get(n)||null,""+r,i);if("object"===typeof r&&null!==r){switch(r.$$typeof){case ee:return e=e.get(null===r.key?n:r.key)||null,r.type===ne?d(t,e,r.props.children,i,r.key):u(t,e,r,i);case te:return c(t,e=e.get(null===r.key?n:r.key)||null,r,i)}if(xa(r)||ge(r))return d(t,e=e.get(n)||null,r,i,null);wa(t,r)}return null}function g(i,o,l,s){for(var u=null,c=null,d=o,g=o=0,m=null;null!==d&&g<l.length;g++){d.index>g?(m=d,d=null):m=d.sibling;var v=h(i,d,l[g],s);if(null===v){null===d&&(d=m);break}e&&d&&null===v.alternate&&t(i,d),o=a(v,o,g),null===c?u=v:c.sibling=v,c=v,d=m}if(g===l.length)return n(i,d),u;if(null===d){for(;g<l.length;g++)null!==(d=f(i,l[g],s))&&(o=a(d,o,g),null===c?u=d:c.sibling=d,c=d);return u}for(d=r(i,d);g<l.length;g++)null!==(m=p(d,i,g,l[g],s))&&(e&&null!==m.alternate&&d.delete(null===m.key?g:m.key),o=a(m,o,g),null===c?u=m:c.sibling=m,c=m);return e&&d.forEach((function(e){return t(i,e)})),u}function m(i,l,s,u){var c=ge(s);if("function"!==typeof c)throw Error(o(150));if(null==(s=c.call(s)))throw Error(o(151));for(var d=c=null,g=l,m=l=0,v=null,y=s.next();null!==g&&!y.done;m++,y=s.next()){g.index>m?(v=g,g=null):v=g.sibling;var b=h(i,g,y.value,u);if(null===b){null===g&&(g=v);break}e&&g&&null===b.alternate&&t(i,g),l=a(b,l,m),null===d?c=b:d.sibling=b,d=b,g=v}if(y.done)return n(i,g),c;if(null===g){for(;!y.done;m++,y=s.next())null!==(y=f(i,y.value,u))&&(l=a(y,l,m),null===d?c=y:d.sibling=y,d=y);return c}for(g=r(i,g);!y.done;m++,y=s.next())null!==(y=p(g,i,m,y.value,u))&&(e&&null!==y.alternate&&g.delete(null===y.key?m:y.key),l=a(y,l,m),null===d?c=y:d.sibling=y,d=y);return e&&g.forEach((function(e){return t(i,e)})),c}return function(e,r,a,s){var u="object"===typeof a&&null!==a&&a.type===ne&&null===a.key;u&&(a=a.props.children);var c="object"===typeof a&&null!==a;if(c)switch(a.$$typeof){case ee:e:{for(c=a.key,u=r;null!==u;){if(u.key===c){switch(u.tag){case 7:if(a.type===ne){n(e,u.sibling),(r=i(u,a.props.children)).return=e,e=r;break e}break;default:if(u.elementType===a.type){n(e,u.sibling),(r=i(u,a.props)).ref=_a(e,u,a),r.return=e,e=r;break e}}n(e,u);break}t(e,u),u=u.sibling}a.type===ne?((r=Ps(a.props.children,e.mode,s,a.key)).return=e,e=r):((s=Cs(a.type,a.key,a.props,null,e.mode,s)).ref=_a(e,r,a),s.return=e,e=s)}return l(e);case te:e:{for(u=a.key;null!==r;){if(r.key===u){if(4===r.tag&&r.stateNode.containerInfo===a.containerInfo&&r.stateNode.implementation===a.implementation){n(e,r.sibling),(r=i(r,a.children||[])).return=e,e=r;break e}n(e,r);break}t(e,r),r=r.sibling}(r=Es(a,e.mode,s)).return=e,e=r}return l(e)}if("string"===typeof a||"number"===typeof a)return a=""+a,null!==r&&6===r.tag?(n(e,r.sibling),(r=i(r,a)).return=e,e=r):(n(e,r),(r=Ds(a,e.mode,s)).return=e,e=r),l(e);if(xa(a))return g(e,r,a,s);if(ge(a))return m(e,r,a,s);if(c&&wa(e,a),"undefined"===typeof a&&!u)switch(e.tag){case 1:case 0:throw e=e.type,Error(o(152,e.displayName||e.name||"Component"))}return n(e,r)}}var Sa=ka(!0),Ta=ka(!1),Ma={},Ca={current:Ma},Pa={current:Ma},Da={current:Ma};function Ea(e){if(e===Ma)throw Error(o(174));return e}function Oa(e,t){switch(ui(Da,t),ui(Pa,e),ui(Ca,Ma),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Le(null,"");break;default:t=Le(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}si(Ca),ui(Ca,t)}function Na(){si(Ca),si(Pa),si(Da)}function Ia(e){Ea(Da.current);var t=Ea(Ca.current),n=Le(t,e.type);t!==n&&(ui(Pa,e),ui(Ca,n))}function Aa(e){Pa.current===e&&(si(Ca),si(Pa))}var Fa={current:0};function Ra(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(64&t.effectTag))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function La(e,t){return{responder:e,props:t}}var za=K.ReactCurrentDispatcher,ja=K.ReactCurrentBatchConfig,Wa=0,Va=null,Ya=null,Ba=null,Ha=!1;function Ua(){throw Error(o(321))}function qa(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Lr(e[n],t[n]))return!1;return!0}function $a(e,t,n,r,i,a){if(Wa=a,Va=t,t.memoizedState=null,t.updateQueue=null,t.expirationTime=0,za.current=null===e||null===e.memoizedState?vo:yo,e=n(r,i),t.expirationTime===Wa){a=0;do{if(t.expirationTime=0,!(25>a))throw Error(o(301));a+=1,Ba=Ya=null,t.updateQueue=null,za.current=bo,e=n(r,i)}while(t.expirationTime===Wa)}if(za.current=mo,t=null!==Ya&&null!==Ya.next,Wa=0,Ba=Ya=Va=null,Ha=!1,t)throw Error(o(300));return e}function Ga(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===Ba?Va.memoizedState=Ba=e:Ba=Ba.next=e,Ba}function Qa(){if(null===Ya){var e=Va.alternate;e=null!==e?e.memoizedState:null}else e=Ya.next;var t=null===Ba?Va.memoizedState:Ba.next;if(null!==t)Ba=t,Ya=e;else{if(null===e)throw Error(o(310));e={memoizedState:(Ya=e).memoizedState,baseState:Ya.baseState,baseQueue:Ya.baseQueue,queue:Ya.queue,next:null},null===Ba?Va.memoizedState=Ba=e:Ba=Ba.next=e}return Ba}function Ka(e,t){return"function"===typeof t?t(e):t}function Za(e){var t=Qa(),n=t.queue;if(null===n)throw Error(o(311));n.lastRenderedReducer=e;var r=Ya,i=r.baseQueue,a=n.pending;if(null!==a){if(null!==i){var l=i.next;i.next=a.next,a.next=l}r.baseQueue=i=a,n.pending=null}if(null!==i){i=i.next,r=r.baseState;var s=l=a=null,u=i;do{var c=u.expirationTime;if(c<Wa){var d={expirationTime:u.expirationTime,suspenseConfig:u.suspenseConfig,action:u.action,eagerReducer:u.eagerReducer,eagerState:u.eagerState,next:null};null===s?(l=s=d,a=r):s=s.next=d,c>Va.expirationTime&&(Va.expirationTime=c,os(c))}else null!==s&&(s=s.next={expirationTime:1073741823,suspenseConfig:u.suspenseConfig,action:u.action,eagerReducer:u.eagerReducer,eagerState:u.eagerState,next:null}),as(c,u.suspenseConfig),r=u.eagerReducer===e?u.eagerState:e(r,u.action);u=u.next}while(null!==u&&u!==i);null===s?a=r:s.next=l,Lr(r,t.memoizedState)||(Eo=!0),t.memoizedState=r,t.baseState=a,t.baseQueue=s,n.lastRenderedState=r}return[t.memoizedState,n.dispatch]}function Xa(e){var t=Qa(),n=t.queue;if(null===n)throw Error(o(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,a=t.memoizedState;if(null!==i){n.pending=null;var l=i=i.next;do{a=e(a,l.action),l=l.next}while(l!==i);Lr(a,t.memoizedState)||(Eo=!0),t.memoizedState=a,null===t.baseQueue&&(t.baseState=a),n.lastRenderedState=a}return[a,r]}function Ja(e){var t=Ga();return"function"===typeof e&&(e=e()),t.memoizedState=t.baseState=e,e=(e=t.queue={pending:null,dispatch:null,lastRenderedReducer:Ka,lastRenderedState:e}).dispatch=go.bind(null,Va,e),[t.memoizedState,e]}function eo(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=Va.updateQueue)?(t={lastEffect:null},Va.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function to(){return Qa().memoizedState}function no(e,t,n,r){var i=Ga();Va.effectTag|=e,i.memoizedState=eo(1|t,n,void 0,void 0===r?null:r)}function ro(e,t,n,r){var i=Qa();r=void 0===r?null:r;var a=void 0;if(null!==Ya){var o=Ya.memoizedState;if(a=o.destroy,null!==r&&qa(r,o.deps))return void eo(t,n,a,r)}Va.effectTag|=e,i.memoizedState=eo(1|t,n,a,r)}function io(e,t){return no(516,4,e,t)}function ao(e,t){return ro(516,4,e,t)}function oo(e,t){return ro(4,2,e,t)}function lo(e,t){return"function"===typeof t?(e=e(),t(e),function(){t(null)}):null!==t&&void 0!==t?(e=e(),t.current=e,function(){t.current=null}):void 0}function so(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,ro(4,2,lo.bind(null,t,e),n)}function uo(){}function co(e,t){return Ga().memoizedState=[e,void 0===t?null:t],e}function fo(e,t){var n=Qa();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&qa(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function ho(e,t){var n=Qa();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&qa(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function po(e,t,n){var r=Wi();Yi(98>r?98:r,(function(){e(!0)})),Yi(97<r?97:r,(function(){var r=ja.suspense;ja.suspense=void 0===t?null:t;try{e(!1),n()}finally{ja.suspense=r}}))}function go(e,t,n){var r=ql(),i=fa.suspense;i={expirationTime:r=$l(r,e,i),suspenseConfig:i,action:n,eagerReducer:null,eagerState:null,next:null};var a=t.pending;if(null===a?i.next=i:(i.next=a.next,a.next=i),t.pending=i,a=e.alternate,e===Va||null!==a&&a===Va)Ha=!0,i.expirationTime=Wa,Va.expirationTime=Wa;else{if(0===e.expirationTime&&(null===a||0===a.expirationTime)&&null!==(a=t.lastRenderedReducer))try{var o=t.lastRenderedState,l=a(o,n);if(i.eagerReducer=a,i.eagerState=l,Lr(l,o))return}catch(s){}Gl(e,r)}}var mo={readContext:ra,useCallback:Ua,useContext:Ua,useEffect:Ua,useImperativeHandle:Ua,useLayoutEffect:Ua,useMemo:Ua,useReducer:Ua,useRef:Ua,useState:Ua,useDebugValue:Ua,useResponder:Ua,useDeferredValue:Ua,useTransition:Ua},vo={readContext:ra,useCallback:co,useContext:ra,useEffect:io,useImperativeHandle:function(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,no(4,2,lo.bind(null,t,e),n)},useLayoutEffect:function(e,t){return no(4,2,e,t)},useMemo:function(e,t){var n=Ga();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Ga();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e=(e=r.queue={pending:null,dispatch:null,lastRenderedReducer:e,lastRenderedState:t}).dispatch=go.bind(null,Va,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},Ga().memoizedState=e},useState:Ja,useDebugValue:uo,useResponder:La,useDeferredValue:function(e,t){var n=Ja(e),r=n[0],i=n[1];return io((function(){var n=ja.suspense;ja.suspense=void 0===t?null:t;try{i(e)}finally{ja.suspense=n}}),[e,t]),r},useTransition:function(e){var t=Ja(!1),n=t[0];return t=t[1],[co(po.bind(null,t,e),[t,e]),n]}},yo={readContext:ra,useCallback:fo,useContext:ra,useEffect:ao,useImperativeHandle:so,useLayoutEffect:oo,useMemo:ho,useReducer:Za,useRef:to,useState:function(){return Za(Ka)},useDebugValue:uo,useResponder:La,useDeferredValue:function(e,t){var n=Za(Ka),r=n[0],i=n[1];return ao((function(){var n=ja.suspense;ja.suspense=void 0===t?null:t;try{i(e)}finally{ja.suspense=n}}),[e,t]),r},useTransition:function(e){var t=Za(Ka),n=t[0];return t=t[1],[fo(po.bind(null,t,e),[t,e]),n]}},bo={readContext:ra,useCallback:fo,useContext:ra,useEffect:ao,useImperativeHandle:so,useLayoutEffect:oo,useMemo:ho,useReducer:Xa,useRef:to,useState:function(){return Xa(Ka)},useDebugValue:uo,useResponder:La,useDeferredValue:function(e,t){var n=Xa(Ka),r=n[0],i=n[1];return ao((function(){var n=ja.suspense;ja.suspense=void 0===t?null:t;try{i(e)}finally{ja.suspense=n}}),[e,t]),r},useTransition:function(e){var t=Xa(Ka),n=t[0];return t=t[1],[fo(po.bind(null,t,e),[t,e]),n]}},xo=null,_o=null,wo=!1;function ko(e,t){var n=Ss(5,null,null,0);n.elementType="DELETED",n.type="DELETED",n.stateNode=t,n.return=e,n.effectTag=8,null!==e.lastEffect?(e.lastEffect.nextEffect=n,e.lastEffect=n):e.firstEffect=e.lastEffect=n}function So(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,!0);case 13:default:return!1}}function To(e){if(wo){var t=_o;if(t){var n=t;if(!So(e,t)){if(!(t=_n(n.nextSibling))||!So(e,t))return e.effectTag=-1025&e.effectTag|2,wo=!1,void(xo=e);ko(xo,n)}xo=e,_o=_n(t.firstChild)}else e.effectTag=-1025&e.effectTag|2,wo=!1,xo=e}}function Mo(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;xo=e}function Co(e){if(e!==xo)return!1;if(!wo)return Mo(e),wo=!0,!1;var t=e.type;if(5!==e.tag||"head"!==t&&"body"!==t&&!yn(t,e.memoizedProps))for(t=_o;t;)ko(e,t),t=_n(t.nextSibling);if(Mo(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(o(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){_o=_n(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}_o=null}}else _o=xo?_n(e.stateNode.nextSibling):null;return!0}function Po(){_o=xo=null,wo=!1}var Do=K.ReactCurrentOwner,Eo=!1;function Oo(e,t,n,r){t.child=null===e?Ta(t,null,n,r):Sa(t,e.child,n,r)}function No(e,t,n,r,i){n=n.render;var a=t.ref;return na(t,i),r=$a(e,t,n,r,a,i),null===e||Eo?(t.effectTag|=1,Oo(e,t,r,i),t.child):(t.updateQueue=e.updateQueue,t.effectTag&=-517,e.expirationTime<=i&&(e.expirationTime=0),Go(e,t,i))}function Io(e,t,n,r,i,a){if(null===e){var o=n.type;return"function"!==typeof o||Ts(o)||void 0!==o.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=Cs(n.type,null,r,null,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=o,Ao(e,t,o,r,i,a))}return o=e.child,i<a&&(i=o.memoizedProps,(n=null!==(n=n.compare)?n:jr)(i,r)&&e.ref===t.ref)?Go(e,t,a):(t.effectTag|=1,(e=Ms(o,r)).ref=t.ref,e.return=t,t.child=e)}function Ao(e,t,n,r,i,a){return null!==e&&jr(e.memoizedProps,r)&&e.ref===t.ref&&(Eo=!1,i<a)?(t.expirationTime=e.expirationTime,Go(e,t,a)):Ro(e,t,n,r,a)}function Fo(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.effectTag|=128)}function Ro(e,t,n,r,i){var a=gi(n)?hi:di.current;return a=pi(t,a),na(t,i),n=$a(e,t,n,r,a,i),null===e||Eo?(t.effectTag|=1,Oo(e,t,n,i),t.child):(t.updateQueue=e.updateQueue,t.effectTag&=-517,e.expirationTime<=i&&(e.expirationTime=0),Go(e,t,i))}function Lo(e,t,n,r,i){if(gi(n)){var a=!0;bi(t)}else a=!1;if(na(t,i),null===t.stateNode)null!==e&&(e.alternate=null,t.alternate=null,t.effectTag|=2),va(t,n,r),ba(t,n,r,i),r=!0;else if(null===e){var o=t.stateNode,l=t.memoizedProps;o.props=l;var s=o.context,u=n.contextType;"object"===typeof u&&null!==u?u=ra(u):u=pi(t,u=gi(n)?hi:di.current);var c=n.getDerivedStateFromProps,d="function"===typeof c||"function"===typeof o.getSnapshotBeforeUpdate;d||"function"!==typeof o.UNSAFE_componentWillReceiveProps&&"function"!==typeof o.componentWillReceiveProps||(l!==r||s!==u)&&ya(t,o,r,u),ia=!1;var f=t.memoizedState;o.state=f,ca(t,r,o,i),s=t.memoizedState,l!==r||f!==s||fi.current||ia?("function"===typeof c&&(pa(t,n,c,r),s=t.memoizedState),(l=ia||ma(t,n,l,r,f,s,u))?(d||"function"!==typeof o.UNSAFE_componentWillMount&&"function"!==typeof o.componentWillMount||("function"===typeof o.componentWillMount&&o.componentWillMount(),"function"===typeof o.UNSAFE_componentWillMount&&o.UNSAFE_componentWillMount()),"function"===typeof o.componentDidMount&&(t.effectTag|=4)):("function"===typeof o.componentDidMount&&(t.effectTag|=4),t.memoizedProps=r,t.memoizedState=s),o.props=r,o.state=s,o.context=u,r=l):("function"===typeof o.componentDidMount&&(t.effectTag|=4),r=!1)}else o=t.stateNode,oa(e,t),l=t.memoizedProps,o.props=t.type===t.elementType?l:Gi(t.type,l),s=o.context,"object"===typeof(u=n.contextType)&&null!==u?u=ra(u):u=pi(t,u=gi(n)?hi:di.current),(d="function"===typeof(c=n.getDerivedStateFromProps)||"function"===typeof o.getSnapshotBeforeUpdate)||"function"!==typeof o.UNSAFE_componentWillReceiveProps&&"function"!==typeof o.componentWillReceiveProps||(l!==r||s!==u)&&ya(t,o,r,u),ia=!1,s=t.memoizedState,o.state=s,ca(t,r,o,i),f=t.memoizedState,l!==r||s!==f||fi.current||ia?("function"===typeof c&&(pa(t,n,c,r),f=t.memoizedState),(c=ia||ma(t,n,l,r,s,f,u))?(d||"function"!==typeof o.UNSAFE_componentWillUpdate&&"function"!==typeof o.componentWillUpdate||("function"===typeof o.componentWillUpdate&&o.componentWillUpdate(r,f,u),"function"===typeof o.UNSAFE_componentWillUpdate&&o.UNSAFE_componentWillUpdate(r,f,u)),"function"===typeof o.componentDidUpdate&&(t.effectTag|=4),"function"===typeof o.getSnapshotBeforeUpdate&&(t.effectTag|=256)):("function"!==typeof o.componentDidUpdate||l===e.memoizedProps&&s===e.memoizedState||(t.effectTag|=4),"function"!==typeof o.getSnapshotBeforeUpdate||l===e.memoizedProps&&s===e.memoizedState||(t.effectTag|=256),t.memoizedProps=r,t.memoizedState=f),o.props=r,o.state=f,o.context=u,r=c):("function"!==typeof o.componentDidUpdate||l===e.memoizedProps&&s===e.memoizedState||(t.effectTag|=4),"function"!==typeof o.getSnapshotBeforeUpdate||l===e.memoizedProps&&s===e.memoizedState||(t.effectTag|=256),r=!1);return zo(e,t,n,r,a,i)}function zo(e,t,n,r,i,a){Fo(e,t);var o=0!==(64&t.effectTag);if(!r&&!o)return i&&xi(t,n,!1),Go(e,t,a);r=t.stateNode,Do.current=t;var l=o&&"function"!==typeof n.getDerivedStateFromError?null:r.render();return t.effectTag|=1,null!==e&&o?(t.child=Sa(t,e.child,null,a),t.child=Sa(t,null,l,a)):Oo(e,t,l,a),t.memoizedState=r.state,i&&xi(t,n,!0),t.child}function jo(e){var t=e.stateNode;t.pendingContext?vi(0,t.pendingContext,t.pendingContext!==t.context):t.context&&vi(0,t.context,!1),Oa(e,t.containerInfo)}var Wo,Vo,Yo,Bo={dehydrated:null,retryTime:0};function Ho(e,t,n){var r,i=t.mode,a=t.pendingProps,o=Fa.current,l=!1;if((r=0!==(64&t.effectTag))||(r=0!==(2&o)&&(null===e||null!==e.memoizedState)),r?(l=!0,t.effectTag&=-65):null!==e&&null===e.memoizedState||void 0===a.fallback||!0===a.unstable_avoidThisFallback||(o|=1),ui(Fa,1&o),null===e){if(void 0!==a.fallback&&To(t),l){if(l=a.fallback,(a=Ps(null,i,0,null)).return=t,0===(2&t.mode))for(e=null!==t.memoizedState?t.child.child:t.child,a.child=e;null!==e;)e.return=a,e=e.sibling;return(n=Ps(l,i,n,null)).return=t,a.sibling=n,t.memoizedState=Bo,t.child=a,n}return i=a.children,t.memoizedState=null,t.child=Ta(t,null,i,n)}if(null!==e.memoizedState){if(i=(e=e.child).sibling,l){if(a=a.fallback,(n=Ms(e,e.pendingProps)).return=t,0===(2&t.mode)&&(l=null!==t.memoizedState?t.child.child:t.child)!==e.child)for(n.child=l;null!==l;)l.return=n,l=l.sibling;return(i=Ms(i,a)).return=t,n.sibling=i,n.childExpirationTime=0,t.memoizedState=Bo,t.child=n,i}return n=Sa(t,e.child,a.children,n),t.memoizedState=null,t.child=n}if(e=e.child,l){if(l=a.fallback,(a=Ps(null,i,0,null)).return=t,a.child=e,null!==e&&(e.return=a),0===(2&t.mode))for(e=null!==t.memoizedState?t.child.child:t.child,a.child=e;null!==e;)e.return=a,e=e.sibling;return(n=Ps(l,i,n,null)).return=t,a.sibling=n,n.effectTag|=2,a.childExpirationTime=0,t.memoizedState=Bo,t.child=a,n}return t.memoizedState=null,t.child=Sa(t,e,a.children,n)}function Uo(e,t){e.expirationTime<t&&(e.expirationTime=t);var n=e.alternate;null!==n&&n.expirationTime<t&&(n.expirationTime=t),ta(e.return,t)}function qo(e,t,n,r,i,a){var o=e.memoizedState;null===o?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailExpiration:0,tailMode:i,lastEffect:a}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailExpiration=0,o.tailMode=i,o.lastEffect=a)}function $o(e,t,n){var r=t.pendingProps,i=r.revealOrder,a=r.tail;if(Oo(e,t,r.children,n),0!==(2&(r=Fa.current)))r=1&r|2,t.effectTag|=64;else{if(null!==e&&0!==(64&e.effectTag))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Uo(e,n);else if(19===e.tag)Uo(e,n);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(ui(Fa,r),0===(2&t.mode))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;null!==n;)null!==(e=n.alternate)&&null===Ra(e)&&(i=n),n=n.sibling;null===(n=i)?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),qo(t,!1,i,n,a,t.lastEffect);break;case"backwards":for(n=null,i=t.child,t.child=null;null!==i;){if(null!==(e=i.alternate)&&null===Ra(e)){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}qo(t,!0,n,null,a,t.lastEffect);break;case"together":qo(t,!1,null,null,void 0,t.lastEffect);break;default:t.memoizedState=null}return t.child}function Go(e,t,n){null!==e&&(t.dependencies=e.dependencies);var r=t.expirationTime;if(0!==r&&os(r),t.childExpirationTime<n)return null;if(null!==e&&t.child!==e.child)throw Error(o(153));if(null!==t.child){for(n=Ms(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Ms(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Qo(e,t){switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Ko(e,t,n){var r=t.pendingProps;switch(t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return null;case 1:return gi(t.type)&&mi(),null;case 3:return Na(),si(fi),si(di),(n=t.stateNode).pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),null!==e&&null!==e.child||!Co(t)||(t.effectTag|=4),null;case 5:Aa(t),n=Ea(Da.current);var a=t.type;if(null!==e&&null!=t.stateNode)Vo(e,t,a,r,n),e.ref!==t.ref&&(t.effectTag|=128);else{if(!r){if(null===t.stateNode)throw Error(o(166));return null}if(e=Ea(Ca.current),Co(t)){r=t.stateNode,a=t.type;var l=t.memoizedProps;switch(r[Sn]=t,r[Tn]=l,a){case"iframe":case"object":case"embed":$t("load",r);break;case"video":case"audio":for(e=0;e<Ke.length;e++)$t(Ke[e],r);break;case"source":$t("error",r);break;case"img":case"image":case"link":$t("error",r),$t("load",r);break;case"form":$t("reset",r),$t("submit",r);break;case"details":$t("toggle",r);break;case"input":ke(r,l),$t("invalid",r),sn(n,"onChange");break;case"select":r._wrapperState={wasMultiple:!!l.multiple},$t("invalid",r),sn(n,"onChange");break;case"textarea":Oe(r,l),$t("invalid",r),sn(n,"onChange")}for(var s in an(a,l),e=null,l)if(l.hasOwnProperty(s)){var u=l[s];"children"===s?"string"===typeof u?r.textContent!==u&&(e=["children",u]):"number"===typeof u&&r.textContent!==""+u&&(e=["children",""+u]):S.hasOwnProperty(s)&&null!=u&&sn(n,s)}switch(a){case"input":xe(r),Me(r,l,!0);break;case"textarea":xe(r),Ie(r);break;case"select":case"option":break;default:"function"===typeof l.onClick&&(r.onclick=un)}n=e,t.updateQueue=n,null!==n&&(t.effectTag|=4)}else{switch(s=9===n.nodeType?n:n.ownerDocument,e===ln&&(e=Re(a)),e===ln?"script"===a?((e=s.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"===typeof r.is?e=s.createElement(a,{is:r.is}):(e=s.createElement(a),"select"===a&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,a),e[Sn]=t,e[Tn]=r,Wo(e,t),t.stateNode=e,s=on(a,r),a){case"iframe":case"object":case"embed":$t("load",e),u=r;break;case"video":case"audio":for(u=0;u<Ke.length;u++)$t(Ke[u],e);u=r;break;case"source":$t("error",e),u=r;break;case"img":case"image":case"link":$t("error",e),$t("load",e),u=r;break;case"form":$t("reset",e),$t("submit",e),u=r;break;case"details":$t("toggle",e),u=r;break;case"input":ke(e,r),u=we(e,r),$t("invalid",e),sn(n,"onChange");break;case"option":u=Pe(e,r);break;case"select":e._wrapperState={wasMultiple:!!r.multiple},u=i({},r,{value:void 0}),$t("invalid",e),sn(n,"onChange");break;case"textarea":Oe(e,r),u=Ee(e,r),$t("invalid",e),sn(n,"onChange");break;default:u=r}an(a,u);var c=u;for(l in c)if(c.hasOwnProperty(l)){var d=c[l];"style"===l?nn(e,d):"dangerouslySetInnerHTML"===l?null!=(d=d?d.__html:void 0)&&je(e,d):"children"===l?"string"===typeof d?("textarea"!==a||""!==d)&&We(e,d):"number"===typeof d&&We(e,""+d):"suppressContentEditableWarning"!==l&&"suppressHydrationWarning"!==l&&"autoFocus"!==l&&(S.hasOwnProperty(l)?null!=d&&sn(n,l):null!=d&&Z(e,l,d,s))}switch(a){case"input":xe(e),Me(e,r,!1);break;case"textarea":xe(e),Ie(e);break;case"option":null!=r.value&&e.setAttribute("value",""+ye(r.value));break;case"select":e.multiple=!!r.multiple,null!=(n=r.value)?De(e,!!r.multiple,n,!1):null!=r.defaultValue&&De(e,!!r.multiple,r.defaultValue,!0);break;default:"function"===typeof u.onClick&&(e.onclick=un)}vn(a,r)&&(t.effectTag|=4)}null!==t.ref&&(t.effectTag|=128)}return null;case 6:if(e&&null!=t.stateNode)Yo(0,t,e.memoizedProps,r);else{if("string"!==typeof r&&null===t.stateNode)throw Error(o(166));n=Ea(Da.current),Ea(Ca.current),Co(t)?(n=t.stateNode,r=t.memoizedProps,n[Sn]=t,n.nodeValue!==r&&(t.effectTag|=4)):((n=(9===n.nodeType?n:n.ownerDocument).createTextNode(r))[Sn]=t,t.stateNode=n)}return null;case 13:return si(Fa),r=t.memoizedState,0!==(64&t.effectTag)?(t.expirationTime=n,t):(n=null!==r,r=!1,null===e?void 0!==t.memoizedProps.fallback&&Co(t):(r=null!==(a=e.memoizedState),n||null===a||null!==(a=e.child.sibling)&&(null!==(l=t.firstEffect)?(t.firstEffect=a,a.nextEffect=l):(t.firstEffect=t.lastEffect=a,a.nextEffect=null),a.effectTag=8)),n&&!r&&0!==(2&t.mode)&&(null===e&&!0!==t.memoizedProps.unstable_avoidThisFallback||0!==(1&Fa.current)?Cl===xl&&(Cl=_l):(Cl!==xl&&Cl!==_l||(Cl=wl),0!==Nl&&null!==Sl&&(Is(Sl,Ml),As(Sl,Nl)))),(n||r)&&(t.effectTag|=4),null);case 4:return Na(),null;case 10:return ea(t),null;case 17:return gi(t.type)&&mi(),null;case 19:if(si(Fa),null===(r=t.memoizedState))return null;if(a=0!==(64&t.effectTag),null===(l=r.rendering)){if(a)Qo(r,!1);else if(Cl!==xl||null!==e&&0!==(64&e.effectTag))for(l=t.child;null!==l;){if(null!==(e=Ra(l))){for(t.effectTag|=64,Qo(r,!1),null!==(a=e.updateQueue)&&(t.updateQueue=a,t.effectTag|=4),null===r.lastEffect&&(t.firstEffect=null),t.lastEffect=r.lastEffect,r=t.child;null!==r;)l=n,(a=r).effectTag&=2,a.nextEffect=null,a.firstEffect=null,a.lastEffect=null,null===(e=a.alternate)?(a.childExpirationTime=0,a.expirationTime=l,a.child=null,a.memoizedProps=null,a.memoizedState=null,a.updateQueue=null,a.dependencies=null):(a.childExpirationTime=e.childExpirationTime,a.expirationTime=e.expirationTime,a.child=e.child,a.memoizedProps=e.memoizedProps,a.memoizedState=e.memoizedState,a.updateQueue=e.updateQueue,l=e.dependencies,a.dependencies=null===l?null:{expirationTime:l.expirationTime,firstContext:l.firstContext,responders:l.responders}),r=r.sibling;return ui(Fa,1&Fa.current|2),t.child}l=l.sibling}}else{if(!a)if(null!==(e=Ra(l))){if(t.effectTag|=64,a=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.effectTag|=4),Qo(r,!0),null===r.tail&&"hidden"===r.tailMode&&!l.alternate)return null!==(t=t.lastEffect=r.lastEffect)&&(t.nextEffect=null),null}else 2*ji()-r.renderingStartTime>r.tailExpiration&&1<n&&(t.effectTag|=64,a=!0,Qo(r,!1),t.expirationTime=t.childExpirationTime=n-1);r.isBackwards?(l.sibling=t.child,t.child=l):(null!==(n=r.last)?n.sibling=l:t.child=l,r.last=l)}return null!==r.tail?(0===r.tailExpiration&&(r.tailExpiration=ji()+500),n=r.tail,r.rendering=n,r.tail=n.sibling,r.lastEffect=t.lastEffect,r.renderingStartTime=ji(),n.sibling=null,t=Fa.current,ui(Fa,a?1&t|2:1&t),n):null}throw Error(o(156,t.tag))}function Zo(e){switch(e.tag){case 1:gi(e.type)&&mi();var t=e.effectTag;return 4096&t?(e.effectTag=-4097&t|64,e):null;case 3:if(Na(),si(fi),si(di),0!==(64&(t=e.effectTag)))throw Error(o(285));return e.effectTag=-4097&t|64,e;case 5:return Aa(e),null;case 13:return si(Fa),4096&(t=e.effectTag)?(e.effectTag=-4097&t|64,e):null;case 19:return si(Fa),null;case 4:return Na(),null;case 10:return ea(e),null;default:return null}}function Xo(e,t){return{value:e,source:t,stack:ve(t)}}Wo=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},Vo=function(e,t,n,r,a){var o=e.memoizedProps;if(o!==r){var l,s,u=t.stateNode;switch(Ea(Ca.current),e=null,n){case"input":o=we(u,o),r=we(u,r),e=[];break;case"option":o=Pe(u,o),r=Pe(u,r),e=[];break;case"select":o=i({},o,{value:void 0}),r=i({},r,{value:void 0}),e=[];break;case"textarea":o=Ee(u,o),r=Ee(u,r),e=[];break;default:"function"!==typeof o.onClick&&"function"===typeof r.onClick&&(u.onclick=un)}for(l in an(n,r),n=null,o)if(!r.hasOwnProperty(l)&&o.hasOwnProperty(l)&&null!=o[l])if("style"===l)for(s in u=o[l])u.hasOwnProperty(s)&&(n||(n={}),n[s]="");else"dangerouslySetInnerHTML"!==l&&"children"!==l&&"suppressContentEditableWarning"!==l&&"suppressHydrationWarning"!==l&&"autoFocus"!==l&&(S.hasOwnProperty(l)?e||(e=[]):(e=e||[]).push(l,null));for(l in r){var c=r[l];if(u=null!=o?o[l]:void 0,r.hasOwnProperty(l)&&c!==u&&(null!=c||null!=u))if("style"===l)if(u){for(s in u)!u.hasOwnProperty(s)||c&&c.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in c)c.hasOwnProperty(s)&&u[s]!==c[s]&&(n||(n={}),n[s]=c[s])}else n||(e||(e=[]),e.push(l,n)),n=c;else"dangerouslySetInnerHTML"===l?(c=c?c.__html:void 0,u=u?u.__html:void 0,null!=c&&u!==c&&(e=e||[]).push(l,c)):"children"===l?u===c||"string"!==typeof c&&"number"!==typeof c||(e=e||[]).push(l,""+c):"suppressContentEditableWarning"!==l&&"suppressHydrationWarning"!==l&&(S.hasOwnProperty(l)?(null!=c&&sn(a,l),e||u===c||(e=[])):(e=e||[]).push(l,c))}n&&(e=e||[]).push("style",n),a=e,(t.updateQueue=a)&&(t.effectTag|=4)}},Yo=function(e,t,n,r){n!==r&&(t.effectTag|=4)};var Jo="function"===typeof WeakSet?WeakSet:Set;function el(e,t){var n=t.source,r=t.stack;null===r&&null!==n&&(r=ve(n)),null!==n&&me(n.type),t=t.value,null!==e&&1===e.tag&&me(e.type);try{console.error(t)}catch(i){setTimeout((function(){throw i}))}}function tl(e){var t=e.ref;if(null!==t)if("function"===typeof t)try{t(null)}catch(n){ys(e,n)}else t.current=null}function nl(e,t){switch(t.tag){case 0:case 11:case 15:case 22:return;case 1:if(256&t.effectTag&&null!==e){var n=e.memoizedProps,r=e.memoizedState;t=(e=t.stateNode).getSnapshotBeforeUpdate(t.elementType===t.type?n:Gi(t.type,n),r),e.__reactInternalSnapshotBeforeUpdate=t}return;case 3:case 5:case 6:case 4:case 17:return}throw Error(o(163))}function rl(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.destroy;n.destroy=void 0,void 0!==r&&r()}n=n.next}while(n!==t)}}function il(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function al(e,t,n){switch(n.tag){case 0:case 11:case 15:case 22:return void il(3,n);case 1:if(e=n.stateNode,4&n.effectTag)if(null===t)e.componentDidMount();else{var r=n.elementType===n.type?t.memoizedProps:Gi(n.type,t.memoizedProps);e.componentDidUpdate(r,t.memoizedState,e.__reactInternalSnapshotBeforeUpdate)}return void(null!==(t=n.updateQueue)&&da(n,t,e));case 3:if(null!==(t=n.updateQueue)){if(e=null,null!==n.child)switch(n.child.tag){case 5:e=n.child.stateNode;break;case 1:e=n.child.stateNode}da(n,t,e)}return;case 5:return e=n.stateNode,void(null===t&&4&n.effectTag&&vn(n.type,n.memoizedProps)&&e.focus());case 6:case 4:case 12:return;case 13:return void(null===n.memoizedState&&(n=n.alternate,null!==n&&(n=n.memoizedState,null!==n&&(n=n.dehydrated,null!==n&&Rt(n)))));case 19:case 17:case 20:case 21:return}throw Error(o(163))}function ol(e,t,n){switch("function"===typeof ws&&ws(t),t.tag){case 0:case 11:case 14:case 15:case 22:if(null!==(e=t.updateQueue)&&null!==(e=e.lastEffect)){var r=e.next;Yi(97<n?97:n,(function(){var e=r;do{var n=e.destroy;if(void 0!==n){var i=t;try{n()}catch(a){ys(i,a)}}e=e.next}while(e!==r)}))}break;case 1:tl(t),"function"===typeof(n=t.stateNode).componentWillUnmount&&function(e,t){try{t.props=e.memoizedProps,t.state=e.memoizedState,t.componentWillUnmount()}catch(n){ys(e,n)}}(t,n);break;case 5:tl(t);break;case 4:cl(e,t,n)}}function ll(e){var t=e.alternate;e.return=null,e.child=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.alternate=null,e.firstEffect=null,e.lastEffect=null,e.pendingProps=null,e.memoizedProps=null,e.stateNode=null,null!==t&&ll(t)}function sl(e){return 5===e.tag||3===e.tag||4===e.tag}function ul(e){e:{for(var t=e.return;null!==t;){if(sl(t)){var n=t;break e}t=t.return}throw Error(o(160))}switch(t=n.stateNode,n.tag){case 5:var r=!1;break;case 3:case 4:t=t.containerInfo,r=!0;break;default:throw Error(o(161))}16&n.effectTag&&(We(t,""),n.effectTag&=-17);e:t:for(n=e;;){for(;null===n.sibling;){if(null===n.return||sl(n.return)){n=null;break e}n=n.return}for(n.sibling.return=n.return,n=n.sibling;5!==n.tag&&6!==n.tag&&18!==n.tag;){if(2&n.effectTag)continue t;if(null===n.child||4===n.tag)continue t;n.child.return=n,n=n.child}if(!(2&n.effectTag)){n=n.stateNode;break e}}r?function e(t,n,r){var i=t.tag,a=5===i||6===i;if(a)t=a?t.stateNode:t.stateNode.instance,n?8===r.nodeType?r.parentNode.insertBefore(t,n):r.insertBefore(t,n):(8===r.nodeType?(n=r.parentNode).insertBefore(t,r):(n=r).appendChild(t),null!==(r=r._reactRootContainer)&&void 0!==r||null!==n.onclick||(n.onclick=un));else if(4!==i&&null!==(t=t.child))for(e(t,n,r),t=t.sibling;null!==t;)e(t,n,r),t=t.sibling}(e,n,t):function e(t,n,r){var i=t.tag,a=5===i||6===i;if(a)t=a?t.stateNode:t.stateNode.instance,n?r.insertBefore(t,n):r.appendChild(t);else if(4!==i&&null!==(t=t.child))for(e(t,n,r),t=t.sibling;null!==t;)e(t,n,r),t=t.sibling}(e,n,t)}function cl(e,t,n){for(var r,i,a=t,l=!1;;){if(!l){l=a.return;e:for(;;){if(null===l)throw Error(o(160));switch(r=l.stateNode,l.tag){case 5:i=!1;break e;case 3:case 4:r=r.containerInfo,i=!0;break e}l=l.return}l=!0}if(5===a.tag||6===a.tag){e:for(var s=e,u=a,c=n,d=u;;)if(ol(s,d,c),null!==d.child&&4!==d.tag)d.child.return=d,d=d.child;else{if(d===u)break e;for(;null===d.sibling;){if(null===d.return||d.return===u)break e;d=d.return}d.sibling.return=d.return,d=d.sibling}i?(s=r,u=a.stateNode,8===s.nodeType?s.parentNode.removeChild(u):s.removeChild(u)):r.removeChild(a.stateNode)}else if(4===a.tag){if(null!==a.child){r=a.stateNode.containerInfo,i=!0,a.child.return=a,a=a.child;continue}}else if(ol(e,a,n),null!==a.child){a.child.return=a,a=a.child;continue}if(a===t)break;for(;null===a.sibling;){if(null===a.return||a.return===t)return;4===(a=a.return).tag&&(l=!1)}a.sibling.return=a.return,a=a.sibling}}function dl(e,t){switch(t.tag){case 0:case 11:case 14:case 15:case 22:return void rl(3,t);case 1:return;case 5:var n=t.stateNode;if(null!=n){var r=t.memoizedProps,i=null!==e?e.memoizedProps:r;e=t.type;var a=t.updateQueue;if(t.updateQueue=null,null!==a){for(n[Tn]=r,"input"===e&&"radio"===r.type&&null!=r.name&&Se(n,r),on(e,i),t=on(e,r),i=0;i<a.length;i+=2){var l=a[i],s=a[i+1];"style"===l?nn(n,s):"dangerouslySetInnerHTML"===l?je(n,s):"children"===l?We(n,s):Z(n,l,s,t)}switch(e){case"input":Te(n,r);break;case"textarea":Ne(n,r);break;case"select":t=n._wrapperState.wasMultiple,n._wrapperState.wasMultiple=!!r.multiple,null!=(e=r.value)?De(n,!!r.multiple,e,!1):t!==!!r.multiple&&(null!=r.defaultValue?De(n,!!r.multiple,r.defaultValue,!0):De(n,!!r.multiple,r.multiple?[]:"",!1))}}}return;case 6:if(null===t.stateNode)throw Error(o(162));return void(t.stateNode.nodeValue=t.memoizedProps);case 3:return void((t=t.stateNode).hydrate&&(t.hydrate=!1,Rt(t.containerInfo)));case 12:return;case 13:if(n=t,null===t.memoizedState?r=!1:(r=!0,n=t.child,Al=ji()),null!==n)e:for(e=n;;){if(5===e.tag)a=e.stateNode,r?"function"===typeof(a=a.style).setProperty?a.setProperty("display","none","important"):a.display="none":(a=e.stateNode,i=void 0!==(i=e.memoizedProps.style)&&null!==i&&i.hasOwnProperty("display")?i.display:null,a.style.display=tn("display",i));else if(6===e.tag)e.stateNode.nodeValue=r?"":e.memoizedProps;else{if(13===e.tag&&null!==e.memoizedState&&null===e.memoizedState.dehydrated){(a=e.child.sibling).return=e,e=a;continue}if(null!==e.child){e.child.return=e,e=e.child;continue}}if(e===n)break;for(;null===e.sibling;){if(null===e.return||e.return===n)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}return void fl(t);case 19:return void fl(t);case 17:return}throw Error(o(163))}function fl(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new Jo),t.forEach((function(t){var r=xs.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))}))}}var hl="function"===typeof WeakMap?WeakMap:Map;function pl(e,t,n){(n=la(n,null)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Rl||(Rl=!0,Ll=r),el(e,t)},n}function gl(e,t,n){(n=la(n,null)).tag=3;var r=e.type.getDerivedStateFromError;if("function"===typeof r){var i=t.value;n.payload=function(){return el(e,t),r(i)}}var a=e.stateNode;return null!==a&&"function"===typeof a.componentDidCatch&&(n.callback=function(){"function"!==typeof r&&(null===zl?zl=new Set([this]):zl.add(this),el(e,t));var n=t.stack;this.componentDidCatch(t.value,{componentStack:null!==n?n:""})}),n}var ml,vl=Math.ceil,yl=K.ReactCurrentDispatcher,bl=K.ReactCurrentOwner,xl=0,_l=3,wl=4,kl=0,Sl=null,Tl=null,Ml=0,Cl=xl,Pl=null,Dl=1073741823,El=1073741823,Ol=null,Nl=0,Il=!1,Al=0,Fl=null,Rl=!1,Ll=null,zl=null,jl=!1,Wl=null,Vl=90,Yl=null,Bl=0,Hl=null,Ul=0;function ql(){return 0!==(48&kl)?1073741821-(ji()/10|0):0!==Ul?Ul:Ul=1073741821-(ji()/10|0)}function $l(e,t,n){if(0===(2&(t=t.mode)))return 1073741823;var r=Wi();if(0===(4&t))return 99===r?1073741823:1073741822;if(0!==(16&kl))return Ml;if(null!==n)e=$i(e,0|n.timeoutMs||5e3,250);else switch(r){case 99:e=1073741823;break;case 98:e=$i(e,150,100);break;case 97:case 96:e=$i(e,5e3,250);break;case 95:e=2;break;default:throw Error(o(326))}return null!==Sl&&e===Ml&&--e,e}function Gl(e,t){if(50<Bl)throw Bl=0,Hl=null,Error(o(185));if(null!==(e=Ql(e,t))){var n=Wi();1073741823===t?0!==(8&kl)&&0===(48&kl)?Jl(e):(Zl(e),0===kl&&Ui()):Zl(e),0===(4&kl)||98!==n&&99!==n||(null===Yl?Yl=new Map([[e,t]]):(void 0===(n=Yl.get(e))||n>t)&&Yl.set(e,t))}}function Ql(e,t){e.expirationTime<t&&(e.expirationTime=t);var n=e.alternate;null!==n&&n.expirationTime<t&&(n.expirationTime=t);var r=e.return,i=null;if(null===r&&3===e.tag)i=e.stateNode;else for(;null!==r;){if(n=r.alternate,r.childExpirationTime<t&&(r.childExpirationTime=t),null!==n&&n.childExpirationTime<t&&(n.childExpirationTime=t),null===r.return&&3===r.tag){i=r.stateNode;break}r=r.return}return null!==i&&(Sl===i&&(os(t),Cl===wl&&Is(i,Ml)),As(i,t)),i}function Kl(e){var t=e.lastExpiredTime;if(0!==t)return t;if(!Ns(e,t=e.firstPendingTime))return t;var n=e.lastPingedTime;return 2>=(e=n>(e=e.nextKnownPendingLevel)?n:e)&&t!==e?0:e}function Zl(e){if(0!==e.lastExpiredTime)e.callbackExpirationTime=1073741823,e.callbackPriority=99,e.callbackNode=Hi(Jl.bind(null,e));else{var t=Kl(e),n=e.callbackNode;if(0===t)null!==n&&(e.callbackNode=null,e.callbackExpirationTime=0,e.callbackPriority=90);else{var r=ql();if(1073741823===t?r=99:1===t||2===t?r=95:r=0>=(r=10*(1073741821-t)-10*(1073741821-r))?99:250>=r?98:5250>=r?97:95,null!==n){var i=e.callbackPriority;if(e.callbackExpirationTime===t&&i>=r)return;n!==Ni&&ki(n)}e.callbackExpirationTime=t,e.callbackPriority=r,t=1073741823===t?Hi(Jl.bind(null,e)):Bi(r,Xl.bind(null,e),{timeout:10*(1073741821-t)-ji()}),e.callbackNode=t}}}function Xl(e,t){if(Ul=0,t)return Fs(e,t=ql()),Zl(e),null;var n=Kl(e);if(0!==n){if(t=e.callbackNode,0!==(48&kl))throw Error(o(327));if(gs(),e===Sl&&n===Ml||ns(e,n),null!==Tl){var r=kl;kl|=16;for(var i=is();;)try{ss();break}catch(s){rs(e,s)}if(Ji(),kl=r,yl.current=i,1===Cl)throw t=Pl,ns(e,n),Is(e,n),Zl(e),t;if(null===Tl)switch(i=e.finishedWork=e.current.alternate,e.finishedExpirationTime=n,r=Cl,Sl=null,r){case xl:case 1:throw Error(o(345));case 2:Fs(e,2<n?2:n);break;case _l:if(Is(e,n),n===(r=e.lastSuspendedTime)&&(e.nextKnownPendingLevel=ds(i)),1073741823===Dl&&10<(i=Al+500-ji())){if(Il){var a=e.lastPingedTime;if(0===a||a>=n){e.lastPingedTime=n,ns(e,n);break}}if(0!==(a=Kl(e))&&a!==n)break;if(0!==r&&r!==n){e.lastPingedTime=r;break}e.timeoutHandle=bn(fs.bind(null,e),i);break}fs(e);break;case wl:if(Is(e,n),n===(r=e.lastSuspendedTime)&&(e.nextKnownPendingLevel=ds(i)),Il&&(0===(i=e.lastPingedTime)||i>=n)){e.lastPingedTime=n,ns(e,n);break}if(0!==(i=Kl(e))&&i!==n)break;if(0!==r&&r!==n){e.lastPingedTime=r;break}if(1073741823!==El?r=10*(1073741821-El)-ji():1073741823===Dl?r=0:(r=10*(1073741821-Dl)-5e3,0>(r=(i=ji())-r)&&(r=0),(n=10*(1073741821-n)-i)<(r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*vl(r/1960))-r)&&(r=n)),10<r){e.timeoutHandle=bn(fs.bind(null,e),r);break}fs(e);break;case 5:if(1073741823!==Dl&&null!==Ol){a=Dl;var l=Ol;if(0>=(r=0|l.busyMinDurationMs)?r=0:(i=0|l.busyDelayMs,r=(a=ji()-(10*(1073741821-a)-(0|l.timeoutMs||5e3)))<=i?0:i+r-a),10<r){Is(e,n),e.timeoutHandle=bn(fs.bind(null,e),r);break}}fs(e);break;default:throw Error(o(329))}if(Zl(e),e.callbackNode===t)return Xl.bind(null,e)}}return null}function Jl(e){var t=e.lastExpiredTime;if(t=0!==t?t:1073741823,0!==(48&kl))throw Error(o(327));if(gs(),e===Sl&&t===Ml||ns(e,t),null!==Tl){var n=kl;kl|=16;for(var r=is();;)try{ls();break}catch(i){rs(e,i)}if(Ji(),kl=n,yl.current=r,1===Cl)throw n=Pl,ns(e,t),Is(e,t),Zl(e),n;if(null!==Tl)throw Error(o(261));e.finishedWork=e.current.alternate,e.finishedExpirationTime=t,Sl=null,fs(e),Zl(e)}return null}function es(e,t){var n=kl;kl|=1;try{return e(t)}finally{0===(kl=n)&&Ui()}}function ts(e,t){var n=kl;kl&=-2,kl|=8;try{return e(t)}finally{0===(kl=n)&&Ui()}}function ns(e,t){e.finishedWork=null,e.finishedExpirationTime=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,xn(n)),null!==Tl)for(n=Tl.return;null!==n;){var r=n;switch(r.tag){case 1:null!==(r=r.type.childContextTypes)&&void 0!==r&&mi();break;case 3:Na(),si(fi),si(di);break;case 5:Aa(r);break;case 4:Na();break;case 13:case 19:si(Fa);break;case 10:ea(r)}n=n.return}Sl=e,Tl=Ms(e.current,null),Ml=t,Cl=xl,Pl=null,El=Dl=1073741823,Ol=null,Nl=0,Il=!1}function rs(e,t){for(;;){try{if(Ji(),za.current=mo,Ha)for(var n=Va.memoizedState;null!==n;){var r=n.queue;null!==r&&(r.pending=null),n=n.next}if(Wa=0,Ba=Ya=Va=null,Ha=!1,null===Tl||null===Tl.return)return Cl=1,Pl=t,Tl=null;e:{var i=e,a=Tl.return,o=Tl,l=t;if(t=Ml,o.effectTag|=2048,o.firstEffect=o.lastEffect=null,null!==l&&"object"===typeof l&&"function"===typeof l.then){var s=l;if(0===(2&o.mode)){var u=o.alternate;u?(o.updateQueue=u.updateQueue,o.memoizedState=u.memoizedState,o.expirationTime=u.expirationTime):(o.updateQueue=null,o.memoizedState=null)}var c=0!==(1&Fa.current),d=a;do{var f;if(f=13===d.tag){var h=d.memoizedState;if(null!==h)f=null!==h.dehydrated;else{var p=d.memoizedProps;f=void 0!==p.fallback&&(!0!==p.unstable_avoidThisFallback||!c)}}if(f){var g=d.updateQueue;if(null===g){var m=new Set;m.add(s),d.updateQueue=m}else g.add(s);if(0===(2&d.mode)){if(d.effectTag|=64,o.effectTag&=-2981,1===o.tag)if(null===o.alternate)o.tag=17;else{var v=la(1073741823,null);v.tag=2,sa(o,v)}o.expirationTime=1073741823;break e}l=void 0,o=t;var y=i.pingCache;if(null===y?(y=i.pingCache=new hl,l=new Set,y.set(s,l)):void 0===(l=y.get(s))&&(l=new Set,y.set(s,l)),!l.has(o)){l.add(o);var b=bs.bind(null,i,s,o);s.then(b,b)}d.effectTag|=4096,d.expirationTime=t;break e}d=d.return}while(null!==d);l=Error((me(o.type)||"A React component")+" suspended while rendering, but no fallback UI was specified.\n\nAdd a <Suspense fallback=...> component higher in the tree to provide a loading indicator or placeholder to display."+ve(o))}5!==Cl&&(Cl=2),l=Xo(l,o),d=a;do{switch(d.tag){case 3:s=l,d.effectTag|=4096,d.expirationTime=t,ua(d,pl(d,s,t));break e;case 1:s=l;var x=d.type,_=d.stateNode;if(0===(64&d.effectTag)&&("function"===typeof x.getDerivedStateFromError||null!==_&&"function"===typeof _.componentDidCatch&&(null===zl||!zl.has(_)))){d.effectTag|=4096,d.expirationTime=t,ua(d,gl(d,s,t));break e}}d=d.return}while(null!==d)}Tl=cs(Tl)}catch(w){t=w;continue}break}}function is(){var e=yl.current;return yl.current=mo,null===e?mo:e}function as(e,t){e<Dl&&2<e&&(Dl=e),null!==t&&e<El&&2<e&&(El=e,Ol=t)}function os(e){e>Nl&&(Nl=e)}function ls(){for(;null!==Tl;)Tl=us(Tl)}function ss(){for(;null!==Tl&&!Ii();)Tl=us(Tl)}function us(e){var t=ml(e.alternate,e,Ml);return e.memoizedProps=e.pendingProps,null===t&&(t=cs(e)),bl.current=null,t}function cs(e){Tl=e;do{var t=Tl.alternate;if(e=Tl.return,0===(2048&Tl.effectTag)){if(t=Ko(t,Tl,Ml),1===Ml||1!==Tl.childExpirationTime){for(var n=0,r=Tl.child;null!==r;){var i=r.expirationTime,a=r.childExpirationTime;i>n&&(n=i),a>n&&(n=a),r=r.sibling}Tl.childExpirationTime=n}if(null!==t)return t;null!==e&&0===(2048&e.effectTag)&&(null===e.firstEffect&&(e.firstEffect=Tl.firstEffect),null!==Tl.lastEffect&&(null!==e.lastEffect&&(e.lastEffect.nextEffect=Tl.firstEffect),e.lastEffect=Tl.lastEffect),1<Tl.effectTag&&(null!==e.lastEffect?e.lastEffect.nextEffect=Tl:e.firstEffect=Tl,e.lastEffect=Tl))}else{if(null!==(t=Zo(Tl)))return t.effectTag&=2047,t;null!==e&&(e.firstEffect=e.lastEffect=null,e.effectTag|=2048)}if(null!==(t=Tl.sibling))return t;Tl=e}while(null!==Tl);return Cl===xl&&(Cl=5),null}function ds(e){var t=e.expirationTime;return t>(e=e.childExpirationTime)?t:e}function fs(e){var t=Wi();return Yi(99,hs.bind(null,e,t)),null}function hs(e,t){do{gs()}while(null!==Wl);if(0!==(48&kl))throw Error(o(327));var n=e.finishedWork,r=e.finishedExpirationTime;if(null===n)return null;if(e.finishedWork=null,e.finishedExpirationTime=0,n===e.current)throw Error(o(177));e.callbackNode=null,e.callbackExpirationTime=0,e.callbackPriority=90,e.nextKnownPendingLevel=0;var i=ds(n);if(e.firstPendingTime=i,r<=e.lastSuspendedTime?e.firstSuspendedTime=e.lastSuspendedTime=e.nextKnownPendingLevel=0:r<=e.firstSuspendedTime&&(e.firstSuspendedTime=r-1),r<=e.lastPingedTime&&(e.lastPingedTime=0),r<=e.lastExpiredTime&&(e.lastExpiredTime=0),e===Sl&&(Tl=Sl=null,Ml=0),1<n.effectTag?null!==n.lastEffect?(n.lastEffect.nextEffect=n,i=n.firstEffect):i=n:i=n.firstEffect,null!==i){var a=kl;kl|=32,bl.current=null,gn=qt;var l=hn();if(pn(l)){if("selectionStart"in l)var s={start:l.selectionStart,end:l.selectionEnd};else e:{var u=(s=(s=l.ownerDocument)&&s.defaultView||window).getSelection&&s.getSelection();if(u&&0!==u.rangeCount){s=u.anchorNode;var c=u.anchorOffset,d=u.focusNode;u=u.focusOffset;try{s.nodeType,d.nodeType}catch(M){s=null;break e}var f=0,h=-1,p=-1,g=0,m=0,v=l,y=null;t:for(;;){for(var b;v!==s||0!==c&&3!==v.nodeType||(h=f+c),v!==d||0!==u&&3!==v.nodeType||(p=f+u),3===v.nodeType&&(f+=v.nodeValue.length),null!==(b=v.firstChild);)y=v,v=b;for(;;){if(v===l)break t;if(y===s&&++g===c&&(h=f),y===d&&++m===u&&(p=f),null!==(b=v.nextSibling))break;y=(v=y).parentNode}v=b}s=-1===h||-1===p?null:{start:h,end:p}}else s=null}s=s||{start:0,end:0}}else s=null;mn={activeElementDetached:null,focusedElem:l,selectionRange:s},qt=!1,Fl=i;do{try{ps()}catch(M){if(null===Fl)throw Error(o(330));ys(Fl,M),Fl=Fl.nextEffect}}while(null!==Fl);Fl=i;do{try{for(l=e,s=t;null!==Fl;){var x=Fl.effectTag;if(16&x&&We(Fl.stateNode,""),128&x){var _=Fl.alternate;if(null!==_){var w=_.ref;null!==w&&("function"===typeof w?w(null):w.current=null)}}switch(1038&x){case 2:ul(Fl),Fl.effectTag&=-3;break;case 6:ul(Fl),Fl.effectTag&=-3,dl(Fl.alternate,Fl);break;case 1024:Fl.effectTag&=-1025;break;case 1028:Fl.effectTag&=-1025,dl(Fl.alternate,Fl);break;case 4:dl(Fl.alternate,Fl);break;case 8:cl(l,c=Fl,s),ll(c)}Fl=Fl.nextEffect}}catch(M){if(null===Fl)throw Error(o(330));ys(Fl,M),Fl=Fl.nextEffect}}while(null!==Fl);if(w=mn,_=hn(),x=w.focusedElem,s=w.selectionRange,_!==x&&x&&x.ownerDocument&&function e(t,n){return!(!t||!n)&&(t===n||(!t||3!==t.nodeType)&&(n&&3===n.nodeType?e(t,n.parentNode):"contains"in t?t.contains(n):!!t.compareDocumentPosition&&!!(16&t.compareDocumentPosition(n))))}(x.ownerDocument.documentElement,x)){null!==s&&pn(x)&&(_=s.start,void 0===(w=s.end)&&(w=_),"selectionStart"in x?(x.selectionStart=_,x.selectionEnd=Math.min(w,x.value.length)):(w=(_=x.ownerDocument||document)&&_.defaultView||window).getSelection&&(w=w.getSelection(),c=x.textContent.length,l=Math.min(s.start,c),s=void 0===s.end?l:Math.min(s.end,c),!w.extend&&l>s&&(c=s,s=l,l=c),c=fn(x,l),d=fn(x,s),c&&d&&(1!==w.rangeCount||w.anchorNode!==c.node||w.anchorOffset!==c.offset||w.focusNode!==d.node||w.focusOffset!==d.offset)&&((_=_.createRange()).setStart(c.node,c.offset),w.removeAllRanges(),l>s?(w.addRange(_),w.extend(d.node,d.offset)):(_.setEnd(d.node,d.offset),w.addRange(_))))),_=[];for(w=x;w=w.parentNode;)1===w.nodeType&&_.push({element:w,left:w.scrollLeft,top:w.scrollTop});for("function"===typeof x.focus&&x.focus(),x=0;x<_.length;x++)(w=_[x]).element.scrollLeft=w.left,w.element.scrollTop=w.top}qt=!!gn,mn=gn=null,e.current=n,Fl=i;do{try{for(x=e;null!==Fl;){var k=Fl.effectTag;if(36&k&&al(x,Fl.alternate,Fl),128&k){_=void 0;var S=Fl.ref;if(null!==S){var T=Fl.stateNode;switch(Fl.tag){case 5:_=T;break;default:_=T}"function"===typeof S?S(_):S.current=_}}Fl=Fl.nextEffect}}catch(M){if(null===Fl)throw Error(o(330));ys(Fl,M),Fl=Fl.nextEffect}}while(null!==Fl);Fl=null,Ai(),kl=a}else e.current=n;if(jl)jl=!1,Wl=e,Vl=t;else for(Fl=i;null!==Fl;)t=Fl.nextEffect,Fl.nextEffect=null,Fl=t;if(0===(t=e.firstPendingTime)&&(zl=null),1073741823===t?e===Hl?Bl++:(Bl=0,Hl=e):Bl=0,"function"===typeof _s&&_s(n.stateNode,r),Zl(e),Rl)throw Rl=!1,e=Ll,Ll=null,e;return 0!==(8&kl)||Ui(),null}function ps(){for(;null!==Fl;){var e=Fl.effectTag;0!==(256&e)&&nl(Fl.alternate,Fl),0===(512&e)||jl||(jl=!0,Bi(97,(function(){return gs(),null}))),Fl=Fl.nextEffect}}function gs(){if(90!==Vl){var e=97<Vl?97:Vl;return Vl=90,Yi(e,ms)}}function ms(){if(null===Wl)return!1;var e=Wl;if(Wl=null,0!==(48&kl))throw Error(o(331));var t=kl;for(kl|=32,e=e.current.firstEffect;null!==e;){try{var n=e;if(0!==(512&n.effectTag))switch(n.tag){case 0:case 11:case 15:case 22:rl(5,n),il(5,n)}}catch(r){if(null===e)throw Error(o(330));ys(e,r)}n=e.nextEffect,e.nextEffect=null,e=n}return kl=t,Ui(),!0}function vs(e,t,n){sa(e,t=pl(e,t=Xo(n,t),1073741823)),null!==(e=Ql(e,1073741823))&&Zl(e)}function ys(e,t){if(3===e.tag)vs(e,e,t);else for(var n=e.return;null!==n;){if(3===n.tag){vs(n,e,t);break}if(1===n.tag){var r=n.stateNode;if("function"===typeof n.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===zl||!zl.has(r))){sa(n,e=gl(n,e=Xo(t,e),1073741823)),null!==(n=Ql(n,1073741823))&&Zl(n);break}}n=n.return}}function bs(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),Sl===e&&Ml===n?Cl===wl||Cl===_l&&1073741823===Dl&&ji()-Al<500?ns(e,Ml):Il=!0:Ns(e,n)&&(0!==(t=e.lastPingedTime)&&t<n||(e.lastPingedTime=n,Zl(e)))}function xs(e,t){var n=e.stateNode;null!==n&&n.delete(t),0===(t=0)&&(t=$l(t=ql(),e,null)),null!==(e=Ql(e,t))&&Zl(e)}ml=function(e,t,n){var r=t.expirationTime;if(null!==e){var i=t.pendingProps;if(e.memoizedProps!==i||fi.current)Eo=!0;else{if(r<n){switch(Eo=!1,t.tag){case 3:jo(t),Po();break;case 5:if(Ia(t),4&t.mode&&1!==n&&i.hidden)return t.expirationTime=t.childExpirationTime=1,null;break;case 1:gi(t.type)&&bi(t);break;case 4:Oa(t,t.stateNode.containerInfo);break;case 10:r=t.memoizedProps.value,i=t.type._context,ui(Qi,i._currentValue),i._currentValue=r;break;case 13:if(null!==t.memoizedState)return 0!==(r=t.child.childExpirationTime)&&r>=n?Ho(e,t,n):(ui(Fa,1&Fa.current),null!==(t=Go(e,t,n))?t.sibling:null);ui(Fa,1&Fa.current);break;case 19:if(r=t.childExpirationTime>=n,0!==(64&e.effectTag)){if(r)return $o(e,t,n);t.effectTag|=64}if(null!==(i=t.memoizedState)&&(i.rendering=null,i.tail=null),ui(Fa,Fa.current),!r)return null}return Go(e,t,n)}Eo=!1}}else Eo=!1;switch(t.expirationTime=0,t.tag){case 2:if(r=t.type,null!==e&&(e.alternate=null,t.alternate=null,t.effectTag|=2),e=t.pendingProps,i=pi(t,di.current),na(t,n),i=$a(null,t,r,e,i,n),t.effectTag|=1,"object"===typeof i&&null!==i&&"function"===typeof i.render&&void 0===i.$$typeof){if(t.tag=1,t.memoizedState=null,t.updateQueue=null,gi(r)){var a=!0;bi(t)}else a=!1;t.memoizedState=null!==i.state&&void 0!==i.state?i.state:null,aa(t);var l=r.getDerivedStateFromProps;"function"===typeof l&&pa(t,r,l,e),i.updater=ga,t.stateNode=i,i._reactInternalFiber=t,ba(t,r,e,n),t=zo(null,t,r,!0,a,n)}else t.tag=0,Oo(null,t,i,n),t=t.child;return t;case 16:e:{if(i=t.elementType,null!==e&&(e.alternate=null,t.alternate=null,t.effectTag|=2),e=t.pendingProps,function(e){if(-1===e._status){e._status=0;var t=e._ctor;t=t(),e._result=t,t.then((function(t){0===e._status&&(t=t.default,e._status=1,e._result=t)}),(function(t){0===e._status&&(e._status=2,e._result=t)}))}}(i),1!==i._status)throw i._result;switch(i=i._result,t.type=i,a=t.tag=function(e){if("function"===typeof e)return Ts(e)?1:0;if(void 0!==e&&null!==e){if((e=e.$$typeof)===se)return 11;if(e===de)return 14}return 2}(i),e=Gi(i,e),a){case 0:t=Ro(null,t,i,e,n);break e;case 1:t=Lo(null,t,i,e,n);break e;case 11:t=No(null,t,i,e,n);break e;case 14:t=Io(null,t,i,Gi(i.type,e),r,n);break e}throw Error(o(306,i,""))}return t;case 0:return r=t.type,i=t.pendingProps,Ro(e,t,r,i=t.elementType===r?i:Gi(r,i),n);case 1:return r=t.type,i=t.pendingProps,Lo(e,t,r,i=t.elementType===r?i:Gi(r,i),n);case 3:if(jo(t),r=t.updateQueue,null===e||null===r)throw Error(o(282));if(r=t.pendingProps,i=null!==(i=t.memoizedState)?i.element:null,oa(e,t),ca(t,r,null,n),(r=t.memoizedState.element)===i)Po(),t=Go(e,t,n);else{if((i=t.stateNode.hydrate)&&(_o=_n(t.stateNode.containerInfo.firstChild),xo=t,i=wo=!0),i)for(n=Ta(t,null,r,n),t.child=n;n;)n.effectTag=-3&n.effectTag|1024,n=n.sibling;else Oo(e,t,r,n),Po();t=t.child}return t;case 5:return Ia(t),null===e&&To(t),r=t.type,i=t.pendingProps,a=null!==e?e.memoizedProps:null,l=i.children,yn(r,i)?l=null:null!==a&&yn(r,a)&&(t.effectTag|=16),Fo(e,t),4&t.mode&&1!==n&&i.hidden?(t.expirationTime=t.childExpirationTime=1,t=null):(Oo(e,t,l,n),t=t.child),t;case 6:return null===e&&To(t),null;case 13:return Ho(e,t,n);case 4:return Oa(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=Sa(t,null,r,n):Oo(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,No(e,t,r,i=t.elementType===r?i:Gi(r,i),n);case 7:return Oo(e,t,t.pendingProps,n),t.child;case 8:case 12:return Oo(e,t,t.pendingProps.children,n),t.child;case 10:e:{r=t.type._context,i=t.pendingProps,l=t.memoizedProps,a=i.value;var s=t.type._context;if(ui(Qi,s._currentValue),s._currentValue=a,null!==l)if(s=l.value,0===(a=Lr(s,a)?0:0|("function"===typeof r._calculateChangedBits?r._calculateChangedBits(s,a):1073741823))){if(l.children===i.children&&!fi.current){t=Go(e,t,n);break e}}else for(null!==(s=t.child)&&(s.return=t);null!==s;){var u=s.dependencies;if(null!==u){l=s.child;for(var c=u.firstContext;null!==c;){if(c.context===r&&0!==(c.observedBits&a)){1===s.tag&&((c=la(n,null)).tag=2,sa(s,c)),s.expirationTime<n&&(s.expirationTime=n),null!==(c=s.alternate)&&c.expirationTime<n&&(c.expirationTime=n),ta(s.return,n),u.expirationTime<n&&(u.expirationTime=n);break}c=c.next}}else l=10===s.tag&&s.type===t.type?null:s.child;if(null!==l)l.return=s;else for(l=s;null!==l;){if(l===t){l=null;break}if(null!==(s=l.sibling)){s.return=l.return,l=s;break}l=l.return}s=l}Oo(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=(a=t.pendingProps).children,na(t,n),r=r(i=ra(i,a.unstable_observedBits)),t.effectTag|=1,Oo(e,t,r,n),t.child;case 14:return a=Gi(i=t.type,t.pendingProps),Io(e,t,i,a=Gi(i.type,a),r,n);case 15:return Ao(e,t,t.type,t.pendingProps,r,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:Gi(r,i),null!==e&&(e.alternate=null,t.alternate=null,t.effectTag|=2),t.tag=1,gi(r)?(e=!0,bi(t)):e=!1,na(t,n),va(t,r,i),ba(t,r,i,n),zo(null,t,r,!0,e,n);case 19:return $o(e,t,n)}throw Error(o(156,t.tag))};var _s=null,ws=null;function ks(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.effectTag=0,this.lastEffect=this.firstEffect=this.nextEffect=null,this.childExpirationTime=this.expirationTime=0,this.alternate=null}function Ss(e,t,n,r){return new ks(e,t,n,r)}function Ts(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Ms(e,t){var n=e.alternate;return null===n?((n=Ss(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.effectTag=0,n.nextEffect=null,n.firstEffect=null,n.lastEffect=null),n.childExpirationTime=e.childExpirationTime,n.expirationTime=e.expirationTime,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{expirationTime:t.expirationTime,firstContext:t.firstContext,responders:t.responders},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Cs(e,t,n,r,i,a){var l=2;if(r=e,"function"===typeof e)Ts(e)&&(l=1);else if("string"===typeof e)l=5;else e:switch(e){case ne:return Ps(n.children,i,a,t);case le:l=8,i|=7;break;case re:l=8,i|=1;break;case ie:return(e=Ss(12,n,t,8|i)).elementType=ie,e.type=ie,e.expirationTime=a,e;case ue:return(e=Ss(13,n,t,i)).type=ue,e.elementType=ue,e.expirationTime=a,e;case ce:return(e=Ss(19,n,t,i)).elementType=ce,e.expirationTime=a,e;default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case ae:l=10;break e;case oe:l=9;break e;case se:l=11;break e;case de:l=14;break e;case fe:l=16,r=null;break e;case he:l=22;break e}throw Error(o(130,null==e?e:typeof e,""))}return(t=Ss(l,n,t,i)).elementType=e,t.type=r,t.expirationTime=a,t}function Ps(e,t,n,r){return(e=Ss(7,e,r,t)).expirationTime=n,e}function Ds(e,t,n){return(e=Ss(6,e,null,t)).expirationTime=n,e}function Es(e,t,n){return(t=Ss(4,null!==e.children?e.children:[],e.key,t)).expirationTime=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Os(e,t,n){this.tag=t,this.current=null,this.containerInfo=e,this.pingCache=this.pendingChildren=null,this.finishedExpirationTime=0,this.finishedWork=null,this.timeoutHandle=-1,this.pendingContext=this.context=null,this.hydrate=n,this.callbackNode=null,this.callbackPriority=90,this.lastExpiredTime=this.lastPingedTime=this.nextKnownPendingLevel=this.lastSuspendedTime=this.firstSuspendedTime=this.firstPendingTime=0}function Ns(e,t){var n=e.firstSuspendedTime;return e=e.lastSuspendedTime,0!==n&&n>=t&&e<=t}function Is(e,t){var n=e.firstSuspendedTime,r=e.lastSuspendedTime;n<t&&(e.firstSuspendedTime=t),(r>t||0===n)&&(e.lastSuspendedTime=t),t<=e.lastPingedTime&&(e.lastPingedTime=0),t<=e.lastExpiredTime&&(e.lastExpiredTime=0)}function As(e,t){t>e.firstPendingTime&&(e.firstPendingTime=t);var n=e.firstSuspendedTime;0!==n&&(t>=n?e.firstSuspendedTime=e.lastSuspendedTime=e.nextKnownPendingLevel=0:t>=e.lastSuspendedTime&&(e.lastSuspendedTime=t+1),t>e.nextKnownPendingLevel&&(e.nextKnownPendingLevel=t))}function Fs(e,t){var n=e.lastExpiredTime;(0===n||n>t)&&(e.lastExpiredTime=t)}function Rs(e,t,n,r){var i=t.current,a=ql(),l=fa.suspense;a=$l(a,i,l);e:if(n){t:{if(Je(n=n._reactInternalFiber)!==n||1!==n.tag)throw Error(o(170));var s=n;do{switch(s.tag){case 3:s=s.stateNode.context;break t;case 1:if(gi(s.type)){s=s.stateNode.__reactInternalMemoizedMergedChildContext;break t}}s=s.return}while(null!==s);throw Error(o(171))}if(1===n.tag){var u=n.type;if(gi(u)){n=yi(n,u,s);break e}}n=s}else n=ci;return null===t.context?t.context=n:t.pendingContext=n,(t=la(a,l)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),sa(i,t),Gl(i,a),a}function Ls(e){if(!(e=e.current).child)return null;switch(e.child.tag){case 5:default:return e.child.stateNode}}function zs(e,t){null!==(e=e.memoizedState)&&null!==e.dehydrated&&e.retryTime<t&&(e.retryTime=t)}function js(e,t){zs(e,t),(e=e.alternate)&&zs(e,t)}function Ws(e,t,n){var r=new Os(e,t,n=null!=n&&!0===n.hydrate),i=Ss(3,null,null,2===t?7:1===t?3:0);r.current=i,i.stateNode=r,aa(i),e[Mn]=r.current,n&&0!==t&&function(e,t){var n=Xe(t);Mt.forEach((function(e){pt(e,t,n)})),Ct.forEach((function(e){pt(e,t,n)}))}(0,9===e.nodeType?e:e.ownerDocument),this._internalRoot=r}function Vs(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function Ys(e,t,n,r,i){var a=n._reactRootContainer;if(a){var o=a._internalRoot;if("function"===typeof i){var l=i;i=function(){var e=Ls(o);l.call(e)}}Rs(t,o,e,i)}else{if(a=n._reactRootContainer=function(e,t){if(t||(t=!(!(t=e?9===e.nodeType?e.documentElement:e.firstChild:null)||1!==t.nodeType||!t.hasAttribute("data-reactroot"))),!t)for(var n;n=e.lastChild;)e.removeChild(n);return new Ws(e,0,t?{hydrate:!0}:void 0)}(n,r),o=a._internalRoot,"function"===typeof i){var s=i;i=function(){var e=Ls(o);s.call(e)}}ts((function(){Rs(t,o,e,i)}))}return Ls(o)}function Bs(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:te,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}function Hs(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!Vs(t))throw Error(o(200));return Bs(e,t,null,n)}Ws.prototype.render=function(e){Rs(e,this._internalRoot,null,null)},Ws.prototype.unmount=function(){var e=this._internalRoot,t=e.containerInfo;Rs(null,e,null,(function(){t[Mn]=null}))},gt=function(e){if(13===e.tag){var t=$i(ql(),150,100);Gl(e,t),js(e,t)}},mt=function(e){13===e.tag&&(Gl(e,3),js(e,3))},vt=function(e){if(13===e.tag){var t=ql();Gl(e,t=$l(t,e,null)),js(e,t)}},P=function(e,t,n){switch(t){case"input":if(Te(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=En(r);if(!i)throw Error(o(90));_e(r),Te(r,i)}}}break;case"textarea":Ne(e,n);break;case"select":null!=(t=n.value)&&De(e,!!n.multiple,t,!1)}},A=es,F=function(e,t,n,r,i){var a=kl;kl|=4;try{return Yi(98,e.bind(null,t,n,r,i))}finally{0===(kl=a)&&Ui()}},R=function(){0===(49&kl)&&(function(){if(null!==Yl){var e=Yl;Yl=null,e.forEach((function(e,t){Fs(t,e),Zl(t)})),Ui()}}(),gs())},L=function(e,t){var n=kl;kl|=2;try{return e(t)}finally{0===(kl=n)&&Ui()}};var Us={Events:[Pn,Dn,En,M,k,Ln,function(e){it(e,Rn)},N,I,Zt,lt,gs,{current:!1}]};!function(e){var t=e.findFiberByHostInstance;(function(e){if("undefined"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__)return!1;var t=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(t.isDisabled||!t.supportsFiber)return!0;try{var n=t.inject(e);_s=function(e){try{t.onCommitFiberRoot(n,e,void 0,64===(64&e.current.effectTag))}catch(r){}},ws=function(e){try{t.onCommitFiberUnmount(n,e)}catch(r){}}}catch(r){}})(i({},e,{overrideHookState:null,overrideProps:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:K.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=nt(e))?null:e.stateNode},findFiberByHostInstance:function(e){return t?t(e):null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null}))}({findFiberByHostInstance:Cn,bundleType:0,version:"16.13.1",rendererPackageName:"react-dom"}),t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Us,t.createPortal=Hs,t.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternalFiber;if(void 0===t){if("function"===typeof e.render)throw Error(o(188));throw Error(o(268,Object.keys(e)))}return e=null===(e=nt(t))?null:e.stateNode},t.flushSync=function(e,t){if(0!==(48&kl))throw Error(o(187));var n=kl;kl|=1;try{return Yi(99,e.bind(null,t))}finally{kl=n,Ui()}},t.hydrate=function(e,t,n){if(!Vs(t))throw Error(o(200));return Ys(null,e,t,!0,n)},t.render=function(e,t,n){if(!Vs(t))throw Error(o(200));return Ys(null,e,t,!1,n)},t.unmountComponentAtNode=function(e){if(!Vs(e))throw Error(o(40));return!!e._reactRootContainer&&(ts((function(){Ys(null,null,e,!1,(function(){e._reactRootContainer=null,e[Mn]=null}))})),!0)},t.unstable_batchedUpdates=es,t.unstable_createPortal=function(e,t){return Hs(e,t,2<arguments.length&&void 0!==arguments[2]?arguments[2]:null)},t.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Vs(n))throw Error(o(200));if(null==e||void 0===e._reactInternalFiber)throw Error(o(38));return Ys(e,t,n,!1,r)},t.version="16.13.1"},function(e,t,n){"use strict";e.exports=n(52)},function(e,t,n){"use strict";var r,i,a,o,l;if("undefined"===typeof window||"function"!==typeof MessageChannel){var s=null,u=null,c=function e(){if(null!==s)try{var n=t.unstable_now();s(!0,n),s=null}catch(r){throw setTimeout(e,0),r}},d=Date.now();t.unstable_now=function(){return Date.now()-d},r=function(e){null!==s?setTimeout(r,0,e):(s=e,setTimeout(c,0))},i=function(e,t){u=setTimeout(e,t)},a=function(){clearTimeout(u)},o=function(){return!1},l=t.unstable_forceFrameRate=function(){}}else{var f=window.performance,h=window.Date,p=window.setTimeout,g=window.clearTimeout;if("undefined"!==typeof console){var m=window.cancelAnimationFrame;"function"!==typeof window.requestAnimationFrame&&console.error("This browser doesn't support requestAnimationFrame. Make sure that you load a polyfill in older browsers. https://fb.me/react-polyfills"),"function"!==typeof m&&console.error("This browser doesn't support cancelAnimationFrame. Make sure that you load a polyfill in older browsers. https://fb.me/react-polyfills")}if("object"===typeof f&&"function"===typeof f.now)t.unstable_now=function(){return f.now()};else{var v=h.now();t.unstable_now=function(){return h.now()-v}}var y=!1,b=null,x=-1,_=5,w=0;o=function(){return t.unstable_now()>=w},l=function(){},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing framerates higher than 125 fps is not unsupported"):_=0<e?Math.floor(1e3/e):5};var k=new MessageChannel,S=k.port2;k.port1.onmessage=function(){if(null!==b){var e=t.unstable_now();w=e+_;try{b(!0,e)?S.postMessage(null):(y=!1,b=null)}catch(n){throw S.postMessage(null),n}}else y=!1},r=function(e){b=e,y||(y=!0,S.postMessage(null))},i=function(e,n){x=p((function(){e(t.unstable_now())}),n)},a=function(){g(x),x=-1}}function T(e,t){var n=e.length;e.push(t);e:for(;;){var r=n-1>>>1,i=e[r];if(!(void 0!==i&&0<P(i,t)))break e;e[r]=t,e[n]=i,n=r}}function M(e){return void 0===(e=e[0])?null:e}function C(e){var t=e[0];if(void 0!==t){var n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,i=e.length;r<i;){var a=2*(r+1)-1,o=e[a],l=a+1,s=e[l];if(void 0!==o&&0>P(o,n))void 0!==s&&0>P(s,o)?(e[r]=s,e[l]=n,r=l):(e[r]=o,e[a]=n,r=a);else{if(!(void 0!==s&&0>P(s,n)))break e;e[r]=s,e[l]=n,r=l}}}return t}return null}function P(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}var D=[],E=[],O=1,N=null,I=3,A=!1,F=!1,R=!1;function L(e){for(var t=M(E);null!==t;){if(null===t.callback)C(E);else{if(!(t.startTime<=e))break;C(E),t.sortIndex=t.expirationTime,T(D,t)}t=M(E)}}function z(e){if(R=!1,L(e),!F)if(null!==M(D))F=!0,r(j);else{var t=M(E);null!==t&&i(z,t.startTime-e)}}function j(e,n){F=!1,R&&(R=!1,a()),A=!0;var r=I;try{for(L(n),N=M(D);null!==N&&(!(N.expirationTime>n)||e&&!o());){var l=N.callback;if(null!==l){N.callback=null,I=N.priorityLevel;var s=l(N.expirationTime<=n);n=t.unstable_now(),"function"===typeof s?N.callback=s:N===M(D)&&C(D),L(n)}else C(D);N=M(D)}if(null!==N)var u=!0;else{var c=M(E);null!==c&&i(z,c.startTime-n),u=!1}return u}finally{N=null,I=r,A=!1}}function W(e){switch(e){case 1:return-1;case 2:return 250;case 5:return 1073741823;case 4:return 1e4;default:return 5e3}}var V=l;t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){F||A||(F=!0,r(j))},t.unstable_getCurrentPriorityLevel=function(){return I},t.unstable_getFirstCallbackNode=function(){return M(D)},t.unstable_next=function(e){switch(I){case 1:case 2:case 3:var t=3;break;default:t=I}var n=I;I=t;try{return e()}finally{I=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=V,t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=I;I=e;try{return t()}finally{I=n}},t.unstable_scheduleCallback=function(e,n,o){var l=t.unstable_now();if("object"===typeof o&&null!==o){var s=o.delay;s="number"===typeof s&&0<s?l+s:l,o="number"===typeof o.timeout?o.timeout:W(e)}else o=W(e),s=l;return e={id:O++,callback:n,priorityLevel:e,startTime:s,expirationTime:o=s+o,sortIndex:-1},s>l?(e.sortIndex=s,T(E,e),null===M(D)&&e===M(E)&&(R?a():R=!0,i(z,s-l))):(e.sortIndex=o,T(D,e),F||A||(F=!0,r(j))),e},t.unstable_shouldYield=function(){var e=t.unstable_now();L(e);var n=M(D);return n!==N&&null!==N&&null!==n&&null!==n.callback&&n.startTime<=e&&n.expirationTime<N.expirationTime||o()},t.unstable_wrapCallback=function(e){var t=I;return function(){var n=I;I=t;try{return e.apply(this,arguments)}finally{I=n}}}},,function(e,t,n){"use strict";var r=n(55);function i(){}function a(){}a.resetWarningCache=i,e.exports=function(){function e(e,t,n,i,a,o){if(o!==r){var l=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw l.name="Invariant Violation",l}}function t(){return e}e.isRequired=e;var n={array:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:a,resetWarningCache:i};return n.PropTypes=n,n}},function(e,t,n){"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},function(e,t,n){"use strict";var r=n(0);e.exports=function(e){return r.createElement("button",{type:"button",className:e.classNames.selectedTag,title:"Click to remove tag",onClick:e.onDelete},r.createElement("span",{className:e.classNames.selectedTagName},e.tag.name))}},function(e,t,n){"use strict";var r=n(0),i={position:"absolute",width:0,height:0,visibility:"hidden",overflow:"scroll",whiteSpace:"pre"},a=["fontSize","fontFamily","fontWeight","fontStyle","letterSpacing","textTransform"],o=function(e){function t(t){e.call(this,t),this.state={inputWidth:null}}return e&&(t.__proto__=e),t.prototype=Object.create(e&&e.prototype),t.prototype.constructor=t,t.prototype.componentDidMount=function(){this.props.autoresize&&(this.copyInputStyles(),this.updateInputWidth()),this.props.autofocus&&this.input.focus()},t.prototype.componentDidUpdate=function(e){var t=e.query,n=e.placeholder;t===this.props.query&&n===this.props.placeholder||this.updateInputWidth()},t.prototype.copyInputStyles=function(){var e=this,t=window.getComputedStyle(this.input);a.forEach((function(n){e.sizer.style[n]=t[n]}))},t.prototype.updateInputWidth=function(){var e;this.props.autoresize&&(e=Math.ceil(this.sizer.scrollWidth)+2),e!==this.state.inputWidth&&this.setState({inputWidth:e})},t.prototype.render=function(){var e=this,t=this.props,n=t.inputAttributes,a=t.inputEventHandlers,o=t.query,l=t.placeholder,s=t.expandable,u=t.listboxId,c=t.selectedIndex;return r.createElement("div",{className:this.props.classNames.searchInput},r.createElement("input",Object.assign({},n,a,{ref:function(t){e.input=t},value:o,placeholder:l,role:"combobox","aria-autocomplete":"list","aria-label":l,"aria-owns":u,"aria-activedescendant":c>-1?u+"-"+c:null,"aria-expanded":s,style:{width:this.state.inputWidth}})),r.createElement("div",{ref:function(t){e.sizer=t},style:i},o||l))},t}(r.Component);e.exports=o},function(e,t,n){"use strict";var r=n(0);function i(e){return e.replace(/[-\\^$*+?.()|[\]{}]/g,"\\$&")}function a(e,t){if(t){var n=RegExp(i(t),"gi");e=e.replace(n,"<mark>$&</mark>")}return{__html:e}}function o(e,t,n,r,a){if(!r){var o=new RegExp("(?:^|\\s)"+i(e),"i");r=function(e){return o.test(e.name)}}var l=t.filter((function(t){return r(t,e)})).slice(0,n);return 0===l.length&&a&&l.push({id:0,name:a,disabled:!0,disableMarkIt:!0}),l}var l=function(e){function t(t){e.call(this,t),this.state={options:o(this.props.query,this.props.suggestions,this.props.maxSuggestionsLength,this.props.suggestionsFilter,this.props.noSuggestionsText)}}return e&&(t.__proto__=e),t.prototype=Object.create(e&&e.prototype),t.prototype.constructor=t,t.prototype.componentWillReceiveProps=function(e){this.setState({options:o(e.query,e.suggestions,e.maxSuggestionsLength,e.suggestionsFilter,e.noSuggestionsText)})},t.prototype.handleMouseDown=function(e,t){t.preventDefault(),this.props.addTag(e)},t.prototype.render=function(){var e=this;if(!this.props.expandable||!this.state.options.length)return null;var t=this.state.options.map((function(t,n){var i=e.props.listboxId+"-"+n,o=[];return e.props.selectedIndex===n&&o.push(e.props.classNames.suggestionActive),t.disabled&&o.push(e.props.classNames.suggestionDisabled),r.createElement("li",{id:i,key:i,role:"option",className:o.join(" "),"aria-disabled":!0===t.disabled,onMouseDown:e.handleMouseDown.bind(e,t)},t.disableMarkIt?t.name:r.createElement("span",{dangerouslySetInnerHTML:a(t.name,e.props.query,t.markInput)}))}));return r.createElement("div",{className:this.props.classNames.suggestions},r.createElement("ul",{role:"listbox",id:this.props.listboxId},t))},t}(r.Component);e.exports=l},function(e,t){var n,r,i=e.exports={};function a(){throw new Error("setTimeout has not been defined")}function o(){throw new Error("clearTimeout has not been defined")}function l(e){if(n===setTimeout)return setTimeout(e,0);if((n===a||!n)&&setTimeout)return n=setTimeout,setTimeout(e,0);try{return n(e,0)}catch(t){try{return n.call(null,e,0)}catch(t){return n.call(this,e,0)}}}!function(){try{n="function"===typeof setTimeout?setTimeout:a}catch(e){n=a}try{r="function"===typeof clearTimeout?clearTimeout:o}catch(e){r=o}}();var s,u=[],c=!1,d=-1;function f(){c&&s&&(c=!1,s.length?u=s.concat(u):d=-1,u.length&&h())}function h(){if(!c){var e=l(f);c=!0;for(var t=u.length;t;){for(s=u,u=[];++d<t;)s&&s[d].run();d=-1,t=u.length}s=null,c=!1,function(e){if(r===clearTimeout)return clearTimeout(e);if((r===o||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(e);try{r(e)}catch(t){try{return r.call(null,e)}catch(t){return r.call(this,e)}}}(e)}}function p(e,t){this.fun=e,this.array=t}function g(){}i.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];u.push(new p(e,t)),1!==u.length||c||l(h)},p.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=g,i.addListener=g,i.once=g,i.off=g,i.removeListener=g,i.removeAllListeners=g,i.emit=g,i.prependListener=g,i.prependOnceListener=g,i.listeners=function(e){return[]},i.binding=function(e){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(e){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},function(e,t,n){e.exports=function(e){"use strict";e=e&&e.hasOwnProperty("default")?e.default:e;var t={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]},n=function(e,t){return e(t={exports:{}},t.exports),t.exports}((function(e){var n={};for(var r in t)t.hasOwnProperty(r)&&(n[t[r]]=r);var i=e.exports={rgb:{channels:3,labels:"rgb"},hsl:{channels:3,labels:"hsl"},hsv:{channels:3,labels:"hsv"},hwb:{channels:3,labels:"hwb"},cmyk:{channels:4,labels:"cmyk"},xyz:{channels:3,labels:"xyz"},lab:{channels:3,labels:"lab"},lch:{channels:3,labels:"lch"},hex:{channels:1,labels:["hex"]},keyword:{channels:1,labels:["keyword"]},ansi16:{channels:1,labels:["ansi16"]},ansi256:{channels:1,labels:["ansi256"]},hcg:{channels:3,labels:["h","c","g"]},apple:{channels:3,labels:["r16","g16","b16"]},gray:{channels:1,labels:["gray"]}};for(var a in i)if(i.hasOwnProperty(a)){if(!("channels"in i[a]))throw new Error("missing channels property: "+a);if(!("labels"in i[a]))throw new Error("missing channel labels property: "+a);if(i[a].labels.length!==i[a].channels)throw new Error("channel and label counts mismatch: "+a);var o=i[a].channels,l=i[a].labels;delete i[a].channels,delete i[a].labels,Object.defineProperty(i[a],"channels",{value:o}),Object.defineProperty(i[a],"labels",{value:l})}i.rgb.hsl=function(e){var t,n,r=e[0]/255,i=e[1]/255,a=e[2]/255,o=Math.min(r,i,a),l=Math.max(r,i,a),s=l-o;return l===o?t=0:r===l?t=(i-a)/s:i===l?t=2+(a-r)/s:a===l&&(t=4+(r-i)/s),(t=Math.min(60*t,360))<0&&(t+=360),n=(o+l)/2,[t,100*(l===o?0:n<=.5?s/(l+o):s/(2-l-o)),100*n]},i.rgb.hsv=function(e){var t,n,r,i,a,o=e[0]/255,l=e[1]/255,s=e[2]/255,u=Math.max(o,l,s),c=u-Math.min(o,l,s),d=function(e){return(u-e)/6/c+.5};return 0===c?i=a=0:(a=c/u,t=d(o),n=d(l),r=d(s),o===u?i=r-n:l===u?i=1/3+t-r:s===u&&(i=2/3+n-t),i<0?i+=1:i>1&&(i-=1)),[360*i,100*a,100*u]},i.rgb.hwb=function(e){var t=e[0],n=e[1],r=e[2];return[i.rgb.hsl(e)[0],1/255*Math.min(t,Math.min(n,r))*100,100*(r=1-1/255*Math.max(t,Math.max(n,r)))]},i.rgb.cmyk=function(e){var t,n=e[0]/255,r=e[1]/255,i=e[2]/255;return[100*((1-n-(t=Math.min(1-n,1-r,1-i)))/(1-t)||0),100*((1-r-t)/(1-t)||0),100*((1-i-t)/(1-t)||0),100*t]},i.rgb.keyword=function(e){var r=n[e];if(r)return r;var i,a,o,l=1/0;for(var s in t)if(t.hasOwnProperty(s)){var u=t[s],c=(a=e,o=u,Math.pow(a[0]-o[0],2)+Math.pow(a[1]-o[1],2)+Math.pow(a[2]-o[2],2));c<l&&(l=c,i=s)}return i},i.keyword.rgb=function(e){return t[e]},i.rgb.xyz=function(e){var t=e[0]/255,n=e[1]/255,r=e[2]/255;return[100*(.4124*(t=t>.04045?Math.pow((t+.055)/1.055,2.4):t/12.92)+.3576*(n=n>.04045?Math.pow((n+.055)/1.055,2.4):n/12.92)+.1805*(r=r>.04045?Math.pow((r+.055)/1.055,2.4):r/12.92)),100*(.2126*t+.7152*n+.0722*r),100*(.0193*t+.1192*n+.9505*r)]},i.rgb.lab=function(e){var t=i.rgb.xyz(e),n=t[0],r=t[1],a=t[2];return r/=100,a/=108.883,n=(n/=95.047)>.008856?Math.pow(n,1/3):7.787*n+16/116,[116*(r=r>.008856?Math.pow(r,1/3):7.787*r+16/116)-16,500*(n-r),200*(r-(a=a>.008856?Math.pow(a,1/3):7.787*a+16/116))]},i.hsl.rgb=function(e){var t,n,r,i,a,o=e[0]/360,l=e[1]/100,s=e[2]/100;if(0===l)return[a=255*s,a,a];t=2*s-(n=s<.5?s*(1+l):s+l-s*l),i=[0,0,0];for(var u=0;u<3;u++)(r=o+1/3*-(u-1))<0&&r++,r>1&&r--,a=6*r<1?t+6*(n-t)*r:2*r<1?n:3*r<2?t+(n-t)*(2/3-r)*6:t,i[u]=255*a;return i},i.hsl.hsv=function(e){var t=e[0],n=e[1]/100,r=e[2]/100,i=n,a=Math.max(r,.01);return n*=(r*=2)<=1?r:2-r,i*=a<=1?a:2-a,[t,100*(0===r?2*i/(a+i):2*n/(r+n)),(r+n)/2*100]},i.hsv.rgb=function(e){var t=e[0]/60,n=e[1]/100,r=e[2]/100,i=Math.floor(t)%6,a=t-Math.floor(t),o=255*r*(1-n),l=255*r*(1-n*a),s=255*r*(1-n*(1-a));switch(r*=255,i){case 0:return[r,s,o];case 1:return[l,r,o];case 2:return[o,r,s];case 3:return[o,l,r];case 4:return[s,o,r];case 5:return[r,o,l]}},i.hsv.hsl=function(e){var t,n,r,i=e[0],a=e[1]/100,o=e[2]/100,l=Math.max(o,.01);return r=(2-a)*o,n=a*l,[i,100*(n=(n/=(t=(2-a)*l)<=1?t:2-t)||0),100*(r/=2)]},i.hwb.rgb=function(e){var t,n,r,i,a,o,l,s=e[0]/360,u=e[1]/100,c=e[2]/100,d=u+c;switch(d>1&&(u/=d,c/=d),r=6*s-(t=Math.floor(6*s)),0!==(1&t)&&(r=1-r),i=u+r*((n=1-c)-u),t){default:case 6:case 0:a=n,o=i,l=u;break;case 1:a=i,o=n,l=u;break;case 2:a=u,o=n,l=i;break;case 3:a=u,o=i,l=n;break;case 4:a=i,o=u,l=n;break;case 5:a=n,o=u,l=i}return[255*a,255*o,255*l]},i.cmyk.rgb=function(e){var t=e[0]/100,n=e[1]/100,r=e[2]/100,i=e[3]/100;return[255*(1-Math.min(1,t*(1-i)+i)),255*(1-Math.min(1,n*(1-i)+i)),255*(1-Math.min(1,r*(1-i)+i))]},i.xyz.rgb=function(e){var t,n,r,i=e[0]/100,a=e[1]/100,o=e[2]/100;return n=-.9689*i+1.8758*a+.0415*o,r=.0557*i+-.204*a+1.057*o,t=(t=3.2406*i+-1.5372*a+-.4986*o)>.0031308?1.055*Math.pow(t,1/2.4)-.055:12.92*t,n=n>.0031308?1.055*Math.pow(n,1/2.4)-.055:12.92*n,r=r>.0031308?1.055*Math.pow(r,1/2.4)-.055:12.92*r,[255*(t=Math.min(Math.max(0,t),1)),255*(n=Math.min(Math.max(0,n),1)),255*(r=Math.min(Math.max(0,r),1))]},i.xyz.lab=function(e){var t=e[0],n=e[1],r=e[2];return n/=100,r/=108.883,t=(t/=95.047)>.008856?Math.pow(t,1/3):7.787*t+16/116,[116*(n=n>.008856?Math.pow(n,1/3):7.787*n+16/116)-16,500*(t-n),200*(n-(r=r>.008856?Math.pow(r,1/3):7.787*r+16/116))]},i.lab.xyz=function(e){var t,n,r,i=e[0];t=e[1]/500+(n=(i+16)/116),r=n-e[2]/200;var a=Math.pow(n,3),o=Math.pow(t,3),l=Math.pow(r,3);return n=a>.008856?a:(n-16/116)/7.787,t=o>.008856?o:(t-16/116)/7.787,r=l>.008856?l:(r-16/116)/7.787,[t*=95.047,n*=100,r*=108.883]},i.lab.lch=function(e){var t,n=e[0],r=e[1],i=e[2];return(t=360*Math.atan2(i,r)/2/Math.PI)<0&&(t+=360),[n,Math.sqrt(r*r+i*i),t]},i.lch.lab=function(e){var t,n=e[0],r=e[1];return t=e[2]/360*2*Math.PI,[n,r*Math.cos(t),r*Math.sin(t)]},i.rgb.ansi16=function(e){var t=e[0],n=e[1],r=e[2],a=1 in arguments?arguments[1]:i.rgb.hsv(e)[2];if(0===(a=Math.round(a/50)))return 30;var o=30+(Math.round(r/255)<<2|Math.round(n/255)<<1|Math.round(t/255));return 2===a&&(o+=60),o},i.hsv.ansi16=function(e){return i.rgb.ansi16(i.hsv.rgb(e),e[2])},i.rgb.ansi256=function(e){var t=e[0],n=e[1],r=e[2];return t===n&&n===r?t<8?16:t>248?231:Math.round((t-8)/247*24)+232:16+36*Math.round(t/255*5)+6*Math.round(n/255*5)+Math.round(r/255*5)},i.ansi16.rgb=function(e){var t=e%10;if(0===t||7===t)return e>50&&(t+=3.5),[t=t/10.5*255,t,t];var n=.5*(1+~~(e>50));return[(1&t)*n*255,(t>>1&1)*n*255,(t>>2&1)*n*255]},i.ansi256.rgb=function(e){if(e>=232){var t=10*(e-232)+8;return[t,t,t]}var n;return e-=16,[Math.floor(e/36)/5*255,Math.floor((n=e%36)/6)/5*255,n%6/5*255]},i.rgb.hex=function(e){var t=(((255&Math.round(e[0]))<<16)+((255&Math.round(e[1]))<<8)+(255&Math.round(e[2]))).toString(16).toUpperCase();return"000000".substring(t.length)+t},i.hex.rgb=function(e){var t=e.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);if(!t)return[0,0,0];var n=t[0];3===t[0].length&&(n=n.split("").map((function(e){return e+e})).join(""));var r=parseInt(n,16);return[r>>16&255,r>>8&255,255&r]},i.rgb.hcg=function(e){var t,n=e[0]/255,r=e[1]/255,i=e[2]/255,a=Math.max(Math.max(n,r),i),o=Math.min(Math.min(n,r),i),l=a-o;return t=l<=0?0:a===n?(r-i)/l%6:a===r?2+(i-n)/l:4+(n-r)/l+4,t/=6,[360*(t%=1),100*l,100*(l<1?o/(1-l):0)]},i.hsl.hcg=function(e){var t=e[1]/100,n=e[2]/100,r=1,i=0;return(r=n<.5?2*t*n:2*t*(1-n))<1&&(i=(n-.5*r)/(1-r)),[e[0],100*r,100*i]},i.hsv.hcg=function(e){var t=e[1]/100,n=e[2]/100,r=t*n,i=0;return r<1&&(i=(n-r)/(1-r)),[e[0],100*r,100*i]},i.hcg.rgb=function(e){var t=e[0]/360,n=e[1]/100,r=e[2]/100;if(0===n)return[255*r,255*r,255*r];var i,a=[0,0,0],o=t%1*6,l=o%1,s=1-l;switch(Math.floor(o)){case 0:a[0]=1,a[1]=l,a[2]=0;break;case 1:a[0]=s,a[1]=1,a[2]=0;break;case 2:a[0]=0,a[1]=1,a[2]=l;break;case 3:a[0]=0,a[1]=s,a[2]=1;break;case 4:a[0]=l,a[1]=0,a[2]=1;break;default:a[0]=1,a[1]=0,a[2]=s}return i=(1-n)*r,[255*(n*a[0]+i),255*(n*a[1]+i),255*(n*a[2]+i)]},i.hcg.hsv=function(e){var t=e[1]/100,n=t+e[2]/100*(1-t),r=0;return n>0&&(r=t/n),[e[0],100*r,100*n]},i.hcg.hsl=function(e){var t=e[1]/100,n=e[2]/100*(1-t)+.5*t,r=0;return n>0&&n<.5?r=t/(2*n):n>=.5&&n<1&&(r=t/(2*(1-n))),[e[0],100*r,100*n]},i.hcg.hwb=function(e){var t=e[1]/100,n=t+e[2]/100*(1-t);return[e[0],100*(n-t),100*(1-n)]},i.hwb.hcg=function(e){var t=e[1]/100,n=1-e[2]/100,r=n-t,i=0;return r<1&&(i=(n-r)/(1-r)),[e[0],100*r,100*i]},i.apple.rgb=function(e){return[e[0]/65535*255,e[1]/65535*255,e[2]/65535*255]},i.rgb.apple=function(e){return[e[0]/255*65535,e[1]/255*65535,e[2]/255*65535]},i.gray.rgb=function(e){return[e[0]/100*255,e[0]/100*255,e[0]/100*255]},i.gray.hsl=i.gray.hsv=function(e){return[0,0,e[0]]},i.gray.hwb=function(e){return[0,100,e[0]]},i.gray.cmyk=function(e){return[0,0,0,e[0]]},i.gray.lab=function(e){return[e[0],0,0]},i.gray.hex=function(e){var t=255&Math.round(e[0]/100*255),n=((t<<16)+(t<<8)+t).toString(16).toUpperCase();return"000000".substring(n.length)+n},i.rgb.gray=function(e){return[(e[0]+e[1]+e[2])/3/255*100]}}));function r(e){var t=function(){for(var e={},t=Object.keys(n),r=t.length,i=0;i<r;i++)e[t[i]]={distance:-1,parent:null};return e}(),r=[e];for(t[e].distance=0;r.length;)for(var i=r.pop(),a=Object.keys(n[i]),o=a.length,l=0;l<o;l++){var s=a[l],u=t[s];-1===u.distance&&(u.distance=t[i].distance+1,u.parent=i,r.unshift(s))}return t}function i(e,t){return function(n){return t(e(n))}}function a(e,t){for(var r=[t[e].parent,e],a=n[t[e].parent][e],o=t[e].parent;t[o].parent;)r.unshift(t[o].parent),a=i(n[t[o].parent][o],a),o=t[o].parent;return a.conversion=r,a}n.rgb,n.hsl,n.hsv,n.hwb,n.cmyk,n.xyz,n.lab,n.lch,n.hex,n.keyword,n.ansi16,n.ansi256,n.hcg,n.apple,n.gray;var o={};Object.keys(n).forEach((function(e){o[e]={},Object.defineProperty(o[e],"channels",{value:n[e].channels}),Object.defineProperty(o[e],"labels",{value:n[e].labels});var t=function(e){for(var t=r(e),n={},i=Object.keys(t),o=i.length,l=0;l<o;l++){var s=i[l];null!==t[s].parent&&(n[s]=a(s,t))}return n}(e);Object.keys(t).forEach((function(n){var r=t[n];o[e][n]=function(e){var t=function(t){if(void 0===t||null===t)return t;arguments.length>1&&(t=Array.prototype.slice.call(arguments));var n=e(t);if("object"===typeof n)for(var r=n.length,i=0;i<r;i++)n[i]=Math.round(n[i]);return n};return"conversion"in e&&(t.conversion=e.conversion),t}(r),o[e][n].raw=function(e){var t=function(t){return void 0===t||null===t?t:(arguments.length>1&&(t=Array.prototype.slice.call(arguments)),e(t))};return"conversion"in e&&(t.conversion=e.conversion),t}(r)}))}));var l=o,s={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]},u={getRgba:c,getHsla:d,getRgb:function(e){var t=c(e);return t&&t.slice(0,3)},getHsl:function(e){var t=d(e);return t&&t.slice(0,3)},getHwb:f,getAlpha:function(e){var t=c(e);return t||(t=d(e))||(t=f(e))?t[3]:void 0},hexString:function(e,t){return t=void 0!==t&&3===e.length?t:e[3],"#"+v(e[0])+v(e[1])+v(e[2])+(t>=0&&t<1?v(Math.round(255*t)):"")},rgbString:function(e,t){return t<1||e[3]&&e[3]<1?h(e,t):"rgb("+e[0]+", "+e[1]+", "+e[2]+")"},rgbaString:h,percentString:function(e,t){if(t<1||e[3]&&e[3]<1)return p(e,t);var n=Math.round(e[0]/255*100),r=Math.round(e[1]/255*100),i=Math.round(e[2]/255*100);return"rgb("+n+"%, "+r+"%, "+i+"%)"},percentaString:p,hslString:function(e,t){return t<1||e[3]&&e[3]<1?g(e,t):"hsl("+e[0]+", "+e[1]+"%, "+e[2]+"%)"},hslaString:g,hwbString:function(e,t){return void 0===t&&(t=void 0!==e[3]?e[3]:1),"hwb("+e[0]+", "+e[1]+"%, "+e[2]+"%"+(void 0!==t&&1!==t?", "+t:"")+")"},keyword:function(e){return y[e.slice(0,3)]}};function c(e){if(e){var t=[0,0,0],n=1,r=e.match(/^#([a-fA-F0-9]{3,4})$/i),i="";if(r){i=(r=r[1])[3];for(var a=0;a<t.length;a++)t[a]=parseInt(r[a]+r[a],16);i&&(n=Math.round(parseInt(i+i,16)/255*100)/100)}else if(r=e.match(/^#([a-fA-F0-9]{6}([a-fA-F0-9]{2})?)$/i)){for(i=r[2],r=r[1],a=0;a<t.length;a++)t[a]=parseInt(r.slice(2*a,2*a+2),16);i&&(n=Math.round(parseInt(i,16)/255*100)/100)}else if(r=e.match(/^rgba?\(\s*([+-]?\d+)\s*,\s*([+-]?\d+)\s*,\s*([+-]?\d+)\s*(?:,\s*([+-]?[\d\.]+)\s*)?\)$/i)){for(a=0;a<t.length;a++)t[a]=parseInt(r[a+1]);n=parseFloat(r[4])}else if(r=e.match(/^rgba?\(\s*([+-]?[\d\.]+)\%\s*,\s*([+-]?[\d\.]+)\%\s*,\s*([+-]?[\d\.]+)\%\s*(?:,\s*([+-]?[\d\.]+)\s*)?\)$/i)){for(a=0;a<t.length;a++)t[a]=Math.round(2.55*parseFloat(r[a+1]));n=parseFloat(r[4])}else if(r=e.match(/(\w+)/)){if("transparent"==r[1])return[0,0,0,0];if(!(t=s[r[1]]))return}for(a=0;a<t.length;a++)t[a]=m(t[a],0,255);return n=n||0==n?m(n,0,1):1,t[3]=n,t}}function d(e){if(e){var t=e.match(/^hsla?\(\s*([+-]?\d+)(?:deg)?\s*,\s*([+-]?[\d\.]+)%\s*,\s*([+-]?[\d\.]+)%\s*(?:,\s*([+-]?[\d\.]+)\s*)?\)/);if(t){var n=parseFloat(t[4]);return[m(parseInt(t[1]),0,360),m(parseFloat(t[2]),0,100),m(parseFloat(t[3]),0,100),m(isNaN(n)?1:n,0,1)]}}}function f(e){if(e){var t=e.match(/^hwb\(\s*([+-]?\d+)(?:deg)?\s*,\s*([+-]?[\d\.]+)%\s*,\s*([+-]?[\d\.]+)%\s*(?:,\s*([+-]?[\d\.]+)\s*)?\)/);if(t){var n=parseFloat(t[4]);return[m(parseInt(t[1]),0,360),m(parseFloat(t[2]),0,100),m(parseFloat(t[3]),0,100),m(isNaN(n)?1:n,0,1)]}}}function h(e,t){return void 0===t&&(t=void 0!==e[3]?e[3]:1),"rgba("+e[0]+", "+e[1]+", "+e[2]+", "+t+")"}function p(e,t){return"rgba("+Math.round(e[0]/255*100)+"%, "+Math.round(e[1]/255*100)+"%, "+Math.round(e[2]/255*100)+"%, "+(t||e[3]||1)+")"}function g(e,t){return void 0===t&&(t=void 0!==e[3]?e[3]:1),"hsla("+e[0]+", "+e[1]+"%, "+e[2]+"%, "+t+")"}function m(e,t,n){return Math.min(Math.max(t,e),n)}function v(e){var t=e.toString(16).toUpperCase();return t.length<2?"0"+t:t}var y={};for(var b in s)y[s[b]]=b;var x=function e(t){return t instanceof e?t:this instanceof e?(this.valid=!1,this.values={rgb:[0,0,0],hsl:[0,0,0],hsv:[0,0,0],hwb:[0,0,0],cmyk:[0,0,0,0],alpha:1},void("string"===typeof t?(n=u.getRgba(t))?this.setValues("rgb",n):(n=u.getHsla(t))?this.setValues("hsl",n):(n=u.getHwb(t))&&this.setValues("hwb",n):"object"===typeof t&&(void 0!==(n=t).r||void 0!==n.red?this.setValues("rgb",n):void 0!==n.l||void 0!==n.lightness?this.setValues("hsl",n):void 0!==n.v||void 0!==n.value?this.setValues("hsv",n):void 0!==n.w||void 0!==n.whiteness?this.setValues("hwb",n):void 0===n.c&&void 0===n.cyan||this.setValues("cmyk",n)))):new e(t);var n};(x.prototype={isValid:function(){return this.valid},rgb:function(){return this.setSpace("rgb",arguments)},hsl:function(){return this.setSpace("hsl",arguments)},hsv:function(){return this.setSpace("hsv",arguments)},hwb:function(){return this.setSpace("hwb",arguments)},cmyk:function(){return this.setSpace("cmyk",arguments)},rgbArray:function(){return this.values.rgb},hslArray:function(){return this.values.hsl},hsvArray:function(){return this.values.hsv},hwbArray:function(){var e=this.values;return 1!==e.alpha?e.hwb.concat([e.alpha]):e.hwb},cmykArray:function(){return this.values.cmyk},rgbaArray:function(){var e=this.values;return e.rgb.concat([e.alpha])},hslaArray:function(){var e=this.values;return e.hsl.concat([e.alpha])},alpha:function(e){return void 0===e?this.values.alpha:(this.setValues("alpha",e),this)},red:function(e){return this.setChannel("rgb",0,e)},green:function(e){return this.setChannel("rgb",1,e)},blue:function(e){return this.setChannel("rgb",2,e)},hue:function(e){return e&&(e=(e%=360)<0?360+e:e),this.setChannel("hsl",0,e)},saturation:function(e){return this.setChannel("hsl",1,e)},lightness:function(e){return this.setChannel("hsl",2,e)},saturationv:function(e){return this.setChannel("hsv",1,e)},whiteness:function(e){return this.setChannel("hwb",1,e)},blackness:function(e){return this.setChannel("hwb",2,e)},value:function(e){return this.setChannel("hsv",2,e)},cyan:function(e){return this.setChannel("cmyk",0,e)},magenta:function(e){return this.setChannel("cmyk",1,e)},yellow:function(e){return this.setChannel("cmyk",2,e)},black:function(e){return this.setChannel("cmyk",3,e)},hexString:function(){return u.hexString(this.values.rgb)},rgbString:function(){return u.rgbString(this.values.rgb,this.values.alpha)},rgbaString:function(){return u.rgbaString(this.values.rgb,this.values.alpha)},percentString:function(){return u.percentString(this.values.rgb,this.values.alpha)},hslString:function(){return u.hslString(this.values.hsl,this.values.alpha)},hslaString:function(){return u.hslaString(this.values.hsl,this.values.alpha)},hwbString:function(){return u.hwbString(this.values.hwb,this.values.alpha)},keyword:function(){return u.keyword(this.values.rgb,this.values.alpha)},rgbNumber:function(){var e=this.values.rgb;return e[0]<<16|e[1]<<8|e[2]},luminosity:function(){for(var e=this.values.rgb,t=[],n=0;n<e.length;n++){var r=e[n]/255;t[n]=r<=.03928?r/12.92:Math.pow((r+.055)/1.055,2.4)}return.2126*t[0]+.7152*t[1]+.0722*t[2]},contrast:function(e){var t=this.luminosity(),n=e.luminosity();return t>n?(t+.05)/(n+.05):(n+.05)/(t+.05)},level:function(e){var t=this.contrast(e);return t>=7.1?"AAA":t>=4.5?"AA":""},dark:function(){var e=this.values.rgb;return(299*e[0]+587*e[1]+114*e[2])/1e3<128},light:function(){return!this.dark()},negate:function(){for(var e=[],t=0;t<3;t++)e[t]=255-this.values.rgb[t];return this.setValues("rgb",e),this},lighten:function(e){var t=this.values.hsl;return t[2]+=t[2]*e,this.setValues("hsl",t),this},darken:function(e){var t=this.values.hsl;return t[2]-=t[2]*e,this.setValues("hsl",t),this},saturate:function(e){var t=this.values.hsl;return t[1]+=t[1]*e,this.setValues("hsl",t),this},desaturate:function(e){var t=this.values.hsl;return t[1]-=t[1]*e,this.setValues("hsl",t),this},whiten:function(e){var t=this.values.hwb;return t[1]+=t[1]*e,this.setValues("hwb",t),this},blacken:function(e){var t=this.values.hwb;return t[2]+=t[2]*e,this.setValues("hwb",t),this},greyscale:function(){var e=this.values.rgb,t=.3*e[0]+.59*e[1]+.11*e[2];return this.setValues("rgb",[t,t,t]),this},clearer:function(e){var t=this.values.alpha;return this.setValues("alpha",t-t*e),this},opaquer:function(e){var t=this.values.alpha;return this.setValues("alpha",t+t*e),this},rotate:function(e){var t=this.values.hsl,n=(t[0]+e)%360;return t[0]=n<0?360+n:n,this.setValues("hsl",t),this},mix:function(e,t){var n=e,r=void 0===t?.5:t,i=2*r-1,a=this.alpha()-n.alpha(),o=((i*a===-1?i:(i+a)/(1+i*a))+1)/2,l=1-o;return this.rgb(o*this.red()+l*n.red(),o*this.green()+l*n.green(),o*this.blue()+l*n.blue()).alpha(this.alpha()*r+n.alpha()*(1-r))},toJSON:function(){return this.rgb()},clone:function(){var e,t,n=new x,r=this.values,i=n.values;for(var a in r)r.hasOwnProperty(a)&&(e=r[a],"[object Array]"===(t={}.toString.call(e))?i[a]=e.slice(0):"[object Number]"===t?i[a]=e:console.error("unexpected color value:",e));return n}}).spaces={rgb:["red","green","blue"],hsl:["hue","saturation","lightness"],hsv:["hue","saturation","value"],hwb:["hue","whiteness","blackness"],cmyk:["cyan","magenta","yellow","black"]},x.prototype.maxes={rgb:[255,255,255],hsl:[360,100,100],hsv:[360,100,100],hwb:[360,100,100],cmyk:[100,100,100,100]},x.prototype.getValues=function(e){for(var t=this.values,n={},r=0;r<e.length;r++)n[e.charAt(r)]=t[e][r];return 1!==t.alpha&&(n.a=t.alpha),n},x.prototype.setValues=function(e,t){var n,r,i=this.values,a=this.spaces,o=this.maxes,s=1;if(this.valid=!0,"alpha"===e)s=t;else if(t.length)i[e]=t.slice(0,e.length),s=t[e.length];else if(void 0!==t[e.charAt(0)]){for(n=0;n<e.length;n++)i[e][n]=t[e.charAt(n)];s=t.a}else if(void 0!==t[a[e][0]]){var u=a[e];for(n=0;n<e.length;n++)i[e][n]=t[u[n]];s=t.alpha}if(i.alpha=Math.max(0,Math.min(1,void 0===s?i.alpha:s)),"alpha"===e)return!1;for(n=0;n<e.length;n++)r=Math.max(0,Math.min(o[e][n],i[e][n])),i[e][n]=Math.round(r);for(var c in a)c!==e&&(i[c]=l[e][c](i[e]));return!0},x.prototype.setSpace=function(e,t){var n=t[0];return void 0===n?this.getValues(e):("number"===typeof n&&(n=Array.prototype.slice.call(t)),this.setValues(e,n),this)},x.prototype.setChannel=function(e,t,n){var r=this.values[e];return void 0===n?r[t]:(n===r[t]||(r[t]=n,this.setValues(e,r)),this)},"undefined"!==typeof window&&(window.Color=x);var _=x;function w(e){return-1===["__proto__","prototype","constructor"].indexOf(e)}var k={noop:function(){},uid:function(){var e=0;return function(){return e++}}(),isNullOrUndef:function(e){return null===e||"undefined"===typeof e},isArray:function(e){if(Array.isArray&&Array.isArray(e))return!0;var t=Object.prototype.toString.call(e);return"[object"===t.substr(0,7)&&"Array]"===t.substr(-6)},isObject:function(e){return null!==e&&"[object Object]"===Object.prototype.toString.call(e)},isFinite:function(e){function t(t){return e.apply(this,arguments)}return t.toString=function(){return e.toString()},t}((function(e){return("number"===typeof e||e instanceof Number)&&isFinite(e)})),valueOrDefault:function(e,t){return"undefined"===typeof e?t:e},valueAtIndexOrDefault:function(e,t,n){return k.valueOrDefault(k.isArray(e)?e[t]:e,n)},callback:function(e,t,n){if(e&&"function"===typeof e.call)return e.apply(n,t)},each:function(e,t,n,r){var i,a,o;if(k.isArray(e))if(a=e.length,r)for(i=a-1;i>=0;i--)t.call(n,e[i],i);else for(i=0;i<a;i++)t.call(n,e[i],i);else if(k.isObject(e))for(a=(o=Object.keys(e)).length,i=0;i<a;i++)t.call(n,e[o[i]],o[i])},arrayEquals:function(e,t){var n,r,i,a;if(!e||!t||e.length!==t.length)return!1;for(n=0,r=e.length;n<r;++n)if(i=e[n],a=t[n],i instanceof Array&&a instanceof Array){if(!k.arrayEquals(i,a))return!1}else if(i!==a)return!1;return!0},clone:function(e){if(k.isArray(e))return e.map(k.clone);if(k.isObject(e)){for(var t=Object.create(e),n=Object.keys(e),r=n.length,i=0;i<r;++i)t[n[i]]=k.clone(e[n[i]]);return t}return e},_merger:function(e,t,n,r){if(w(e)){var i=t[e],a=n[e];k.isObject(i)&&k.isObject(a)?k.merge(i,a,r):t[e]=k.clone(a)}},_mergerIf:function(e,t,n){if(w(e)){var r=t[e],i=n[e];k.isObject(r)&&k.isObject(i)?k.mergeIf(r,i):t.hasOwnProperty(e)||(t[e]=k.clone(i))}},merge:function(e,t,n){var r,i,a,o,l,s=k.isArray(t)?t:[t],u=s.length;if(!k.isObject(e))return e;for(r=(n=n||{}).merger||k._merger,i=0;i<u;++i)if(t=s[i],k.isObject(t))for(l=0,o=(a=Object.keys(t)).length;l<o;++l)r(a[l],e,t,n);return e},mergeIf:function(e,t){return k.merge(e,t,{merger:k._mergerIf})},extend:Object.assign||function(e){return k.merge(e,[].slice.call(arguments,1),{merger:function(e,t,n){t[e]=n[e]}})},inherits:function(e){var t=this,n=e&&e.hasOwnProperty("constructor")?e.constructor:function(){return t.apply(this,arguments)},r=function(){this.constructor=n};return r.prototype=t.prototype,n.prototype=new r,n.extend=k.inherits,e&&k.extend(n.prototype,e),n.__super__=t.prototype,n},_deprecated:function(e,t,n,r){void 0!==t&&console.warn(e+': "'+n+'" is deprecated. Please use "'+r+'" instead')}},S=k;k.callCallback=k.callback,k.indexOf=function(e,t,n){return Array.prototype.indexOf.call(e,t,n)},k.getValueOrDefault=k.valueOrDefault,k.getValueAtIndexOrDefault=k.valueAtIndexOrDefault;var T={linear:function(e){return e},easeInQuad:function(e){return e*e},easeOutQuad:function(e){return-e*(e-2)},easeInOutQuad:function(e){return(e/=.5)<1?.5*e*e:-.5*(--e*(e-2)-1)},easeInCubic:function(e){return e*e*e},easeOutCubic:function(e){return(e-=1)*e*e+1},easeInOutCubic:function(e){return(e/=.5)<1?.5*e*e*e:.5*((e-=2)*e*e+2)},easeInQuart:function(e){return e*e*e*e},easeOutQuart:function(e){return-((e-=1)*e*e*e-1)},easeInOutQuart:function(e){return(e/=.5)<1?.5*e*e*e*e:-.5*((e-=2)*e*e*e-2)},easeInQuint:function(e){return e*e*e*e*e},easeOutQuint:function(e){return(e-=1)*e*e*e*e+1},easeInOutQuint:function(e){return(e/=.5)<1?.5*e*e*e*e*e:.5*((e-=2)*e*e*e*e+2)},easeInSine:function(e){return 1-Math.cos(e*(Math.PI/2))},easeOutSine:function(e){return Math.sin(e*(Math.PI/2))},easeInOutSine:function(e){return-.5*(Math.cos(Math.PI*e)-1)},easeInExpo:function(e){return 0===e?0:Math.pow(2,10*(e-1))},easeOutExpo:function(e){return 1===e?1:1-Math.pow(2,-10*e)},easeInOutExpo:function(e){return 0===e?0:1===e?1:(e/=.5)<1?.5*Math.pow(2,10*(e-1)):.5*(2-Math.pow(2,-10*--e))},easeInCirc:function(e){return e>=1?e:-(Math.sqrt(1-e*e)-1)},easeOutCirc:function(e){return Math.sqrt(1-(e-=1)*e)},easeInOutCirc:function(e){return(e/=.5)<1?-.5*(Math.sqrt(1-e*e)-1):.5*(Math.sqrt(1-(e-=2)*e)+1)},easeInElastic:function(e){var t=1.70158,n=0,r=1;return 0===e?0:1===e?1:(n||(n=.3),r<1?(r=1,t=n/4):t=n/(2*Math.PI)*Math.asin(1/r),-r*Math.pow(2,10*(e-=1))*Math.sin((e-t)*(2*Math.PI)/n))},easeOutElastic:function(e){var t=1.70158,n=0,r=1;return 0===e?0:1===e?1:(n||(n=.3),r<1?(r=1,t=n/4):t=n/(2*Math.PI)*Math.asin(1/r),r*Math.pow(2,-10*e)*Math.sin((e-t)*(2*Math.PI)/n)+1)},easeInOutElastic:function(e){var t=1.70158,n=0,r=1;return 0===e?0:2===(e/=.5)?1:(n||(n=.45),r<1?(r=1,t=n/4):t=n/(2*Math.PI)*Math.asin(1/r),e<1?r*Math.pow(2,10*(e-=1))*Math.sin((e-t)*(2*Math.PI)/n)*-.5:r*Math.pow(2,-10*(e-=1))*Math.sin((e-t)*(2*Math.PI)/n)*.5+1)},easeInBack:function(e){var t=1.70158;return e*e*((t+1)*e-t)},easeOutBack:function(e){var t=1.70158;return(e-=1)*e*((t+1)*e+t)+1},easeInOutBack:function(e){var t=1.70158;return(e/=.5)<1?e*e*((1+(t*=1.525))*e-t)*.5:.5*((e-=2)*e*((1+(t*=1.525))*e+t)+2)},easeInBounce:function(e){return 1-T.easeOutBounce(1-e)},easeOutBounce:function(e){return e<1/2.75?7.5625*e*e:e<2/2.75?7.5625*(e-=1.5/2.75)*e+.75:e<2.5/2.75?7.5625*(e-=2.25/2.75)*e+.9375:7.5625*(e-=2.625/2.75)*e+.984375},easeInOutBounce:function(e){return e<.5?.5*T.easeInBounce(2*e):.5*T.easeOutBounce(2*e-1)+.5}},M={effects:T};S.easingEffects=T;var C=Math.PI,P=C/180,D=2*C,E=C/2,O=C/4,N=2*C/3,I={clear:function(e){e.ctx.clearRect(0,0,e.width,e.height)},roundedRect:function(e,t,n,r,i,a){if(a){var o=Math.min(a,i/2,r/2),l=t+o,s=n+o,u=t+r-o,c=n+i-o;e.moveTo(t,s),l<u&&s<c?(e.arc(l,s,o,-C,-E),e.arc(u,s,o,-E,0),e.arc(u,c,o,0,E),e.arc(l,c,o,E,C)):l<u?(e.moveTo(l,n),e.arc(u,s,o,-E,E),e.arc(l,s,o,E,C+E)):s<c?(e.arc(l,s,o,-C,0),e.arc(l,c,o,0,C)):e.arc(l,s,o,-C,C),e.closePath(),e.moveTo(t,n)}else e.rect(t,n,r,i)},drawPoint:function(e,t,n,r,i,a){var o,l,s,u,c,d=(a||0)*P;if(t&&"object"===typeof t&&("[object HTMLImageElement]"===(o=t.toString())||"[object HTMLCanvasElement]"===o))return e.save(),e.translate(r,i),e.rotate(d),e.drawImage(t,-t.width/2,-t.height/2,t.width,t.height),void e.restore();if(!(isNaN(n)||n<=0)){switch(e.beginPath(),t){default:e.arc(r,i,n,0,D),e.closePath();break;case"triangle":e.moveTo(r+Math.sin(d)*n,i-Math.cos(d)*n),d+=N,e.lineTo(r+Math.sin(d)*n,i-Math.cos(d)*n),d+=N,e.lineTo(r+Math.sin(d)*n,i-Math.cos(d)*n),e.closePath();break;case"rectRounded":u=n-(c=.516*n),l=Math.cos(d+O)*u,s=Math.sin(d+O)*u,e.arc(r-l,i-s,c,d-C,d-E),e.arc(r+s,i-l,c,d-E,d),e.arc(r+l,i+s,c,d,d+E),e.arc(r-s,i+l,c,d+E,d+C),e.closePath();break;case"rect":if(!a){u=Math.SQRT1_2*n,e.rect(r-u,i-u,2*u,2*u);break}d+=O;case"rectRot":l=Math.cos(d)*n,s=Math.sin(d)*n,e.moveTo(r-l,i-s),e.lineTo(r+s,i-l),e.lineTo(r+l,i+s),e.lineTo(r-s,i+l),e.closePath();break;case"crossRot":d+=O;case"cross":l=Math.cos(d)*n,s=Math.sin(d)*n,e.moveTo(r-l,i-s),e.lineTo(r+l,i+s),e.moveTo(r+s,i-l),e.lineTo(r-s,i+l);break;case"star":l=Math.cos(d)*n,s=Math.sin(d)*n,e.moveTo(r-l,i-s),e.lineTo(r+l,i+s),e.moveTo(r+s,i-l),e.lineTo(r-s,i+l),d+=O,l=Math.cos(d)*n,s=Math.sin(d)*n,e.moveTo(r-l,i-s),e.lineTo(r+l,i+s),e.moveTo(r+s,i-l),e.lineTo(r-s,i+l);break;case"line":l=Math.cos(d)*n,s=Math.sin(d)*n,e.moveTo(r-l,i-s),e.lineTo(r+l,i+s);break;case"dash":e.moveTo(r,i),e.lineTo(r+Math.cos(d)*n,i+Math.sin(d)*n)}e.fill(),e.stroke()}},_isPointInArea:function(e,t){return e.x>t.left-1e-6&&e.x<t.right+1e-6&&e.y>t.top-1e-6&&e.y<t.bottom+1e-6},clipArea:function(e,t){e.save(),e.beginPath(),e.rect(t.left,t.top,t.right-t.left,t.bottom-t.top),e.clip()},unclipArea:function(e){e.restore()},lineTo:function(e,t,n,r){var i=n.steppedLine;if(i){if("middle"===i){var a=(t.x+n.x)/2;e.lineTo(a,r?n.y:t.y),e.lineTo(a,r?t.y:n.y)}else"after"===i&&!r||"after"!==i&&r?e.lineTo(t.x,n.y):e.lineTo(n.x,t.y);e.lineTo(n.x,n.y)}else n.tension?e.bezierCurveTo(r?t.controlPointPreviousX:t.controlPointNextX,r?t.controlPointPreviousY:t.controlPointNextY,r?n.controlPointNextX:n.controlPointPreviousX,r?n.controlPointNextY:n.controlPointPreviousY,n.x,n.y):e.lineTo(n.x,n.y)}},A=I;S.clear=I.clear,S.drawRoundedRectangle=function(e){e.beginPath(),I.roundedRect.apply(I,arguments)};var F={_set:function(e,t){return S.merge(this[e]||(this[e]={}),t)}};F._set("global",{defaultColor:"rgba(0,0,0,0.1)",defaultFontColor:"#666",defaultFontFamily:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",defaultFontSize:12,defaultFontStyle:"normal",defaultLineHeight:1.2,showLines:!0});var R=F,L=S.valueOrDefault,z={toLineHeight:function(e,t){var n=(""+e).match(/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/);if(!n||"normal"===n[1])return 1.2*t;switch(e=+n[2],n[3]){case"px":return e;case"%":e/=100}return t*e},toPadding:function(e){var t,n,r,i;return S.isObject(e)?(t=+e.top||0,n=+e.right||0,r=+e.bottom||0,i=+e.left||0):t=n=r=i=+e||0,{top:t,right:n,bottom:r,left:i,height:t+r,width:i+n}},_parseFont:function(e){var t=R.global,n=L(e.fontSize,t.defaultFontSize),r={family:L(e.fontFamily,t.defaultFontFamily),lineHeight:S.options.toLineHeight(L(e.lineHeight,t.defaultLineHeight),n),size:n,style:L(e.fontStyle,t.defaultFontStyle),weight:null,string:""};return r.string=function(e){return!e||S.isNullOrUndef(e.size)||S.isNullOrUndef(e.family)?null:(e.style?e.style+" ":"")+(e.weight?e.weight+" ":"")+e.size+"px "+e.family}(r),r},resolve:function(e,t,n,r){var i,a,o,l=!0;for(i=0,a=e.length;i<a;++i)if(void 0!==(o=e[i])&&(void 0!==t&&"function"===typeof o&&(o=o(t),l=!1),void 0!==n&&S.isArray(o)&&(o=o[n],l=!1),void 0!==o))return r&&!l&&(r.cacheable=!1),o}},j={_factorize:function(e){var t,n=[],r=Math.sqrt(e);for(t=1;t<r;t++)e%t===0&&(n.push(t),n.push(e/t));return r===(0|r)&&n.push(r),n.sort((function(e,t){return e-t})).pop(),n},log10:Math.log10||function(e){var t=Math.log(e)*Math.LOG10E,n=Math.round(t);return e===Math.pow(10,n)?n:t}},W=j;S.log10=j.log10;var V=S,Y=M,B=A,H=z,U=W,q={getRtlAdapter:function(e,t,n){return e?function(e,t){return{x:function(n){return e+e+t-n},setWidth:function(e){t=e},textAlign:function(e){return"center"===e?e:"right"===e?"left":"right"},xPlus:function(e,t){return e-t},leftForLtr:function(e,t){return e-t}}}(t,n):{x:function(e){return e},setWidth:function(e){},textAlign:function(e){return e},xPlus:function(e,t){return e+t},leftForLtr:function(e,t){return e}}},overrideTextDirection:function(e,t){var n,r;"ltr"!==t&&"rtl"!==t||(r=[(n=e.canvas.style).getPropertyValue("direction"),n.getPropertyPriority("direction")],n.setProperty("direction",t,"important"),e.prevTextDirection=r)},restoreTextDirection:function(e){var t=e.prevTextDirection;void 0!==t&&(delete e.prevTextDirection,e.canvas.style.setProperty("direction",t[0],t[1]))}};V.easing=Y,V.canvas=B,V.options=H,V.math=U,V.rtl=q;var $=function(e){V.extend(this,e),this.initialize.apply(this,arguments)};V.extend($.prototype,{_type:void 0,initialize:function(){this.hidden=!1},pivot:function(){var e=this;return e._view||(e._view=V.extend({},e._model)),e._start={},e},transition:function(e){var t=this,n=t._model,r=t._start,i=t._view;return n&&1!==e?(i||(i=t._view={}),r||(r=t._start={}),function(e,t,n,r){var i,a,o,l,s,u,c,d,f,h=Object.keys(n);for(i=0,a=h.length;i<a;++i)if(u=n[o=h[i]],t.hasOwnProperty(o)||(t[o]=u),(l=t[o])!==u&&"_"!==o[0]){if(e.hasOwnProperty(o)||(e[o]=l),(c=typeof u)===typeof(s=e[o]))if("string"===c){if((d=_(s)).valid&&(f=_(u)).valid){t[o]=f.mix(d,r).rgbString();continue}}else if(V.isFinite(s)&&V.isFinite(u)){t[o]=s+(u-s)*r;continue}t[o]=u}}(r,i,n,e),t):(t._view=V.extend({},n),t._start=null,t)},tooltipPosition:function(){return{x:this._model.x,y:this._model.y}},hasValue:function(){return V.isNumber(this._model.x)&&V.isNumber(this._model.y)}}),$.extend=V.inherits;var G=$,Q=G.extend({chart:null,currentStep:0,numSteps:60,easing:"",render:null,onAnimationProgress:null,onAnimationComplete:null}),K=Q;Object.defineProperty(Q.prototype,"animationObject",{get:function(){return this}}),Object.defineProperty(Q.prototype,"chartInstance",{get:function(){return this.chart},set:function(e){this.chart=e}}),R._set("global",{animation:{duration:1e3,easing:"easeOutQuart",onProgress:V.noop,onComplete:V.noop}});var Z={animations:[],request:null,addAnimation:function(e,t,n,r){var i,a,o=this.animations;for(t.chart=e,t.startTime=Date.now(),t.duration=n,r||(e.animating=!0),i=0,a=o.length;i<a;++i)if(o[i].chart===e)return void(o[i]=t);o.push(t),1===o.length&&this.requestAnimationFrame()},cancelAnimation:function(e){var t=V.findIndex(this.animations,(function(t){return t.chart===e}));-1!==t&&(this.animations.splice(t,1),e.animating=!1)},requestAnimationFrame:function(){var e=this;null===e.request&&(e.request=V.requestAnimFrame.call(window,(function(){e.request=null,e.startDigest()})))},startDigest:function(){this.advance(),this.animations.length>0&&this.requestAnimationFrame()},advance:function(){for(var e,t,n,r,i=this.animations,a=0;a<i.length;)t=(e=i[a]).chart,n=e.numSteps,r=Math.floor((Date.now()-e.startTime)/e.duration*n)+1,e.currentStep=Math.min(r,n),V.callback(e.render,[t,e],t),V.callback(e.onAnimationProgress,[e],t),e.currentStep>=n?(V.callback(e.onAnimationComplete,[e],t),t.animating=!1,i.splice(a,1)):++a}},X=V.options.resolve,J=["push","pop","shift","splice","unshift"];function ee(e,t){var n=e._chartjs;if(n){var r=n.listeners,i=r.indexOf(t);-1!==i&&r.splice(i,1),r.length>0||(J.forEach((function(t){delete e[t]})),delete e._chartjs)}}var te=function(e,t){this.initialize(e,t)};V.extend(te.prototype,{datasetElementType:null,dataElementType:null,_datasetElementOptions:["backgroundColor","borderCapStyle","borderColor","borderDash","borderDashOffset","borderJoinStyle","borderWidth"],_dataElementOptions:["backgroundColor","borderColor","borderWidth","pointStyle"],initialize:function(e,t){var n=this;n.chart=e,n.index=t,n.linkScales(),n.addElements(),n._type=n.getMeta().type},updateIndex:function(e){this.index=e},linkScales:function(){var e=this.getMeta(),t=this.chart,n=t.scales,r=this.getDataset(),i=t.options.scales;null!==e.xAxisID&&e.xAxisID in n&&!r.xAxisID||(e.xAxisID=r.xAxisID||i.xAxes[0].id),null!==e.yAxisID&&e.yAxisID in n&&!r.yAxisID||(e.yAxisID=r.yAxisID||i.yAxes[0].id)},getDataset:function(){return this.chart.data.datasets[this.index]},getMeta:function(){return this.chart.getDatasetMeta(this.index)},getScaleForId:function(e){return this.chart.scales[e]},_getValueScaleId:function(){return this.getMeta().yAxisID},_getIndexScaleId:function(){return this.getMeta().xAxisID},_getValueScale:function(){return this.getScaleForId(this._getValueScaleId())},_getIndexScale:function(){return this.getScaleForId(this._getIndexScaleId())},reset:function(){this._update(!0)},destroy:function(){this._data&&ee(this._data,this)},createMetaDataset:function(){var e=this.datasetElementType;return e&&new e({_chart:this.chart,_datasetIndex:this.index})},createMetaData:function(e){var t=this.dataElementType;return t&&new t({_chart:this.chart,_datasetIndex:this.index,_index:e})},addElements:function(){var e,t,n=this.getMeta(),r=this.getDataset().data||[],i=n.data;for(e=0,t=r.length;e<t;++e)i[e]=i[e]||this.createMetaData(e);n.dataset=n.dataset||this.createMetaDataset()},addElementAndReset:function(e){var t=this.createMetaData(e);this.getMeta().data.splice(e,0,t),this.updateElement(t,e,!0)},buildOrUpdateElements:function(){var e,t,n=this,r=n.getDataset(),i=r.data||(r.data=[]);n._data!==i&&(n._data&&ee(n._data,n),i&&Object.isExtensible(i)&&(t=n,(e=i)._chartjs?e._chartjs.listeners.push(t):(Object.defineProperty(e,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[t]}}),J.forEach((function(t){var n="onData"+t.charAt(0).toUpperCase()+t.slice(1),r=e[t];Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:function(){var t=Array.prototype.slice.call(arguments),i=r.apply(this,t);return V.each(e._chartjs.listeners,(function(e){"function"===typeof e[n]&&e[n].apply(e,t)})),i}})})))),n._data=i),n.resyncElements()},_configure:function(){this._config=V.merge(Object.create(null),[this.chart.options.datasets[this._type],this.getDataset()],{merger:function(e,t,n){"_meta"!==e&&"data"!==e&&V._merger(e,t,n)}})},_update:function(e){this._configure(),this._cachedDataOpts=null,this.update(e)},update:V.noop,transition:function(e){for(var t=this.getMeta(),n=t.data||[],r=n.length,i=0;i<r;++i)n[i].transition(e);t.dataset&&t.dataset.transition(e)},draw:function(){var e=this.getMeta(),t=e.data||[],n=t.length,r=0;for(e.dataset&&e.dataset.draw();r<n;++r)t[r].draw()},getStyle:function(e){var t,n=this.getMeta(),r=n.dataset;return this._configure(),r&&void 0===e?t=this._resolveDatasetElementOptions(r||{}):(e=e||0,t=this._resolveDataElementOptions(n.data[e]||{},e)),!1!==t.fill&&null!==t.fill||(t.backgroundColor=t.borderColor),t},_resolveDatasetElementOptions:function(e,t){var n,r,i,a,o=this,l=o.chart,s=o._config,u=e.custom||{},c=l.options.elements[o.datasetElementType.prototype._type]||{},d=o._datasetElementOptions,f={},h={chart:l,dataset:o.getDataset(),datasetIndex:o.index,hover:t};for(n=0,r=d.length;n<r;++n)i=d[n],a=t?"hover"+i.charAt(0).toUpperCase()+i.slice(1):i,f[i]=X([u[a],s[a],c[a]],h);return f},_resolveDataElementOptions:function(e,t){var n=this,r=e&&e.custom,i=n._cachedDataOpts;if(i&&!r)return i;var a,o,l,s,u=n.chart,c=n._config,d=u.options.elements[n.dataElementType.prototype._type]||{},f=n._dataElementOptions,h={},p={chart:u,dataIndex:t,dataset:n.getDataset(),datasetIndex:n.index},g={cacheable:!r};if(r=r||{},V.isArray(f))for(o=0,l=f.length;o<l;++o)h[s=f[o]]=X([r[s],c[s],d[s]],p,t,g);else for(o=0,l=(a=Object.keys(f)).length;o<l;++o)h[s=a[o]]=X([r[s],c[f[s]],c[s],d[s]],p,t,g);return g.cacheable&&(n._cachedDataOpts=Object.freeze(h)),h},removeHoverStyle:function(e){V.merge(e._model,e.$previousStyle||{}),delete e.$previousStyle},setHoverStyle:function(e){var t=this.chart.data.datasets[e._datasetIndex],n=e._index,r=e.custom||{},i=e._model,a=V.getHoverColor;e.$previousStyle={backgroundColor:i.backgroundColor,borderColor:i.borderColor,borderWidth:i.borderWidth},i.backgroundColor=X([r.hoverBackgroundColor,t.hoverBackgroundColor,a(i.backgroundColor)],void 0,n),i.borderColor=X([r.hoverBorderColor,t.hoverBorderColor,a(i.borderColor)],void 0,n),i.borderWidth=X([r.hoverBorderWidth,t.hoverBorderWidth,i.borderWidth],void 0,n)},_removeDatasetHoverStyle:function(){var e=this.getMeta().dataset;e&&this.removeHoverStyle(e)},_setDatasetHoverStyle:function(){var e,t,n,r,i,a,o=this.getMeta().dataset,l={};if(o){for(a=o._model,i=this._resolveDatasetElementOptions(o,!0),e=0,t=(r=Object.keys(i)).length;e<t;++e)l[n=r[e]]=a[n],a[n]=i[n];o.$previousStyle=l}},resyncElements:function(){var e=this.getMeta(),t=this.getDataset().data,n=e.data.length,r=t.length;r<n?e.data.splice(r,n-r):r>n&&this.insertElements(n,r-n)},insertElements:function(e,t){for(var n=0;n<t;++n)this.addElementAndReset(e+n)},onDataPush:function(){var e=arguments.length;this.insertElements(this.getDataset().data.length-e,e)},onDataPop:function(){this.getMeta().data.pop()},onDataShift:function(){this.getMeta().data.shift()},onDataSplice:function(e,t){this.getMeta().data.splice(e,t),this.insertElements(e,arguments.length-2)},onDataUnshift:function(){this.insertElements(0,arguments.length)}}),te.extend=V.inherits;var ne=te,re=2*Math.PI;function ie(e,t){var n=t.startAngle,r=t.endAngle,i=t.pixelMargin,a=i/t.outerRadius,o=t.x,l=t.y;e.beginPath(),e.arc(o,l,t.outerRadius,n-a,r+a),t.innerRadius>i?(a=i/t.innerRadius,e.arc(o,l,t.innerRadius-i,r+a,n-a,!0)):e.arc(o,l,i,r+Math.PI/2,n-Math.PI/2),e.closePath(),e.clip()}function ae(e,t,n){var r="inner"===t.borderAlign;r?(e.lineWidth=2*t.borderWidth,e.lineJoin="round"):(e.lineWidth=t.borderWidth,e.lineJoin="bevel"),n.fullCircles&&function(e,t,n,r){var i,a=n.endAngle;for(r&&(n.endAngle=n.startAngle+re,ie(e,n),n.endAngle=a,n.endAngle===n.startAngle&&n.fullCircles&&(n.endAngle+=re,n.fullCircles--)),e.beginPath(),e.arc(n.x,n.y,n.innerRadius,n.startAngle+re,n.startAngle,!0),i=0;i<n.fullCircles;++i)e.stroke();for(e.beginPath(),e.arc(n.x,n.y,t.outerRadius,n.startAngle,n.startAngle+re),i=0;i<n.fullCircles;++i)e.stroke()}(e,t,n,r),r&&ie(e,n),e.beginPath(),e.arc(n.x,n.y,t.outerRadius,n.startAngle,n.endAngle),e.arc(n.x,n.y,n.innerRadius,n.endAngle,n.startAngle,!0),e.closePath(),e.stroke()}R._set("global",{elements:{arc:{backgroundColor:R.global.defaultColor,borderColor:"#fff",borderWidth:2,borderAlign:"center"}}});var oe=G.extend({_type:"arc",inLabelRange:function(e){var t=this._view;return!!t&&Math.pow(e-t.x,2)<Math.pow(t.radius+t.hoverRadius,2)},inRange:function(e,t){var n=this._view;if(n){for(var r=V.getAngleFromPoint(n,{x:e,y:t}),i=r.angle,a=r.distance,o=n.startAngle,l=n.endAngle;l<o;)l+=re;for(;i>l;)i-=re;for(;i<o;)i+=re;var s=i>=o&&i<=l,u=a>=n.innerRadius&&a<=n.outerRadius;return s&&u}return!1},getCenterPoint:function(){var e=this._view,t=(e.startAngle+e.endAngle)/2,n=(e.innerRadius+e.outerRadius)/2;return{x:e.x+Math.cos(t)*n,y:e.y+Math.sin(t)*n}},getArea:function(){var e=this._view;return Math.PI*((e.endAngle-e.startAngle)/(2*Math.PI))*(Math.pow(e.outerRadius,2)-Math.pow(e.innerRadius,2))},tooltipPosition:function(){var e=this._view,t=e.startAngle+(e.endAngle-e.startAngle)/2,n=(e.outerRadius-e.innerRadius)/2+e.innerRadius;return{x:e.x+Math.cos(t)*n,y:e.y+Math.sin(t)*n}},draw:function(){var e,t=this._chart.ctx,n=this._view,r="inner"===n.borderAlign?.33:0,i={x:n.x,y:n.y,innerRadius:n.innerRadius,outerRadius:Math.max(n.outerRadius-r,0),pixelMargin:r,startAngle:n.startAngle,endAngle:n.endAngle,fullCircles:Math.floor(n.circumference/re)};if(t.save(),t.fillStyle=n.backgroundColor,t.strokeStyle=n.borderColor,i.fullCircles){for(i.endAngle=i.startAngle+re,t.beginPath(),t.arc(i.x,i.y,i.outerRadius,i.startAngle,i.endAngle),t.arc(i.x,i.y,i.innerRadius,i.endAngle,i.startAngle,!0),t.closePath(),e=0;e<i.fullCircles;++e)t.fill();i.endAngle=i.startAngle+n.circumference%re}t.beginPath(),t.arc(i.x,i.y,i.outerRadius,i.startAngle,i.endAngle),t.arc(i.x,i.y,i.innerRadius,i.endAngle,i.startAngle,!0),t.closePath(),t.fill(),n.borderWidth&&ae(t,n,i),t.restore()}}),le=V.valueOrDefault,se=R.global.defaultColor;R._set("global",{elements:{line:{tension:.4,backgroundColor:se,borderWidth:3,borderColor:se,borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",capBezierPoints:!0,fill:!0}}});var ue=G.extend({_type:"line",draw:function(){var e,t,n,r=this,i=r._view,a=r._chart.ctx,o=i.spanGaps,l=r._children.slice(),s=R.global,u=s.elements.line,c=-1,d=r._loop;if(l.length){if(r._loop){for(e=0;e<l.length;++e)if(t=V.previousItem(l,e),!l[e]._view.skip&&t._view.skip){l=l.slice(e).concat(l.slice(0,e)),d=o;break}d&&l.push(l[0])}for(a.save(),a.lineCap=i.borderCapStyle||u.borderCapStyle,a.setLineDash&&a.setLineDash(i.borderDash||u.borderDash),a.lineDashOffset=le(i.borderDashOffset,u.borderDashOffset),a.lineJoin=i.borderJoinStyle||u.borderJoinStyle,a.lineWidth=le(i.borderWidth,u.borderWidth),a.strokeStyle=i.borderColor||s.defaultColor,a.beginPath(),(n=l[0]._view).skip||(a.moveTo(n.x,n.y),c=0),e=1;e<l.length;++e)n=l[e]._view,t=-1===c?V.previousItem(l,e):l[c],n.skip||(c!==e-1&&!o||-1===c?a.moveTo(n.x,n.y):V.canvas.lineTo(a,t._view,n),c=e);d&&a.closePath(),a.stroke(),a.restore()}}}),ce=V.valueOrDefault,de=R.global.defaultColor;function fe(e){var t=this._view;return!!t&&Math.abs(e-t.x)<t.radius+t.hitRadius}R._set("global",{elements:{point:{radius:3,pointStyle:"circle",backgroundColor:de,borderColor:de,borderWidth:1,hitRadius:1,hoverRadius:4,hoverBorderWidth:1}}});var he=G.extend({_type:"point",inRange:function(e,t){var n=this._view;return!!n&&Math.pow(e-n.x,2)+Math.pow(t-n.y,2)<Math.pow(n.hitRadius+n.radius,2)},inLabelRange:fe,inXRange:fe,inYRange:function(e){var t=this._view;return!!t&&Math.abs(e-t.y)<t.radius+t.hitRadius},getCenterPoint:function(){var e=this._view;return{x:e.x,y:e.y}},getArea:function(){return Math.PI*Math.pow(this._view.radius,2)},tooltipPosition:function(){var e=this._view;return{x:e.x,y:e.y,padding:e.radius+e.borderWidth}},draw:function(e){var t=this._view,n=this._chart.ctx,r=t.pointStyle,i=t.rotation,a=t.radius,o=t.x,l=t.y,s=R.global,u=s.defaultColor;t.skip||(void 0===e||V.canvas._isPointInArea(t,e))&&(n.strokeStyle=t.borderColor||u,n.lineWidth=ce(t.borderWidth,s.elements.point.borderWidth),n.fillStyle=t.backgroundColor||u,V.canvas.drawPoint(n,r,a,o,l,i))}}),pe=R.global.defaultColor;function ge(e){return e&&void 0!==e.width}function me(e){var t,n,r,i,a;return ge(e)?(a=e.width/2,t=e.x-a,n=e.x+a,r=Math.min(e.y,e.base),i=Math.max(e.y,e.base)):(a=e.height/2,t=Math.min(e.x,e.base),n=Math.max(e.x,e.base),r=e.y-a,i=e.y+a),{left:t,top:r,right:n,bottom:i}}function ve(e,t,n){return e===t?n:e===n?t:e}function ye(e,t,n){var r,i,a,o,l=e.borderWidth,s=function(e){var t=e.borderSkipped,n={};return t?(e.horizontal?e.base>e.x&&(t=ve(t,"left","right")):e.base<e.y&&(t=ve(t,"bottom","top")),n[t]=!0,n):n}(e);return V.isObject(l)?(r=+l.top||0,i=+l.right||0,a=+l.bottom||0,o=+l.left||0):r=i=a=o=+l||0,{t:s.top||r<0?0:r>n?n:r,r:s.right||i<0?0:i>t?t:i,b:s.bottom||a<0?0:a>n?n:a,l:s.left||o<0?0:o>t?t:o}}function be(e,t,n){var r=null===t,i=null===n,a=!(!e||r&&i)&&me(e);return a&&(r||t>=a.left&&t<=a.right)&&(i||n>=a.top&&n<=a.bottom)}R._set("global",{elements:{rectangle:{backgroundColor:pe,borderColor:pe,borderSkipped:"bottom",borderWidth:0}}});var xe=G.extend({_type:"rectangle",draw:function(){var e=this._chart.ctx,t=this._view,n=function(e){var t=me(e),n=t.right-t.left,r=t.bottom-t.top,i=ye(e,n/2,r/2);return{outer:{x:t.left,y:t.top,w:n,h:r},inner:{x:t.left+i.l,y:t.top+i.t,w:n-i.l-i.r,h:r-i.t-i.b}}}(t),r=n.outer,i=n.inner;e.fillStyle=t.backgroundColor,e.fillRect(r.x,r.y,r.w,r.h),r.w===i.w&&r.h===i.h||(e.save(),e.beginPath(),e.rect(r.x,r.y,r.w,r.h),e.clip(),e.fillStyle=t.borderColor,e.rect(i.x,i.y,i.w,i.h),e.fill("evenodd"),e.restore())},height:function(){var e=this._view;return e.base-e.y},inRange:function(e,t){return be(this._view,e,t)},inLabelRange:function(e,t){var n=this._view;return ge(n)?be(n,e,null):be(n,null,t)},inXRange:function(e){return be(this._view,e,null)},inYRange:function(e){return be(this._view,null,e)},getCenterPoint:function(){var e,t,n=this._view;return ge(n)?(e=n.x,t=(n.y+n.base)/2):(e=(n.x+n.base)/2,t=n.y),{x:e,y:t}},getArea:function(){var e=this._view;return ge(e)?e.width*Math.abs(e.y-e.base):e.height*Math.abs(e.x-e.base)},tooltipPosition:function(){var e=this._view;return{x:e.x,y:e.y}}}),_e={},we=oe,ke=ue,Se=he,Te=xe;_e.Arc=we,_e.Line=ke,_e.Point=Se,_e.Rectangle=Te;var Me=V._deprecated,Ce=V.valueOrDefault;function Pe(e,t,n){var r,i,a=n.barThickness,o=t.stackCount,l=t.pixels[e],s=V.isNullOrUndef(a)?function(e,t){var n,r,i,a,o=e._length;for(i=1,a=t.length;i<a;++i)o=Math.min(o,Math.abs(t[i]-t[i-1]));for(i=0,a=e.getTicks().length;i<a;++i)r=e.getPixelForTick(i),o=i>0?Math.min(o,Math.abs(r-n)):o,n=r;return o}(t.scale,t.pixels):-1;return V.isNullOrUndef(a)?(r=s*n.categoryPercentage,i=n.barPercentage):(r=a*o,i=1),{chunk:r/o,ratio:i,start:l-r/2}}R._set("bar",{hover:{mode:"label"},scales:{xAxes:[{type:"category",offset:!0,gridLines:{offsetGridLines:!0}}],yAxes:[{type:"linear"}]}}),R._set("global",{datasets:{bar:{categoryPercentage:.8,barPercentage:.9}}});var De=ne.extend({dataElementType:_e.Rectangle,_dataElementOptions:["backgroundColor","borderColor","borderSkipped","borderWidth","barPercentage","barThickness","categoryPercentage","maxBarThickness","minBarLength"],initialize:function(){var e,t,n=this;ne.prototype.initialize.apply(n,arguments),(e=n.getMeta()).stack=n.getDataset().stack,e.bar=!0,t=n._getIndexScale().options,Me("bar chart",t.barPercentage,"scales.[x/y]Axes.barPercentage","dataset.barPercentage"),Me("bar chart",t.barThickness,"scales.[x/y]Axes.barThickness","dataset.barThickness"),Me("bar chart",t.categoryPercentage,"scales.[x/y]Axes.categoryPercentage","dataset.categoryPercentage"),Me("bar chart",n._getValueScale().options.minBarLength,"scales.[x/y]Axes.minBarLength","dataset.minBarLength"),Me("bar chart",t.maxBarThickness,"scales.[x/y]Axes.maxBarThickness","dataset.maxBarThickness")},update:function(e){var t,n,r=this.getMeta().data;for(this._ruler=this.getRuler(),t=0,n=r.length;t<n;++t)this.updateElement(r[t],t,e)},updateElement:function(e,t,n){var r=this,i=r.getMeta(),a=r.getDataset(),o=r._resolveDataElementOptions(e,t);e._xScale=r.getScaleForId(i.xAxisID),e._yScale=r.getScaleForId(i.yAxisID),e._datasetIndex=r.index,e._index=t,e._model={backgroundColor:o.backgroundColor,borderColor:o.borderColor,borderSkipped:o.borderSkipped,borderWidth:o.borderWidth,datasetLabel:a.label,label:r.chart.data.labels[t]},V.isArray(a.data[t])&&(e._model.borderSkipped=null),r._updateElementGeometry(e,t,n,o),e.pivot()},_updateElementGeometry:function(e,t,n,r){var i=this,a=e._model,o=i._getValueScale(),l=o.getBasePixel(),s=o.isHorizontal(),u=i._ruler||i.getRuler(),c=i.calculateBarValuePixels(i.index,t,r),d=i.calculateBarIndexPixels(i.index,t,u,r);a.horizontal=s,a.base=n?l:c.base,a.x=s?n?l:c.head:d.center,a.y=s?d.center:n?l:c.head,a.height=s?d.size:void 0,a.width=s?void 0:d.size},_getStacks:function(e){var t,n,r=this._getIndexScale(),i=r._getMatchingVisibleMetas(this._type),a=r.options.stacked,o=i.length,l=[];for(t=0;t<o&&(n=i[t],(!1===a||-1===l.indexOf(n.stack)||void 0===a&&void 0===n.stack)&&l.push(n.stack),n.index!==e);++t);return l},getStackCount:function(){return this._getStacks().length},getStackIndex:function(e,t){var n=this._getStacks(e),r=void 0!==t?n.indexOf(t):-1;return-1===r?n.length-1:r},getRuler:function(){var e,t,n=this._getIndexScale(),r=[];for(e=0,t=this.getMeta().data.length;e<t;++e)r.push(n.getPixelForValue(null,e,this.index));return{pixels:r,start:n._startPixel,end:n._endPixel,stackCount:this.getStackCount(),scale:n}},calculateBarValuePixels:function(e,t,n){var r,i,a,o,l,s,u,c=this.chart,d=this._getValueScale(),f=d.isHorizontal(),h=c.data.datasets,p=d._getMatchingVisibleMetas(this._type),g=d._parseValue(h[e].data[t]),m=n.minBarLength,v=d.options.stacked,y=this.getMeta().stack,b=void 0===g.start?0:g.max>=0&&g.min>=0?g.min:g.max,x=void 0===g.start?g.end:g.max>=0&&g.min>=0?g.max-g.min:g.min-g.max,_=p.length;if(v||void 0===v&&void 0!==y)for(r=0;r<_&&(i=p[r]).index!==e;++r)i.stack===y&&(a=void 0===(u=d._parseValue(h[i.index].data[t])).start?u.end:u.min>=0&&u.max>=0?u.max:u.min,(g.min<0&&a<0||g.max>=0&&a>0)&&(b+=a));return o=d.getPixelForValue(b),s=(l=d.getPixelForValue(b+x))-o,void 0!==m&&Math.abs(s)<m&&(s=m,l=x>=0&&!f||x<0&&f?o-m:o+m),{size:s,base:o,head:l,center:l+s/2}},calculateBarIndexPixels:function(e,t,n,r){var i="flex"===r.barThickness?function(e,t,n){var r,i=t.pixels,a=i[e],o=e>0?i[e-1]:null,l=e<i.length-1?i[e+1]:null,s=n.categoryPercentage;return null===o&&(o=a-(null===l?t.end-t.start:l-a)),null===l&&(l=a+a-o),r=a-(a-Math.min(o,l))/2*s,{chunk:Math.abs(l-o)/2*s/t.stackCount,ratio:n.barPercentage,start:r}}(t,n,r):Pe(t,n,r),a=this.getStackIndex(e,this.getMeta().stack),o=i.start+i.chunk*a+i.chunk/2,l=Math.min(Ce(r.maxBarThickness,1/0),i.chunk*i.ratio);return{base:o-l/2,head:o+l/2,center:o,size:l}},draw:function(){var e=this.chart,t=this._getValueScale(),n=this.getMeta().data,r=this.getDataset(),i=n.length,a=0;for(V.canvas.clipArea(e.ctx,e.chartArea);a<i;++a){var o=t._parseValue(r.data[a]);isNaN(o.min)||isNaN(o.max)||n[a].draw()}V.canvas.unclipArea(e.ctx)},_resolveDataElementOptions:function(){var e=this,t=V.extend({},ne.prototype._resolveDataElementOptions.apply(e,arguments)),n=e._getIndexScale().options,r=e._getValueScale().options;return t.barPercentage=Ce(n.barPercentage,t.barPercentage),t.barThickness=Ce(n.barThickness,t.barThickness),t.categoryPercentage=Ce(n.categoryPercentage,t.categoryPercentage),t.maxBarThickness=Ce(n.maxBarThickness,t.maxBarThickness),t.minBarLength=Ce(r.minBarLength,t.minBarLength),t}}),Ee=V.valueOrDefault,Oe=V.options.resolve;R._set("bubble",{hover:{mode:"single"},scales:{xAxes:[{type:"linear",position:"bottom",id:"x-axis-0"}],yAxes:[{type:"linear",position:"left",id:"y-axis-0"}]},tooltips:{callbacks:{title:function(){return""},label:function(e,t){var n=t.datasets[e.datasetIndex].label||"",r=t.datasets[e.datasetIndex].data[e.index];return n+": ("+e.xLabel+", "+e.yLabel+", "+r.r+")"}}}});var Ne=ne.extend({dataElementType:_e.Point,_dataElementOptions:["backgroundColor","borderColor","borderWidth","hoverBackgroundColor","hoverBorderColor","hoverBorderWidth","hoverRadius","hitRadius","pointStyle","rotation"],update:function(e){var t=this,n=t.getMeta().data;V.each(n,(function(n,r){t.updateElement(n,r,e)}))},updateElement:function(e,t,n){var r=this,i=r.getMeta(),a=e.custom||{},o=r.getScaleForId(i.xAxisID),l=r.getScaleForId(i.yAxisID),s=r._resolveDataElementOptions(e,t),u=r.getDataset().data[t],c=r.index,d=n?o.getPixelForDecimal(.5):o.getPixelForValue("object"===typeof u?u:NaN,t,c),f=n?l.getBasePixel():l.getPixelForValue(u,t,c);e._xScale=o,e._yScale=l,e._options=s,e._datasetIndex=c,e._index=t,e._model={backgroundColor:s.backgroundColor,borderColor:s.borderColor,borderWidth:s.borderWidth,hitRadius:s.hitRadius,pointStyle:s.pointStyle,rotation:s.rotation,radius:n?0:s.radius,skip:a.skip||isNaN(d)||isNaN(f),x:d,y:f},e.pivot()},setHoverStyle:function(e){var t=e._model,n=e._options,r=V.getHoverColor;e.$previousStyle={backgroundColor:t.backgroundColor,borderColor:t.borderColor,borderWidth:t.borderWidth,radius:t.radius},t.backgroundColor=Ee(n.hoverBackgroundColor,r(n.backgroundColor)),t.borderColor=Ee(n.hoverBorderColor,r(n.borderColor)),t.borderWidth=Ee(n.hoverBorderWidth,n.borderWidth),t.radius=n.radius+n.hoverRadius},_resolveDataElementOptions:function(e,t){var n=this,r=n.chart,i=n.getDataset(),a=e.custom||{},o=i.data[t]||{},l=ne.prototype._resolveDataElementOptions.apply(n,arguments),s={chart:r,dataIndex:t,dataset:i,datasetIndex:n.index};return n._cachedDataOpts===l&&(l=V.extend({},l)),l.radius=Oe([a.radius,o.r,n._config.radius,r.options.elements.point.radius],s,t),l}}),Ie=V.valueOrDefault,Ae=Math.PI,Fe=2*Ae,Re=Ae/2;R._set("doughnut",{animation:{animateRotate:!0,animateScale:!1},hover:{mode:"single"},legendCallback:function(e){var t,n,r,i=document.createElement("ul"),a=e.data,o=a.datasets,l=a.labels;if(i.setAttribute("class",e.id+"-legend"),o.length)for(t=0,n=o[0].data.length;t<n;++t)(r=i.appendChild(document.createElement("li"))).appendChild(document.createElement("span")).style.backgroundColor=o[0].backgroundColor[t],l[t]&&r.appendChild(document.createTextNode(l[t]));return i.outerHTML},legend:{labels:{generateLabels:function(e){var t=e.data;return t.labels.length&&t.datasets.length?t.labels.map((function(n,r){var i=e.getDatasetMeta(0),a=i.controller.getStyle(r);return{text:n,fillStyle:a.backgroundColor,strokeStyle:a.borderColor,lineWidth:a.borderWidth,hidden:isNaN(t.datasets[0].data[r])||i.data[r].hidden,index:r}})):[]}},onClick:function(e,t){var n,r,i,a=t.index,o=this.chart;for(n=0,r=(o.data.datasets||[]).length;n<r;++n)(i=o.getDatasetMeta(n)).data[a]&&(i.data[a].hidden=!i.data[a].hidden);o.update()}},cutoutPercentage:50,rotation:-Re,circumference:Fe,tooltips:{callbacks:{title:function(){return""},label:function(e,t){var n=t.labels[e.index],r=": "+t.datasets[e.datasetIndex].data[e.index];return V.isArray(n)?(n=n.slice())[0]+=r:n+=r,n}}}});var Le=ne.extend({dataElementType:_e.Arc,linkScales:V.noop,_dataElementOptions:["backgroundColor","borderColor","borderWidth","borderAlign","hoverBackgroundColor","hoverBorderColor","hoverBorderWidth"],getRingIndex:function(e){for(var t=0,n=0;n<e;++n)this.chart.isDatasetVisible(n)&&++t;return t},update:function(e){var t,n,r,i,a=this,o=a.chart,l=o.chartArea,s=o.options,u=1,c=1,d=0,f=0,h=a.getMeta(),p=h.data,g=s.cutoutPercentage/100||0,m=s.circumference,v=a._getRingWeight(a.index);if(m<Fe){var y=s.rotation%Fe,b=(y+=y>=Ae?-Fe:y<-Ae?Fe:0)+m,x=Math.cos(y),_=Math.sin(y),w=Math.cos(b),k=Math.sin(b),S=y<=0&&b>=0||b>=Fe,T=y<=Re&&b>=Re||b>=Fe+Re,M=y<=-Re&&b>=-Re||b>=Ae+Re,C=y===-Ae||b>=Ae?-1:Math.min(x,x*g,w,w*g),P=M?-1:Math.min(_,_*g,k,k*g),D=S?1:Math.max(x,x*g,w,w*g),E=T?1:Math.max(_,_*g,k,k*g);u=(D-C)/2,c=(E-P)/2,d=-(D+C)/2,f=-(E+P)/2}for(r=0,i=p.length;r<i;++r)p[r]._options=a._resolveDataElementOptions(p[r],r);for(o.borderWidth=a.getMaxBorderWidth(),t=(l.right-l.left-o.borderWidth)/u,n=(l.bottom-l.top-o.borderWidth)/c,o.outerRadius=Math.max(Math.min(t,n)/2,0),o.innerRadius=Math.max(o.outerRadius*g,0),o.radiusLength=(o.outerRadius-o.innerRadius)/(a._getVisibleDatasetWeightTotal()||1),o.offsetX=d*o.outerRadius,o.offsetY=f*o.outerRadius,h.total=a.calculateTotal(),a.outerRadius=o.outerRadius-o.radiusLength*a._getRingWeightOffset(a.index),a.innerRadius=Math.max(a.outerRadius-o.radiusLength*v,0),r=0,i=p.length;r<i;++r)a.updateElement(p[r],r,e)},updateElement:function(e,t,n){var r=this,i=r.chart,a=i.chartArea,o=i.options,l=o.animation,s=(a.left+a.right)/2,u=(a.top+a.bottom)/2,c=o.rotation,d=o.rotation,f=r.getDataset(),h=n&&l.animateRotate||e.hidden?0:r.calculateCircumference(f.data[t])*(o.circumference/Fe),p=n&&l.animateScale?0:r.innerRadius,g=n&&l.animateScale?0:r.outerRadius,m=e._options||{};V.extend(e,{_datasetIndex:r.index,_index:t,_model:{backgroundColor:m.backgroundColor,borderColor:m.borderColor,borderWidth:m.borderWidth,borderAlign:m.borderAlign,x:s+i.offsetX,y:u+i.offsetY,startAngle:c,endAngle:d,circumference:h,outerRadius:g,innerRadius:p,label:V.valueAtIndexOrDefault(f.label,t,i.data.labels[t])}});var v=e._model;n&&l.animateRotate||(v.startAngle=0===t?o.rotation:r.getMeta().data[t-1]._model.endAngle,v.endAngle=v.startAngle+v.circumference),e.pivot()},calculateTotal:function(){var e,t=this.getDataset(),n=this.getMeta(),r=0;return V.each(n.data,(function(n,i){e=t.data[i],isNaN(e)||n.hidden||(r+=Math.abs(e))})),r},calculateCircumference:function(e){var t=this.getMeta().total;return t>0&&!isNaN(e)?Fe*(Math.abs(e)/t):0},getMaxBorderWidth:function(e){var t,n,r,i,a,o,l,s,u=0,c=this.chart;if(!e)for(t=0,n=c.data.datasets.length;t<n;++t)if(c.isDatasetVisible(t)){e=(r=c.getDatasetMeta(t)).data,t!==this.index&&(a=r.controller);break}if(!e)return 0;for(t=0,n=e.length;t<n;++t)i=e[t],a?(a._configure(),o=a._resolveDataElementOptions(i,t)):o=i._options,"inner"!==o.borderAlign&&(l=o.borderWidth,u=(s=o.hoverBorderWidth)>(u=l>u?l:u)?s:u);return u},setHoverStyle:function(e){var t=e._model,n=e._options,r=V.getHoverColor;e.$previousStyle={backgroundColor:t.backgroundColor,borderColor:t.borderColor,borderWidth:t.borderWidth},t.backgroundColor=Ie(n.hoverBackgroundColor,r(n.backgroundColor)),t.borderColor=Ie(n.hoverBorderColor,r(n.borderColor)),t.borderWidth=Ie(n.hoverBorderWidth,n.borderWidth)},_getRingWeightOffset:function(e){for(var t=0,n=0;n<e;++n)this.chart.isDatasetVisible(n)&&(t+=this._getRingWeight(n));return t},_getRingWeight:function(e){return Math.max(Ie(this.chart.data.datasets[e].weight,1),0)},_getVisibleDatasetWeightTotal:function(){return this._getRingWeightOffset(this.chart.data.datasets.length)}});R._set("horizontalBar",{hover:{mode:"index",axis:"y"},scales:{xAxes:[{type:"linear",position:"bottom"}],yAxes:[{type:"category",position:"left",offset:!0,gridLines:{offsetGridLines:!0}}]},elements:{rectangle:{borderSkipped:"left"}},tooltips:{mode:"index",axis:"y"}}),R._set("global",{datasets:{horizontalBar:{categoryPercentage:.8,barPercentage:.9}}});var ze=De.extend({_getValueScaleId:function(){return this.getMeta().xAxisID},_getIndexScaleId:function(){return this.getMeta().yAxisID}}),je=V.valueOrDefault,We=V.options.resolve,Ve=V.canvas._isPointInArea;function Ye(e,t){var n=e&&e.options.ticks||{},r=n.reverse,i=void 0===n.min?t:0,a=void 0===n.max?t:0;return{start:r?a:i,end:r?i:a}}function Be(e,t,n){var r=n/2,i=Ye(e,r),a=Ye(t,r);return{top:a.end,right:i.end,bottom:a.start,left:i.start}}function He(e){var t,n,r,i;return V.isObject(e)?(t=e.top,n=e.right,r=e.bottom,i=e.left):t=n=r=i=e,{top:t,right:n,bottom:r,left:i}}R._set("line",{showLines:!0,spanGaps:!1,hover:{mode:"label"},scales:{xAxes:[{type:"category",id:"x-axis-0"}],yAxes:[{type:"linear",id:"y-axis-0"}]}});var Ue=ne.extend({datasetElementType:_e.Line,dataElementType:_e.Point,_datasetElementOptions:["backgroundColor","borderCapStyle","borderColor","borderDash","borderDashOffset","borderJoinStyle","borderWidth","cubicInterpolationMode","fill"],_dataElementOptions:{backgroundColor:"pointBackgroundColor",borderColor:"pointBorderColor",borderWidth:"pointBorderWidth",hitRadius:"pointHitRadius",hoverBackgroundColor:"pointHoverBackgroundColor",hoverBorderColor:"pointHoverBorderColor",hoverBorderWidth:"pointHoverBorderWidth",hoverRadius:"pointHoverRadius",pointStyle:"pointStyle",radius:"pointRadius",rotation:"pointRotation"},update:function(e){var t,n,r=this,i=r.getMeta(),a=i.dataset,o=i.data||[],l=r.chart.options,s=r._config,u=r._showLine=je(s.showLine,l.showLines);for(r._xScale=r.getScaleForId(i.xAxisID),r._yScale=r.getScaleForId(i.yAxisID),u&&(void 0!==s.tension&&void 0===s.lineTension&&(s.lineTension=s.tension),a._scale=r._yScale,a._datasetIndex=r.index,a._children=o,a._model=r._resolveDatasetElementOptions(a),a.pivot()),t=0,n=o.length;t<n;++t)r.updateElement(o[t],t,e);for(u&&0!==a._model.tension&&r.updateBezierControlPoints(),t=0,n=o.length;t<n;++t)o[t].pivot()},updateElement:function(e,t,n){var r,i,a=this,o=a.getMeta(),l=e.custom||{},s=a.getDataset(),u=a.index,c=s.data[t],d=a._xScale,f=a._yScale,h=o.dataset._model,p=a._resolveDataElementOptions(e,t);r=d.getPixelForValue("object"===typeof c?c:NaN,t,u),i=n?f.getBasePixel():a.calculatePointY(c,t,u),e._xScale=d,e._yScale=f,e._options=p,e._datasetIndex=u,e._index=t,e._model={x:r,y:i,skip:l.skip||isNaN(r)||isNaN(i),radius:p.radius,pointStyle:p.pointStyle,rotation:p.rotation,backgroundColor:p.backgroundColor,borderColor:p.borderColor,borderWidth:p.borderWidth,tension:je(l.tension,h?h.tension:0),steppedLine:!!h&&h.steppedLine,hitRadius:p.hitRadius}},_resolveDatasetElementOptions:function(e){var t=this,n=t._config,r=e.custom||{},i=t.chart.options,a=i.elements.line,o=ne.prototype._resolveDatasetElementOptions.apply(t,arguments);return o.spanGaps=je(n.spanGaps,i.spanGaps),o.tension=je(n.lineTension,a.tension),o.steppedLine=We([r.steppedLine,n.steppedLine,a.stepped]),o.clip=He(je(n.clip,Be(t._xScale,t._yScale,o.borderWidth))),o},calculatePointY:function(e,t,n){var r,i,a,o,l,s,u,c=this.chart,d=this._yScale,f=0,h=0;if(d.options.stacked){for(l=+d.getRightValue(e),u=(s=c._getSortedVisibleDatasetMetas()).length,r=0;r<u&&(a=s[r]).index!==n;++r)i=c.data.datasets[a.index],"line"===a.type&&a.yAxisID===d.id&&((o=+d.getRightValue(i.data[t]))<0?h+=o||0:f+=o||0);return l<0?d.getPixelForValue(h+l):d.getPixelForValue(f+l)}return d.getPixelForValue(e)},updateBezierControlPoints:function(){var e,t,n,r,i=this.chart,a=this.getMeta(),o=a.dataset._model,l=i.chartArea,s=a.data||[];function u(e,t,n){return Math.max(Math.min(e,n),t)}if(o.spanGaps&&(s=s.filter((function(e){return!e._model.skip}))),"monotone"===o.cubicInterpolationMode)V.splineCurveMonotone(s);else for(e=0,t=s.length;e<t;++e)n=s[e]._model,r=V.splineCurve(V.previousItem(s,e)._model,n,V.nextItem(s,e)._model,o.tension),n.controlPointPreviousX=r.previous.x,n.controlPointPreviousY=r.previous.y,n.controlPointNextX=r.next.x,n.controlPointNextY=r.next.y;if(i.options.elements.line.capBezierPoints)for(e=0,t=s.length;e<t;++e)n=s[e]._model,Ve(n,l)&&(e>0&&Ve(s[e-1]._model,l)&&(n.controlPointPreviousX=u(n.controlPointPreviousX,l.left,l.right),n.controlPointPreviousY=u(n.controlPointPreviousY,l.top,l.bottom)),e<s.length-1&&Ve(s[e+1]._model,l)&&(n.controlPointNextX=u(n.controlPointNextX,l.left,l.right),n.controlPointNextY=u(n.controlPointNextY,l.top,l.bottom)))},draw:function(){var e,t=this.chart,n=this.getMeta(),r=n.data||[],i=t.chartArea,a=t.canvas,o=0,l=r.length;for(this._showLine&&(e=n.dataset._model.clip,V.canvas.clipArea(t.ctx,{left:!1===e.left?0:i.left-e.left,right:!1===e.right?a.width:i.right+e.right,top:!1===e.top?0:i.top-e.top,bottom:!1===e.bottom?a.height:i.bottom+e.bottom}),n.dataset.draw(),V.canvas.unclipArea(t.ctx));o<l;++o)r[o].draw(i)},setHoverStyle:function(e){var t=e._model,n=e._options,r=V.getHoverColor;e.$previousStyle={backgroundColor:t.backgroundColor,borderColor:t.borderColor,borderWidth:t.borderWidth,radius:t.radius},t.backgroundColor=je(n.hoverBackgroundColor,r(n.backgroundColor)),t.borderColor=je(n.hoverBorderColor,r(n.borderColor)),t.borderWidth=je(n.hoverBorderWidth,n.borderWidth),t.radius=je(n.hoverRadius,n.radius)}}),qe=V.options.resolve;R._set("polarArea",{scale:{type:"radialLinear",angleLines:{display:!1},gridLines:{circular:!0},pointLabels:{display:!1},ticks:{beginAtZero:!0}},animation:{animateRotate:!0,animateScale:!0},startAngle:-.5*Math.PI,legendCallback:function(e){var t,n,r,i=document.createElement("ul"),a=e.data,o=a.datasets,l=a.labels;if(i.setAttribute("class",e.id+"-legend"),o.length)for(t=0,n=o[0].data.length;t<n;++t)(r=i.appendChild(document.createElement("li"))).appendChild(document.createElement("span")).style.backgroundColor=o[0].backgroundColor[t],l[t]&&r.appendChild(document.createTextNode(l[t]));return i.outerHTML},legend:{labels:{generateLabels:function(e){var t=e.data;return t.labels.length&&t.datasets.length?t.labels.map((function(n,r){var i=e.getDatasetMeta(0),a=i.controller.getStyle(r);return{text:n,fillStyle:a.backgroundColor,strokeStyle:a.borderColor,lineWidth:a.borderWidth,hidden:isNaN(t.datasets[0].data[r])||i.data[r].hidden,index:r}})):[]}},onClick:function(e,t){var n,r,i,a=t.index,o=this.chart;for(n=0,r=(o.data.datasets||[]).length;n<r;++n)(i=o.getDatasetMeta(n)).data[a].hidden=!i.data[a].hidden;o.update()}},tooltips:{callbacks:{title:function(){return""},label:function(e,t){return t.labels[e.index]+": "+e.yLabel}}}});var $e=ne.extend({dataElementType:_e.Arc,linkScales:V.noop,_dataElementOptions:["backgroundColor","borderColor","borderWidth","borderAlign","hoverBackgroundColor","hoverBorderColor","hoverBorderWidth"],_getIndexScaleId:function(){return this.chart.scale.id},_getValueScaleId:function(){return this.chart.scale.id},update:function(e){var t,n,r,i=this,a=i.getDataset(),o=i.getMeta(),l=i.chart.options.startAngle||0,s=i._starts=[],u=i._angles=[],c=o.data;for(i._updateRadius(),o.count=i.countVisibleElements(),t=0,n=a.data.length;t<n;t++)s[t]=l,r=i._computeAngle(t),u[t]=r,l+=r;for(t=0,n=c.length;t<n;++t)c[t]._options=i._resolveDataElementOptions(c[t],t),i.updateElement(c[t],t,e)},_updateRadius:function(){var e=this,t=e.chart,n=t.chartArea,r=t.options,i=Math.min(n.right-n.left,n.bottom-n.top);t.outerRadius=Math.max(i/2,0),t.innerRadius=Math.max(r.cutoutPercentage?t.outerRadius/100*r.cutoutPercentage:1,0),t.radiusLength=(t.outerRadius-t.innerRadius)/t.getVisibleDatasetCount(),e.outerRadius=t.outerRadius-t.radiusLength*e.index,e.innerRadius=e.outerRadius-t.radiusLength},updateElement:function(e,t,n){var r=this,i=r.chart,a=r.getDataset(),o=i.options,l=o.animation,s=i.scale,u=i.data.labels,c=s.xCenter,d=s.yCenter,f=o.startAngle,h=e.hidden?0:s.getDistanceFromCenterForValue(a.data[t]),p=r._starts[t],g=p+(e.hidden?0:r._angles[t]),m=l.animateScale?0:s.getDistanceFromCenterForValue(a.data[t]),v=e._options||{};V.extend(e,{_datasetIndex:r.index,_index:t,_scale:s,_model:{backgroundColor:v.backgroundColor,borderColor:v.borderColor,borderWidth:v.borderWidth,borderAlign:v.borderAlign,x:c,y:d,innerRadius:0,outerRadius:n?m:h,startAngle:n&&l.animateRotate?f:p,endAngle:n&&l.animateRotate?f:g,label:V.valueAtIndexOrDefault(u,t,u[t])}}),e.pivot()},countVisibleElements:function(){var e=this.getDataset(),t=this.getMeta(),n=0;return V.each(t.data,(function(t,r){isNaN(e.data[r])||t.hidden||n++})),n},setHoverStyle:function(e){var t=e._model,n=e._options,r=V.getHoverColor,i=V.valueOrDefault;e.$previousStyle={backgroundColor:t.backgroundColor,borderColor:t.borderColor,borderWidth:t.borderWidth},t.backgroundColor=i(n.hoverBackgroundColor,r(n.backgroundColor)),t.borderColor=i(n.hoverBorderColor,r(n.borderColor)),t.borderWidth=i(n.hoverBorderWidth,n.borderWidth)},_computeAngle:function(e){var t=this,n=this.getMeta().count,r=t.getDataset(),i=t.getMeta();if(isNaN(r.data[e])||i.data[e].hidden)return 0;var a={chart:t.chart,dataIndex:e,dataset:r,datasetIndex:t.index};return qe([t.chart.options.elements.arc.angle,2*Math.PI/n],a,e)}});R._set("pie",V.clone(R.doughnut)),R._set("pie",{cutoutPercentage:0});var Ge=Le,Qe=V.valueOrDefault;R._set("radar",{spanGaps:!1,scale:{type:"radialLinear"},elements:{line:{fill:"start",tension:0}}});var Ke=ne.extend({datasetElementType:_e.Line,dataElementType:_e.Point,linkScales:V.noop,_datasetElementOptions:["backgroundColor","borderWidth","borderColor","borderCapStyle","borderDash","borderDashOffset","borderJoinStyle","fill"],_dataElementOptions:{backgroundColor:"pointBackgroundColor",borderColor:"pointBorderColor",borderWidth:"pointBorderWidth",hitRadius:"pointHitRadius",hoverBackgroundColor:"pointHoverBackgroundColor",hoverBorderColor:"pointHoverBorderColor",hoverBorderWidth:"pointHoverBorderWidth",hoverRadius:"pointHoverRadius",pointStyle:"pointStyle",radius:"pointRadius",rotation:"pointRotation"},_getIndexScaleId:function(){return this.chart.scale.id},_getValueScaleId:function(){return this.chart.scale.id},update:function(e){var t,n,r=this,i=r.getMeta(),a=i.dataset,o=i.data||[],l=r.chart.scale,s=r._config;for(void 0!==s.tension&&void 0===s.lineTension&&(s.lineTension=s.tension),a._scale=l,a._datasetIndex=r.index,a._children=o,a._loop=!0,a._model=r._resolveDatasetElementOptions(a),a.pivot(),t=0,n=o.length;t<n;++t)r.updateElement(o[t],t,e);for(r.updateBezierControlPoints(),t=0,n=o.length;t<n;++t)o[t].pivot()},updateElement:function(e,t,n){var r=this,i=e.custom||{},a=r.getDataset(),o=r.chart.scale,l=o.getPointPositionForValue(t,a.data[t]),s=r._resolveDataElementOptions(e,t),u=r.getMeta().dataset._model,c=n?o.xCenter:l.x,d=n?o.yCenter:l.y;e._scale=o,e._options=s,e._datasetIndex=r.index,e._index=t,e._model={x:c,y:d,skip:i.skip||isNaN(c)||isNaN(d),radius:s.radius,pointStyle:s.pointStyle,rotation:s.rotation,backgroundColor:s.backgroundColor,borderColor:s.borderColor,borderWidth:s.borderWidth,tension:Qe(i.tension,u?u.tension:0),hitRadius:s.hitRadius}},_resolveDatasetElementOptions:function(){var e=this,t=e._config,n=e.chart.options,r=ne.prototype._resolveDatasetElementOptions.apply(e,arguments);return r.spanGaps=Qe(t.spanGaps,n.spanGaps),r.tension=Qe(t.lineTension,n.elements.line.tension),r},updateBezierControlPoints:function(){var e,t,n,r,i=this.getMeta(),a=this.chart.chartArea,o=i.data||[];function l(e,t,n){return Math.max(Math.min(e,n),t)}for(i.dataset._model.spanGaps&&(o=o.filter((function(e){return!e._model.skip}))),e=0,t=o.length;e<t;++e)n=o[e]._model,r=V.splineCurve(V.previousItem(o,e,!0)._model,n,V.nextItem(o,e,!0)._model,n.tension),n.controlPointPreviousX=l(r.previous.x,a.left,a.right),n.controlPointPreviousY=l(r.previous.y,a.top,a.bottom),n.controlPointNextX=l(r.next.x,a.left,a.right),n.controlPointNextY=l(r.next.y,a.top,a.bottom)},setHoverStyle:function(e){var t=e._model,n=e._options,r=V.getHoverColor;e.$previousStyle={backgroundColor:t.backgroundColor,borderColor:t.borderColor,borderWidth:t.borderWidth,radius:t.radius},t.backgroundColor=Qe(n.hoverBackgroundColor,r(n.backgroundColor)),t.borderColor=Qe(n.hoverBorderColor,r(n.borderColor)),t.borderWidth=Qe(n.hoverBorderWidth,n.borderWidth),t.radius=Qe(n.hoverRadius,n.radius)}});R._set("scatter",{hover:{mode:"single"},scales:{xAxes:[{id:"x-axis-1",type:"linear",position:"bottom"}],yAxes:[{id:"y-axis-1",type:"linear",position:"left"}]},tooltips:{callbacks:{title:function(){return""},label:function(e){return"("+e.xLabel+", "+e.yLabel+")"}}}}),R._set("global",{datasets:{scatter:{showLine:!1}}});var Ze={bar:De,bubble:Ne,doughnut:Le,horizontalBar:ze,line:Ue,polarArea:$e,pie:Ge,radar:Ke,scatter:Ue};function Xe(e,t){return e.native?{x:e.x,y:e.y}:V.getRelativePosition(e,t)}function Je(e,t){var n,r,i,a,o,l,s=e._getSortedVisibleDatasetMetas();for(r=0,a=s.length;r<a;++r)for(i=0,o=(n=s[r].data).length;i<o;++i)(l=n[i])._view.skip||t(l)}function et(e,t){var n=[];return Je(e,(function(e){e.inRange(t.x,t.y)&&n.push(e)})),n}function tt(e,t,n,r){var i=Number.POSITIVE_INFINITY,a=[];return Je(e,(function(e){if(!n||e.inRange(t.x,t.y)){var o=e.getCenterPoint(),l=r(t,o);l<i?(a=[e],i=l):l===i&&a.push(e)}})),a}function nt(e){var t=-1!==e.indexOf("x"),n=-1!==e.indexOf("y");return function(e,r){var i=t?Math.abs(e.x-r.x):0,a=n?Math.abs(e.y-r.y):0;return Math.sqrt(Math.pow(i,2)+Math.pow(a,2))}}function rt(e,t,n){var r=Xe(t,e);n.axis=n.axis||"x";var i=nt(n.axis),a=n.intersect?et(e,r):tt(e,r,!1,i),o=[];return a.length?(e._getSortedVisibleDatasetMetas().forEach((function(e){var t=e.data[a[0]._index];t&&!t._view.skip&&o.push(t)})),o):[]}var it={modes:{single:function(e,t){var n=Xe(t,e),r=[];return Je(e,(function(e){if(e.inRange(n.x,n.y))return r.push(e),r})),r.slice(0,1)},label:rt,index:rt,dataset:function(e,t,n){var r=Xe(t,e);n.axis=n.axis||"xy";var i=nt(n.axis),a=n.intersect?et(e,r):tt(e,r,!1,i);return a.length>0&&(a=e.getDatasetMeta(a[0]._datasetIndex).data),a},"x-axis":function(e,t){return rt(e,t,{intersect:!1})},point:function(e,t){return et(e,Xe(t,e))},nearest:function(e,t,n){var r=Xe(t,e);n.axis=n.axis||"xy";var i=nt(n.axis);return tt(e,r,n.intersect,i)},x:function(e,t,n){var r=Xe(t,e),i=[],a=!1;return Je(e,(function(e){e.inXRange(r.x)&&i.push(e),e.inRange(r.x,r.y)&&(a=!0)})),n.intersect&&!a&&(i=[]),i},y:function(e,t,n){var r=Xe(t,e),i=[],a=!1;return Je(e,(function(e){e.inYRange(r.y)&&i.push(e),e.inRange(r.x,r.y)&&(a=!0)})),n.intersect&&!a&&(i=[]),i}}},at=V.extend;function ot(e,t){return V.where(e,(function(e){return e.pos===t}))}function lt(e,t){return e.sort((function(e,n){var r=t?n:e,i=t?e:n;return r.weight===i.weight?r.index-i.index:r.weight-i.weight}))}function st(e,t,n,r){return Math.max(e[n],t[n])+Math.max(e[r],t[r])}function ut(e,t,n){var r,i,a=n.box,o=e.maxPadding;if(n.size&&(e[n.pos]-=n.size),n.size=n.horizontal?a.height:a.width,e[n.pos]+=n.size,a.getPadding){var l=a.getPadding();o.top=Math.max(o.top,l.top),o.left=Math.max(o.left,l.left),o.bottom=Math.max(o.bottom,l.bottom),o.right=Math.max(o.right,l.right)}if(r=t.outerWidth-st(o,e,"left","right"),i=t.outerHeight-st(o,e,"top","bottom"),r!==e.w||i!==e.h){e.w=r,e.h=i;var s=n.horizontal?[r,e.w]:[i,e.h];return s[0]!==s[1]&&(!isNaN(s[0])||!isNaN(s[1]))}}function ct(e,t){var n=t.maxPadding;function r(e){var r={left:0,top:0,right:0,bottom:0};return e.forEach((function(e){r[e]=Math.max(t[e],n[e])})),r}return r(e?["left","right"]:["top","bottom"])}function dt(e,t,n){var r,i,a,o,l,s,u=[];for(r=0,i=e.length;r<i;++r)(o=(a=e[r]).box).update(a.width||t.w,a.height||t.h,ct(a.horizontal,t)),ut(t,n,a)&&(s=!0,u.length&&(l=!0)),o.fullWidth||u.push(a);return l&&dt(u,t,n)||s}function ft(e,t,n){var r,i,a,o,l=n.padding,s=t.x,u=t.y;for(r=0,i=e.length;r<i;++r)o=(a=e[r]).box,a.horizontal?(o.left=o.fullWidth?l.left:t.left,o.right=o.fullWidth?n.outerWidth-l.right:t.left+t.w,o.top=u,o.bottom=u+o.height,o.width=o.right-o.left,u=o.bottom):(o.left=s,o.right=s+o.width,o.top=t.top,o.bottom=t.top+t.h,o.height=o.bottom-o.top,s=o.right);t.x=s,t.y=u}R._set("global",{layout:{padding:{top:0,right:0,bottom:0,left:0}}});var ht,pt={defaults:{},addBox:function(e,t){e.boxes||(e.boxes=[]),t.fullWidth=t.fullWidth||!1,t.position=t.position||"top",t.weight=t.weight||0,t._layers=t._layers||function(){return[{z:0,draw:function(){t.draw.apply(t,arguments)}}]},e.boxes.push(t)},removeBox:function(e,t){var n=e.boxes?e.boxes.indexOf(t):-1;-1!==n&&e.boxes.splice(n,1)},configure:function(e,t,n){for(var r,i=["fullWidth","position","weight"],a=i.length,o=0;o<a;++o)r=i[o],n.hasOwnProperty(r)&&(t[r]=n[r])},update:function(e,t,n){if(e){var r=e.options.layout||{},i=V.options.toPadding(r.padding),a=t-i.width,o=n-i.height,l=function(e){var t=function(e){var t,n,r,i=[];for(t=0,n=(e||[]).length;t<n;++t)r=e[t],i.push({index:t,box:r,pos:r.position,horizontal:r.isHorizontal(),weight:r.weight});return i}(e),n=lt(ot(t,"left"),!0),r=lt(ot(t,"right")),i=lt(ot(t,"top"),!0),a=lt(ot(t,"bottom"));return{leftAndTop:n.concat(i),rightAndBottom:r.concat(a),chartArea:ot(t,"chartArea"),vertical:n.concat(r),horizontal:i.concat(a)}}(e.boxes),s=l.vertical,u=l.horizontal,c=Object.freeze({outerWidth:t,outerHeight:n,padding:i,availableWidth:a,vBoxMaxWidth:a/2/s.length,hBoxMaxHeight:o/2}),d=at({maxPadding:at({},i),w:a,h:o,x:i.left,y:i.top},i);!function(e,t){var n,r,i;for(n=0,r=e.length;n<r;++n)(i=e[n]).width=i.horizontal?i.box.fullWidth&&t.availableWidth:t.vBoxMaxWidth,i.height=i.horizontal&&t.hBoxMaxHeight}(s.concat(u),c),dt(s,d,c),dt(u,d,c)&&dt(s,d,c),function(e){var t=e.maxPadding;function n(n){var r=Math.max(t[n]-e[n],0);return e[n]+=r,r}e.y+=n("top"),e.x+=n("left"),n("right"),n("bottom")}(d),ft(l.leftAndTop,d,c),d.x+=d.w,d.y+=d.h,ft(l.rightAndBottom,d,c),e.chartArea={left:d.left,top:d.top,right:d.left+d.w,bottom:d.top+d.h},V.each(l.chartArea,(function(t){var n=t.box;at(n,e.chartArea),n.update(d.w,d.h)}))}}},gt=(ht=Object.freeze({__proto__:null,default:"/*\r\n * DOM element rendering detection\r\n * https://davidwalsh.name/detect-node-insertion\r\n */\r\n@keyframes chartjs-render-animation {\r\n\tfrom { opacity: 0.99; }\r\n\tto { opacity: 1; }\r\n}\r\n\r\n.chartjs-render-monitor {\r\n\tanimation: chartjs-render-animation 0.001s;\r\n}\r\n\r\n/*\r\n * DOM element resizing detection\r\n * https://github.com/marcj/css-element-queries\r\n */\r\n.chartjs-size-monitor,\r\n.chartjs-size-monitor-expand,\r\n.chartjs-size-monitor-shrink {\r\n\tposition: absolute;\r\n\tdirection: ltr;\r\n\tleft: 0;\r\n\ttop: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n\toverflow: hidden;\r\n\tpointer-events: none;\r\n\tvisibility: hidden;\r\n\tz-index: -1;\r\n}\r\n\r\n.chartjs-size-monitor-expand > div {\r\n\tposition: absolute;\r\n\twidth: 1000000px;\r\n\theight: 1000000px;\r\n\tleft: 0;\r\n\ttop: 0;\r\n}\r\n\r\n.chartjs-size-monitor-shrink > div {\r\n\tposition: absolute;\r\n\twidth: 200%;\r\n\theight: 200%;\r\n\tleft: 0;\r\n\ttop: 0;\r\n}\r\n"}))&&ht.default||ht,mt=["animationstart","webkitAnimationStart"],vt={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"};function yt(e,t){var n=V.getStyle(e,t),r=n&&n.match(/^(\d+)(\.\d+)?px$/);return r?Number(r[1]):void 0}var bt=!!function(){var e=!1;try{var t=Object.defineProperty({},"passive",{get:function(){e=!0}});window.addEventListener("e",null,t)}catch(n){}return e}()&&{passive:!0};function xt(e,t,n){e.addEventListener(t,n,bt)}function _t(e,t,n){e.removeEventListener(t,n,bt)}function wt(e,t,n,r,i){return{type:e,chart:t,native:i||null,x:void 0!==n?n:null,y:void 0!==r?r:null}}function kt(e){var t=document.createElement("div");return t.className=e||"",t}function St(e,t,n){var r=e.$chartjs||(e.$chartjs={}),i=r.resizer=function(e){var t=kt("chartjs-size-monitor"),n=kt("chartjs-size-monitor-expand"),r=kt("chartjs-size-monitor-shrink");n.appendChild(kt()),r.appendChild(kt()),t.appendChild(n),t.appendChild(r),t._reset=function(){n.scrollLeft=1e6,n.scrollTop=1e6,r.scrollLeft=1e6,r.scrollTop=1e6};var i=function(){t._reset(),e()};return xt(n,"scroll",i.bind(n,"expand")),xt(r,"scroll",i.bind(r,"shrink")),t}(function(e,t){var n=!1,r=[];return function(){r=Array.prototype.slice.call(arguments),t=t||this,n||(n=!0,V.requestAnimFrame.call(window,(function(){n=!1,e.apply(t,r)})))}}((function(){if(r.resizer){var i=n.options.maintainAspectRatio&&e.parentNode,a=i?i.clientWidth:0;t(wt("resize",n)),i&&i.clientWidth<a&&n.canvas&&t(wt("resize",n))}})));!function(e,t){var n=e.$chartjs||(e.$chartjs={}),r=n.renderProxy=function(e){"chartjs-render-animation"===e.animationName&&t()};V.each(mt,(function(t){xt(e,t,r)})),n.reflow=!!e.offsetParent,e.classList.add("chartjs-render-monitor")}(e,(function(){if(r.resizer){var t=e.parentNode;t&&t!==i.parentNode&&t.insertBefore(i,t.firstChild),i._reset()}}))}function Tt(e){var t=e.$chartjs||{},n=t.resizer;delete t.resizer,function(e){var t=e.$chartjs||{},n=t.renderProxy;n&&(V.each(mt,(function(t){_t(e,t,n)})),delete t.renderProxy),e.classList.remove("chartjs-render-monitor")}(e),n&&n.parentNode&&n.parentNode.removeChild(n)}var Mt={disableCSSInjection:!1,_enabled:"undefined"!==typeof window&&"undefined"!==typeof document,_ensureLoaded:function(e){if(!this.disableCSSInjection){var t=e.getRootNode?e.getRootNode():document;!function(e,t){var n=e.$chartjs||(e.$chartjs={});if(!n.containsStyles){n.containsStyles=!0,t="/* Chart.js */\n"+t;var r=document.createElement("style");r.setAttribute("type","text/css"),r.appendChild(document.createTextNode(t)),e.appendChild(r)}}(t.host?t:document.head,gt)}},acquireContext:function(e,t){"string"===typeof e?e=document.getElementById(e):e.length&&(e=e[0]),e&&e.canvas&&(e=e.canvas);var n=e&&e.getContext&&e.getContext("2d");return n&&n.canvas===e?(this._ensureLoaded(e),function(e,t){var n=e.style,r=e.getAttribute("height"),i=e.getAttribute("width");if(e.$chartjs={initial:{height:r,width:i,style:{display:n.display,height:n.height,width:n.width}}},n.display=n.display||"block",null===i||""===i){var a=yt(e,"width");void 0!==a&&(e.width=a)}if(null===r||""===r)if(""===e.style.height)e.height=e.width/(t.options.aspectRatio||2);else{var o=yt(e,"height");void 0!==a&&(e.height=o)}}(e,t),n):null},releaseContext:function(e){var t=e.canvas;if(t.$chartjs){var n=t.$chartjs.initial;["height","width"].forEach((function(e){var r=n[e];V.isNullOrUndef(r)?t.removeAttribute(e):t.setAttribute(e,r)})),V.each(n.style||{},(function(e,n){t.style[n]=e})),t.width=t.width,delete t.$chartjs}},addEventListener:function(e,t,n){var r=e.canvas;if("resize"!==t){var i=n.$chartjs||(n.$chartjs={});xt(r,t,(i.proxies||(i.proxies={}))[e.id+"_"+t]=function(t){n(function(e,t){var n=vt[e.type]||e.type,r=V.getRelativePosition(e,t);return wt(n,t,r.x,r.y,e)}(t,e))})}else St(r,n,e)},removeEventListener:function(e,t,n){var r=e.canvas;if("resize"!==t){var i=((n.$chartjs||{}).proxies||{})[e.id+"_"+t];i&&_t(r,t,i)}else Tt(r)}};V.addEvent=xt,V.removeEvent=_t;var Ct=Mt._enabled?Mt:{acquireContext:function(e){return e&&e.canvas&&(e=e.canvas),e&&e.getContext("2d")||null}},Pt=V.extend({initialize:function(){},acquireContext:function(){},releaseContext:function(){},addEventListener:function(){},removeEventListener:function(){}},Ct);R._set("global",{plugins:{}});var Dt={_plugins:[],_cacheId:0,register:function(e){var t=this._plugins;[].concat(e).forEach((function(e){-1===t.indexOf(e)&&t.push(e)})),this._cacheId++},unregister:function(e){var t=this._plugins;[].concat(e).forEach((function(e){var n=t.indexOf(e);-1!==n&&t.splice(n,1)})),this._cacheId++},clear:function(){this._plugins=[],this._cacheId++},count:function(){return this._plugins.length},getAll:function(){return this._plugins},notify:function(e,t,n){var r,i,a,o,l,s=this.descriptors(e),u=s.length;for(r=0;r<u;++r)if("function"===typeof(l=(a=(i=s[r]).plugin)[t])&&((o=[e].concat(n||[])).push(i.options),!1===l.apply(a,o)))return!1;return!0},descriptors:function(e){var t=e.$plugins||(e.$plugins={});if(t.id===this._cacheId)return t.descriptors;var n=[],r=[],i=e&&e.config||{},a=i.options&&i.options.plugins||{};return this._plugins.concat(i.plugins||[]).forEach((function(e){if(-1===n.indexOf(e)){var t=e.id,i=a[t];!1!==i&&(!0===i&&(i=V.clone(R.global.plugins[t])),n.push(e),r.push({plugin:e,options:i||{}}))}})),t.descriptors=r,t.id=this._cacheId,r},_invalidate:function(e){delete e.$plugins}},Et={constructors:{},defaults:{},registerScaleType:function(e,t,n){this.constructors[e]=t,this.defaults[e]=V.clone(n)},getScaleConstructor:function(e){return this.constructors.hasOwnProperty(e)?this.constructors[e]:void 0},getScaleDefaults:function(e){return this.defaults.hasOwnProperty(e)?V.merge(Object.create(null),[R.scale,this.defaults[e]]):{}},updateScaleDefaults:function(e,t){this.defaults.hasOwnProperty(e)&&(this.defaults[e]=V.extend(this.defaults[e],t))},addScalesToLayout:function(e){V.each(e.scales,(function(t){t.fullWidth=t.options.fullWidth,t.position=t.options.position,t.weight=t.options.weight,pt.addBox(e,t)}))}},Ot=V.valueOrDefault,Nt=V.rtl.getRtlAdapter;R._set("global",{tooltips:{enabled:!0,custom:null,mode:"nearest",position:"average",intersect:!0,backgroundColor:"rgba(0,0,0,0.8)",titleFontStyle:"bold",titleSpacing:2,titleMarginBottom:6,titleFontColor:"#fff",titleAlign:"left",bodySpacing:2,bodyFontColor:"#fff",bodyAlign:"left",footerFontStyle:"bold",footerSpacing:2,footerMarginTop:6,footerFontColor:"#fff",footerAlign:"left",yPadding:6,xPadding:6,caretPadding:2,caretSize:5,cornerRadius:6,multiKeyBackground:"#fff",displayColors:!0,borderColor:"rgba(0,0,0,0)",borderWidth:0,callbacks:{beforeTitle:V.noop,title:function(e,t){var n="",r=t.labels,i=r?r.length:0;if(e.length>0){var a=e[0];a.label?n=a.label:a.xLabel?n=a.xLabel:i>0&&a.index<i&&(n=r[a.index])}return n},afterTitle:V.noop,beforeBody:V.noop,beforeLabel:V.noop,label:function(e,t){var n=t.datasets[e.datasetIndex].label||"";return n&&(n+=": "),V.isNullOrUndef(e.value)?n+=e.yLabel:n+=e.value,n},labelColor:function(e,t){var n=t.getDatasetMeta(e.datasetIndex).data[e.index]._view;return{borderColor:n.borderColor,backgroundColor:n.backgroundColor}},labelTextColor:function(){return this._options.bodyFontColor},afterLabel:V.noop,afterBody:V.noop,beforeFooter:V.noop,footer:V.noop,afterFooter:V.noop}}});var It={average:function(e){if(!e.length)return!1;var t,n,r=0,i=0,a=0;for(t=0,n=e.length;t<n;++t){var o=e[t];if(o&&o.hasValue()){var l=o.tooltipPosition();r+=l.x,i+=l.y,++a}}return{x:r/a,y:i/a}},nearest:function(e,t){var n,r,i,a=t.x,o=t.y,l=Number.POSITIVE_INFINITY;for(n=0,r=e.length;n<r;++n){var s=e[n];if(s&&s.hasValue()){var u=s.getCenterPoint(),c=V.distanceBetweenPoints(t,u);c<l&&(l=c,i=s)}}if(i){var d=i.tooltipPosition();a=d.x,o=d.y}return{x:a,y:o}}};function At(e,t){return t&&(V.isArray(t)?Array.prototype.push.apply(e,t):e.push(t)),e}function Ft(e){return("string"===typeof e||e instanceof String)&&e.indexOf("\n")>-1?e.split("\n"):e}function Rt(e){var t=e._xScale,n=e._yScale||e._scale,r=e._index,i=e._datasetIndex,a=e._chart.getDatasetMeta(i).controller,o=a._getIndexScale(),l=a._getValueScale();return{xLabel:t?t.getLabelForIndex(r,i):"",yLabel:n?n.getLabelForIndex(r,i):"",label:o?""+o.getLabelForIndex(r,i):"",value:l?""+l.getLabelForIndex(r,i):"",index:r,datasetIndex:i,x:e._model.x,y:e._model.y}}function Lt(e){var t=R.global;return{xPadding:e.xPadding,yPadding:e.yPadding,xAlign:e.xAlign,yAlign:e.yAlign,rtl:e.rtl,textDirection:e.textDirection,bodyFontColor:e.bodyFontColor,_bodyFontFamily:Ot(e.bodyFontFamily,t.defaultFontFamily),_bodyFontStyle:Ot(e.bodyFontStyle,t.defaultFontStyle),_bodyAlign:e.bodyAlign,bodyFontSize:Ot(e.bodyFontSize,t.defaultFontSize),bodySpacing:e.bodySpacing,titleFontColor:e.titleFontColor,_titleFontFamily:Ot(e.titleFontFamily,t.defaultFontFamily),_titleFontStyle:Ot(e.titleFontStyle,t.defaultFontStyle),titleFontSize:Ot(e.titleFontSize,t.defaultFontSize),_titleAlign:e.titleAlign,titleSpacing:e.titleSpacing,titleMarginBottom:e.titleMarginBottom,footerFontColor:e.footerFontColor,_footerFontFamily:Ot(e.footerFontFamily,t.defaultFontFamily),_footerFontStyle:Ot(e.footerFontStyle,t.defaultFontStyle),footerFontSize:Ot(e.footerFontSize,t.defaultFontSize),_footerAlign:e.footerAlign,footerSpacing:e.footerSpacing,footerMarginTop:e.footerMarginTop,caretSize:e.caretSize,cornerRadius:e.cornerRadius,backgroundColor:e.backgroundColor,opacity:0,legendColorBackground:e.multiKeyBackground,displayColors:e.displayColors,borderColor:e.borderColor,borderWidth:e.borderWidth}}function zt(e,t){return"center"===t?e.x+e.width/2:"right"===t?e.x+e.width-e.xPadding:e.x+e.xPadding}function jt(e){return At([],Ft(e))}var Wt=G.extend({initialize:function(){this._model=Lt(this._options),this._lastActive=[]},getTitle:function(){var e=this,t=e._options,n=t.callbacks,r=n.beforeTitle.apply(e,arguments),i=n.title.apply(e,arguments),a=n.afterTitle.apply(e,arguments),o=[];return o=At(o,Ft(r)),o=At(o,Ft(i)),o=At(o,Ft(a))},getBeforeBody:function(){return jt(this._options.callbacks.beforeBody.apply(this,arguments))},getBody:function(e,t){var n=this,r=n._options.callbacks,i=[];return V.each(e,(function(e){var a={before:[],lines:[],after:[]};At(a.before,Ft(r.beforeLabel.call(n,e,t))),At(a.lines,r.label.call(n,e,t)),At(a.after,Ft(r.afterLabel.call(n,e,t))),i.push(a)})),i},getAfterBody:function(){return jt(this._options.callbacks.afterBody.apply(this,arguments))},getFooter:function(){var e=this,t=e._options.callbacks,n=t.beforeFooter.apply(e,arguments),r=t.footer.apply(e,arguments),i=t.afterFooter.apply(e,arguments),a=[];return a=At(a,Ft(n)),a=At(a,Ft(r)),a=At(a,Ft(i))},update:function(e){var t,n,r=this,i=r._options,a=r._model,o=r._model=Lt(i),l=r._active,s=r._data,u={xAlign:a.xAlign,yAlign:a.yAlign},c={x:a.x,y:a.y},d={width:a.width,height:a.height},f={x:a.caretX,y:a.caretY};if(l.length){o.opacity=1;var h=[],p=[];f=It[i.position].call(r,l,r._eventPosition);var g=[];for(t=0,n=l.length;t<n;++t)g.push(Rt(l[t]));i.filter&&(g=g.filter((function(e){return i.filter(e,s)}))),i.itemSort&&(g=g.sort((function(e,t){return i.itemSort(e,t,s)}))),V.each(g,(function(e){h.push(i.callbacks.labelColor.call(r,e,r._chart)),p.push(i.callbacks.labelTextColor.call(r,e,r._chart))})),o.title=r.getTitle(g,s),o.beforeBody=r.getBeforeBody(g,s),o.body=r.getBody(g,s),o.afterBody=r.getAfterBody(g,s),o.footer=r.getFooter(g,s),o.x=f.x,o.y=f.y,o.caretPadding=i.caretPadding,o.labelColors=h,o.labelTextColors=p,o.dataPoints=g,d=function(e,t){var n=e._chart.ctx,r=2*t.yPadding,i=0,a=t.body,o=a.reduce((function(e,t){return e+t.before.length+t.lines.length+t.after.length}),0);o+=t.beforeBody.length+t.afterBody.length;var l=t.title.length,s=t.footer.length,u=t.titleFontSize,c=t.bodyFontSize,d=t.footerFontSize;r+=l*u,r+=l?(l-1)*t.titleSpacing:0,r+=l?t.titleMarginBottom:0,r+=o*c,r+=o?(o-1)*t.bodySpacing:0,r+=s?t.footerMarginTop:0,r+=s*d,r+=s?(s-1)*t.footerSpacing:0;var f=0,h=function(e){i=Math.max(i,n.measureText(e).width+f)};return n.font=V.fontString(u,t._titleFontStyle,t._titleFontFamily),V.each(t.title,h),n.font=V.fontString(c,t._bodyFontStyle,t._bodyFontFamily),V.each(t.beforeBody.concat(t.afterBody),h),f=t.displayColors?c+2:0,V.each(a,(function(e){V.each(e.before,h),V.each(e.lines,h),V.each(e.after,h)})),f=0,n.font=V.fontString(d,t._footerFontStyle,t._footerFontFamily),V.each(t.footer,h),{width:i+=2*t.xPadding,height:r}}(this,o),c=function(e,t,n,r){var i=e.x,a=e.y,o=e.caretSize,l=e.caretPadding,s=e.cornerRadius,u=n.xAlign,c=n.yAlign,d=o+l,f=s+l;return"right"===u?i-=t.width:"center"===u&&((i-=t.width/2)+t.width>r.width&&(i=r.width-t.width),i<0&&(i=0)),"top"===c?a+=d:a-="bottom"===c?t.height+d:t.height/2,"center"===c?"left"===u?i+=d:"right"===u&&(i-=d):"left"===u?i-=f:"right"===u&&(i+=f),{x:i,y:a}}(o,d,u=function(e,t){var n,r,i,a,o,l=e._model,s=e._chart,u=e._chart.chartArea,c="center",d="center";l.y<t.height?d="top":l.y>s.height-t.height&&(d="bottom");var f=(u.left+u.right)/2,h=(u.top+u.bottom)/2;"center"===d?(n=function(e){return e<=f},r=function(e){return e>f}):(n=function(e){return e<=t.width/2},r=function(e){return e>=s.width-t.width/2}),i=function(e){return e+t.width+l.caretSize+l.caretPadding>s.width},a=function(e){return e-t.width-l.caretSize-l.caretPadding<0},o=function(e){return e<=h?"top":"bottom"},n(l.x)?(c="left",i(l.x)&&(c="center",d=o(l.y))):r(l.x)&&(c="right",a(l.x)&&(c="center",d=o(l.y)));var p=e._options;return{xAlign:p.xAlign?p.xAlign:c,yAlign:p.yAlign?p.yAlign:d}}(this,d),r._chart)}else o.opacity=0;return o.xAlign=u.xAlign,o.yAlign=u.yAlign,o.x=c.x,o.y=c.y,o.width=d.width,o.height=d.height,o.caretX=f.x,o.caretY=f.y,r._model=o,e&&i.custom&&i.custom.call(r,o),r},drawCaret:function(e,t){var n=this._chart.ctx,r=this._view,i=this.getCaretPosition(e,t,r);n.lineTo(i.x1,i.y1),n.lineTo(i.x2,i.y2),n.lineTo(i.x3,i.y3)},getCaretPosition:function(e,t,n){var r,i,a,o,l,s,u=n.caretSize,c=n.cornerRadius,d=n.xAlign,f=n.yAlign,h=e.x,p=e.y,g=t.width,m=t.height;if("center"===f)l=p+m/2,"left"===d?(i=(r=h)-u,a=r,o=l+u,s=l-u):(i=(r=h+g)+u,a=r,o=l-u,s=l+u);else if("left"===d?(r=(i=h+c+u)-u,a=i+u):"right"===d?(r=(i=h+g-c-u)-u,a=i+u):(r=(i=n.caretX)-u,a=i+u),"top"===f)l=(o=p)-u,s=o;else{l=(o=p+m)+u,s=o;var v=a;a=r,r=v}return{x1:r,x2:i,x3:a,y1:o,y2:l,y3:s}},drawTitle:function(e,t,n){var r,i,a,o=t.title,l=o.length;if(l){var s=Nt(t.rtl,t.x,t.width);for(e.x=zt(t,t._titleAlign),n.textAlign=s.textAlign(t._titleAlign),n.textBaseline="middle",r=t.titleFontSize,i=t.titleSpacing,n.fillStyle=t.titleFontColor,n.font=V.fontString(r,t._titleFontStyle,t._titleFontFamily),a=0;a<l;++a)n.fillText(o[a],s.x(e.x),e.y+r/2),e.y+=r+i,a+1===l&&(e.y+=t.titleMarginBottom-i)}},drawBody:function(e,t,n){var r,i,a,o,l,s,u,c,d=t.bodyFontSize,f=t.bodySpacing,h=t._bodyAlign,p=t.body,g=t.displayColors,m=0,v=g?zt(t,"left"):0,y=Nt(t.rtl,t.x,t.width),b=function(t){n.fillText(t,y.x(e.x+m),e.y+d/2),e.y+=d+f},x=y.textAlign(h);for(n.textAlign=h,n.textBaseline="middle",n.font=V.fontString(d,t._bodyFontStyle,t._bodyFontFamily),e.x=zt(t,x),n.fillStyle=t.bodyFontColor,V.each(t.beforeBody,b),m=g&&"right"!==x?"center"===h?d/2+1:d+2:0,l=0,u=p.length;l<u;++l){for(r=p[l],i=t.labelTextColors[l],a=t.labelColors[l],n.fillStyle=i,V.each(r.before,b),s=0,c=(o=r.lines).length;s<c;++s){if(g){var _=y.x(v);n.fillStyle=t.legendColorBackground,n.fillRect(y.leftForLtr(_,d),e.y,d,d),n.lineWidth=1,n.strokeStyle=a.borderColor,n.strokeRect(y.leftForLtr(_,d),e.y,d,d),n.fillStyle=a.backgroundColor,n.fillRect(y.leftForLtr(y.xPlus(_,1),d-2),e.y+1,d-2,d-2),n.fillStyle=i}b(o[s])}V.each(r.after,b)}m=0,V.each(t.afterBody,b),e.y-=f},drawFooter:function(e,t,n){var r,i,a=t.footer,o=a.length;if(o){var l=Nt(t.rtl,t.x,t.width);for(e.x=zt(t,t._footerAlign),e.y+=t.footerMarginTop,n.textAlign=l.textAlign(t._footerAlign),n.textBaseline="middle",r=t.footerFontSize,n.fillStyle=t.footerFontColor,n.font=V.fontString(r,t._footerFontStyle,t._footerFontFamily),i=0;i<o;++i)n.fillText(a[i],l.x(e.x),e.y+r/2),e.y+=r+t.footerSpacing}},drawBackground:function(e,t,n,r){n.fillStyle=t.backgroundColor,n.strokeStyle=t.borderColor,n.lineWidth=t.borderWidth;var i=t.xAlign,a=t.yAlign,o=e.x,l=e.y,s=r.width,u=r.height,c=t.cornerRadius;n.beginPath(),n.moveTo(o+c,l),"top"===a&&this.drawCaret(e,r),n.lineTo(o+s-c,l),n.quadraticCurveTo(o+s,l,o+s,l+c),"center"===a&&"right"===i&&this.drawCaret(e,r),n.lineTo(o+s,l+u-c),n.quadraticCurveTo(o+s,l+u,o+s-c,l+u),"bottom"===a&&this.drawCaret(e,r),n.lineTo(o+c,l+u),n.quadraticCurveTo(o,l+u,o,l+u-c),"center"===a&&"left"===i&&this.drawCaret(e,r),n.lineTo(o,l+c),n.quadraticCurveTo(o,l,o+c,l),n.closePath(),n.fill(),t.borderWidth>0&&n.stroke()},draw:function(){var e=this._chart.ctx,t=this._view;if(0!==t.opacity){var n={width:t.width,height:t.height},r={x:t.x,y:t.y},i=Math.abs(t.opacity<.001)?0:t.opacity,a=t.title.length||t.beforeBody.length||t.body.length||t.afterBody.length||t.footer.length;this._options.enabled&&a&&(e.save(),e.globalAlpha=i,this.drawBackground(r,t,e,n),r.y+=t.yPadding,V.rtl.overrideTextDirection(e,t.textDirection),this.drawTitle(r,t,e),this.drawBody(r,t,e),this.drawFooter(r,t,e),V.rtl.restoreTextDirection(e,t.textDirection),e.restore())}},handleEvent:function(e){var t,n=this,r=n._options;return n._lastActive=n._lastActive||[],"mouseout"===e.type?n._active=[]:(n._active=n._chart.getElementsAtEventForMode(e,r.mode,r),r.reverse&&n._active.reverse()),(t=!V.arrayEquals(n._active,n._lastActive))&&(n._lastActive=n._active,(r.enabled||r.custom)&&(n._eventPosition={x:e.x,y:e.y},n.update(!0),n.pivot())),t}}),Vt=It,Yt=Wt;Yt.positioners=Vt;var Bt=V.valueOrDefault;function Ht(){return V.merge(Object.create(null),[].slice.call(arguments),{merger:function(e,t,n,r){if("xAxes"===e||"yAxes"===e){var i,a,o,l=n[e].length;for(t[e]||(t[e]=[]),i=0;i<l;++i)o=n[e][i],a=Bt(o.type,"xAxes"===e?"category":"linear"),i>=t[e].length&&t[e].push({}),!t[e][i].type||o.type&&o.type!==t[e][i].type?V.merge(t[e][i],[Et.getScaleDefaults(a),o]):V.merge(t[e][i],o)}else V._merger(e,t,n,r)}})}function Ut(){return V.merge(Object.create(null),[].slice.call(arguments),{merger:function(e,t,n,r){var i=t[e]||Object.create(null),a=n[e];"scales"===e?t[e]=Ht(i,a):"scale"===e?t[e]=V.merge(i,[Et.getScaleDefaults(a.type),a]):V._merger(e,t,n,r)}})}function qt(e){var t=e.options;V.each(e.scales,(function(t){pt.removeBox(e,t)})),t=Ut(R.global,R[e.config.type],t),e.options=e.config.options=t,e.ensureScalesHaveIDs(),e.buildOrUpdateScales(),e.tooltip._options=t.tooltips,e.tooltip.initialize()}function $t(e,t,n){var r,i=function(e){return e.id===r};do{r=t+n++}while(V.findIndex(e,i)>=0);return r}function Gt(e){return"top"===e||"bottom"===e}function Qt(e,t){return function(n,r){return n[e]===r[e]?n[t]-r[t]:n[e]-r[e]}}R._set("global",{elements:{},events:["mousemove","mouseout","click","touchstart","touchmove"],hover:{onHover:null,mode:"nearest",intersect:!0,animationDuration:400},onClick:null,maintainAspectRatio:!0,responsive:!0,responsiveAnimationDuration:0});var Kt=function(e,t){return this.construct(e,t),this};V.extend(Kt.prototype,{construct:function(e,t){var n=this;t=function(e){var t=(e=e||Object.create(null)).data=e.data||{};return t.datasets=t.datasets||[],t.labels=t.labels||[],e.options=Ut(R.global,R[e.type],e.options||{}),e}(t);var r=Pt.acquireContext(e,t),i=r&&r.canvas,a=i&&i.height,o=i&&i.width;n.id=V.uid(),n.ctx=r,n.canvas=i,n.config=t,n.width=o,n.height=a,n.aspectRatio=a?o/a:null,n.options=t.options,n._bufferedRender=!1,n._layers=[],n.chart=n,n.controller=n,Kt.instances[n.id]=n,Object.defineProperty(n,"data",{get:function(){return n.config.data},set:function(e){n.config.data=e}}),r&&i?(n.initialize(),n.update()):console.error("Failed to create chart: can't acquire context from the given item")},initialize:function(){var e=this;return Dt.notify(e,"beforeInit"),V.retinaScale(e,e.options.devicePixelRatio),e.bindEvents(),e.options.responsive&&e.resize(!0),e.initToolTip(),Dt.notify(e,"afterInit"),e},clear:function(){return V.canvas.clear(this),this},stop:function(){return Z.cancelAnimation(this),this},resize:function(e){var t=this,n=t.options,r=t.canvas,i=n.maintainAspectRatio&&t.aspectRatio||null,a=Math.max(0,Math.floor(V.getMaximumWidth(r))),o=Math.max(0,Math.floor(i?a/i:V.getMaximumHeight(r)));if((t.width!==a||t.height!==o)&&(r.width=t.width=a,r.height=t.height=o,r.style.width=a+"px",r.style.height=o+"px",V.retinaScale(t,n.devicePixelRatio),!e)){var l={width:a,height:o};Dt.notify(t,"resize",[l]),n.onResize&&n.onResize(t,l),t.stop(),t.update({duration:n.responsiveAnimationDuration})}},ensureScalesHaveIDs:function(){var e=this.options,t=e.scales||{},n=e.scale;V.each(t.xAxes,(function(e,n){e.id||(e.id=$t(t.xAxes,"x-axis-",n))})),V.each(t.yAxes,(function(e,n){e.id||(e.id=$t(t.yAxes,"y-axis-",n))})),n&&(n.id=n.id||"scale")},buildOrUpdateScales:function(){var e=this,t=e.options,n=e.scales||{},r=[],i=Object.keys(n).reduce((function(e,t){return e[t]=!1,e}),{});t.scales&&(r=r.concat((t.scales.xAxes||[]).map((function(e){return{options:e,dtype:"category",dposition:"bottom"}})),(t.scales.yAxes||[]).map((function(e){return{options:e,dtype:"linear",dposition:"left"}})))),t.scale&&r.push({options:t.scale,dtype:"radialLinear",isDefault:!0,dposition:"chartArea"}),V.each(r,(function(t){var r=t.options,a=r.id,o=Bt(r.type,t.dtype);Gt(r.position)!==Gt(t.dposition)&&(r.position=t.dposition),i[a]=!0;var l=null;if(a in n&&n[a].type===o)(l=n[a]).options=r,l.ctx=e.ctx,l.chart=e;else{var s=Et.getScaleConstructor(o);if(!s)return;l=new s({id:a,type:o,options:r,ctx:e.ctx,chart:e}),n[l.id]=l}l.mergeTicksOptions(),t.isDefault&&(e.scale=l)})),V.each(i,(function(e,t){e||delete n[t]})),e.scales=n,Et.addScalesToLayout(this)},buildOrUpdateControllers:function(){var e,t,n=this,r=[],i=n.data.datasets;for(e=0,t=i.length;e<t;e++){var a=i[e],o=n.getDatasetMeta(e),l=a.type||n.config.type;if(o.type&&o.type!==l&&(n.destroyDatasetMeta(e),o=n.getDatasetMeta(e)),o.type=l,o.order=a.order||0,o.index=e,o.controller)o.controller.updateIndex(e),o.controller.linkScales();else{var s=Ze[o.type];if(void 0===s)throw new Error('"'+o.type+'" is not a chart type.');o.controller=new s(n,e),r.push(o.controller)}}return r},resetElements:function(){var e=this;V.each(e.data.datasets,(function(t,n){e.getDatasetMeta(n).controller.reset()}),e)},reset:function(){this.resetElements(),this.tooltip.initialize()},update:function(e){var t,n,r=this;if(e&&"object"===typeof e||(e={duration:e,lazy:arguments[1]}),qt(r),Dt._invalidate(r),!1!==Dt.notify(r,"beforeUpdate")){r.tooltip._data=r.data;var i=r.buildOrUpdateControllers();for(t=0,n=r.data.datasets.length;t<n;t++)r.getDatasetMeta(t).controller.buildOrUpdateElements();r.updateLayout(),r.options.animation&&r.options.animation.duration&&V.each(i,(function(e){e.reset()})),r.updateDatasets(),r.tooltip.initialize(),r.lastActive=[],Dt.notify(r,"afterUpdate"),r._layers.sort(Qt("z","_idx")),r._bufferedRender?r._bufferedRequest={duration:e.duration,easing:e.easing,lazy:e.lazy}:r.render(e)}},updateLayout:function(){var e=this;!1!==Dt.notify(e,"beforeLayout")&&(pt.update(this,this.width,this.height),e._layers=[],V.each(e.boxes,(function(t){t._configure&&t._configure(),e._layers.push.apply(e._layers,t._layers())}),e),e._layers.forEach((function(e,t){e._idx=t})),Dt.notify(e,"afterScaleUpdate"),Dt.notify(e,"afterLayout"))},updateDatasets:function(){if(!1!==Dt.notify(this,"beforeDatasetsUpdate")){for(var e=0,t=this.data.datasets.length;e<t;++e)this.updateDataset(e);Dt.notify(this,"afterDatasetsUpdate")}},updateDataset:function(e){var t=this.getDatasetMeta(e),n={meta:t,index:e};!1!==Dt.notify(this,"beforeDatasetUpdate",[n])&&(t.controller._update(),Dt.notify(this,"afterDatasetUpdate",[n]))},render:function(e){var t=this;e&&"object"===typeof e||(e={duration:e,lazy:arguments[1]});var n=t.options.animation,r=Bt(e.duration,n&&n.duration),i=e.lazy;if(!1!==Dt.notify(t,"beforeRender")){var a=function(e){Dt.notify(t,"afterRender"),V.callback(n&&n.onComplete,[e],t)};if(n&&r){var o=new K({numSteps:r/16.66,easing:e.easing||n.easing,render:function(e,t){var n=V.easing.effects[t.easing],r=t.currentStep,i=r/t.numSteps;e.draw(n(i),i,r)},onAnimationProgress:n.onProgress,onAnimationComplete:a});Z.addAnimation(t,o,r,i)}else t.draw(),a(new K({numSteps:0,chart:t}));return t}},draw:function(e){var t,n,r=this;if(r.clear(),V.isNullOrUndef(e)&&(e=1),r.transition(e),!(r.width<=0||r.height<=0)&&!1!==Dt.notify(r,"beforeDraw",[e])){for(n=r._layers,t=0;t<n.length&&n[t].z<=0;++t)n[t].draw(r.chartArea);for(r.drawDatasets(e);t<n.length;++t)n[t].draw(r.chartArea);r._drawTooltip(e),Dt.notify(r,"afterDraw",[e])}},transition:function(e){for(var t=0,n=(this.data.datasets||[]).length;t<n;++t)this.isDatasetVisible(t)&&this.getDatasetMeta(t).controller.transition(e);this.tooltip.transition(e)},_getSortedDatasetMetas:function(e){var t,n,r=[];for(t=0,n=(this.data.datasets||[]).length;t<n;++t)e&&!this.isDatasetVisible(t)||r.push(this.getDatasetMeta(t));return r.sort(Qt("order","index")),r},_getSortedVisibleDatasetMetas:function(){return this._getSortedDatasetMetas(!0)},drawDatasets:function(e){var t,n;if(!1!==Dt.notify(this,"beforeDatasetsDraw",[e])){for(n=(t=this._getSortedVisibleDatasetMetas()).length-1;n>=0;--n)this.drawDataset(t[n],e);Dt.notify(this,"afterDatasetsDraw",[e])}},drawDataset:function(e,t){var n={meta:e,index:e.index,easingValue:t};!1!==Dt.notify(this,"beforeDatasetDraw",[n])&&(e.controller.draw(t),Dt.notify(this,"afterDatasetDraw",[n]))},_drawTooltip:function(e){var t=this.tooltip,n={tooltip:t,easingValue:e};!1!==Dt.notify(this,"beforeTooltipDraw",[n])&&(t.draw(),Dt.notify(this,"afterTooltipDraw",[n]))},getElementAtEvent:function(e){return it.modes.single(this,e)},getElementsAtEvent:function(e){return it.modes.label(this,e,{intersect:!0})},getElementsAtXAxis:function(e){return it.modes["x-axis"](this,e,{intersect:!0})},getElementsAtEventForMode:function(e,t,n){var r=it.modes[t];return"function"===typeof r?r(this,e,n):[]},getDatasetAtEvent:function(e){return it.modes.dataset(this,e,{intersect:!0})},getDatasetMeta:function(e){var t=this.data.datasets[e];t._meta||(t._meta={});var n=t._meta[this.id];return n||(n=t._meta[this.id]={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:t.order||0,index:e}),n},getVisibleDatasetCount:function(){for(var e=0,t=0,n=this.data.datasets.length;t<n;++t)this.isDatasetVisible(t)&&e++;return e},isDatasetVisible:function(e){var t=this.getDatasetMeta(e);return"boolean"===typeof t.hidden?!t.hidden:!this.data.datasets[e].hidden},generateLegend:function(){return this.options.legendCallback(this)},destroyDatasetMeta:function(e){var t=this.id,n=this.data.datasets[e],r=n._meta&&n._meta[t];r&&(r.controller.destroy(),delete n._meta[t])},destroy:function(){var e,t,n=this,r=n.canvas;for(n.stop(),e=0,t=n.data.datasets.length;e<t;++e)n.destroyDatasetMeta(e);r&&(n.unbindEvents(),V.canvas.clear(n),Pt.releaseContext(n.ctx),n.canvas=null,n.ctx=null),Dt.notify(n,"destroy"),delete Kt.instances[n.id]},toBase64Image:function(){return this.canvas.toDataURL.apply(this.canvas,arguments)},initToolTip:function(){var e=this;e.tooltip=new Yt({_chart:e,_chartInstance:e,_data:e.data,_options:e.options.tooltips},e)},bindEvents:function(){var e=this,t=e._listeners={},n=function(){e.eventHandler.apply(e,arguments)};V.each(e.options.events,(function(r){Pt.addEventListener(e,r,n),t[r]=n})),e.options.responsive&&(n=function(){e.resize()},Pt.addEventListener(e,"resize",n),t.resize=n)},unbindEvents:function(){var e=this,t=e._listeners;t&&(delete e._listeners,V.each(t,(function(t,n){Pt.removeEventListener(e,n,t)})))},updateHoverStyle:function(e,t,n){var r,i,a,o=n?"set":"remove";for(i=0,a=e.length;i<a;++i)(r=e[i])&&this.getDatasetMeta(r._datasetIndex).controller[o+"HoverStyle"](r);"dataset"===t&&this.getDatasetMeta(e[0]._datasetIndex).controller["_"+o+"DatasetHoverStyle"]()},eventHandler:function(e){var t=this,n=t.tooltip;if(!1!==Dt.notify(t,"beforeEvent",[e])){t._bufferedRender=!0,t._bufferedRequest=null;var r=t.handleEvent(e);n&&(r=n._start?n.handleEvent(e):r|n.handleEvent(e)),Dt.notify(t,"afterEvent",[e]);var i=t._bufferedRequest;return i?t.render(i):r&&!t.animating&&(t.stop(),t.render({duration:t.options.hover.animationDuration,lazy:!0})),t._bufferedRender=!1,t._bufferedRequest=null,t}},handleEvent:function(e){var t,n=this,r=n.options||{},i=r.hover;return n.lastActive=n.lastActive||[],"mouseout"===e.type?n.active=[]:n.active=n.getElementsAtEventForMode(e,i.mode,i),V.callback(r.onHover||r.hover.onHover,[e.native,n.active],n),"mouseup"!==e.type&&"click"!==e.type||r.onClick&&r.onClick.call(n,e.native,n.active),n.lastActive.length&&n.updateHoverStyle(n.lastActive,i.mode,!1),n.active.length&&i.mode&&n.updateHoverStyle(n.active,i.mode,!0),t=!V.arrayEquals(n.active,n.lastActive),n.lastActive=n.active,t}}),Kt.instances={};var Zt=Kt;function Xt(){throw new Error("This method is not implemented: either no adapter can be found or an incomplete integration was provided.")}function Jt(e){this.options=e||{}}Kt.Controller=Kt,Kt.types={},V.configMerge=Ut,V.scaleMerge=Ht,V.extend(Jt.prototype,{formats:Xt,parse:Xt,format:Xt,add:Xt,diff:Xt,startOf:Xt,endOf:Xt,_create:function(e){return e}}),Jt.override=function(e){V.extend(Jt.prototype,e)};var en={_date:Jt},tn={formatters:{values:function(e){return V.isArray(e)?e:""+e},linear:function(e,t,n){var r=n.length>3?n[2]-n[1]:n[1]-n[0];Math.abs(r)>1&&e!==Math.floor(e)&&(r=e-Math.floor(e));var i=V.log10(Math.abs(r)),a="";if(0!==e)if(Math.max(Math.abs(n[0]),Math.abs(n[n.length-1]))<1e-4){var o=V.log10(Math.abs(e)),l=Math.floor(o)-Math.floor(i);l=Math.max(Math.min(l,20),0),a=e.toExponential(l)}else{var s=-1*Math.floor(i);s=Math.max(Math.min(s,20),0),a=e.toFixed(s)}else a="0";return a},logarithmic:function(e,t,n){var r=e/Math.pow(10,Math.floor(V.log10(e)));return 0===e?"0":1===r||2===r||5===r||0===t||t===n.length-1?e.toExponential():""}}},nn=V.isArray,rn=V.isNullOrUndef,an=V.valueOrDefault,on=V.valueAtIndexOrDefault;function ln(e,t,n){var r,i=e.getTicks().length,a=Math.min(t,i-1),o=e.getPixelForTick(a),l=e._startPixel,s=e._endPixel;if(!(n&&(r=1===i?Math.max(o-l,s-o):0===t?(e.getPixelForTick(1)-o)/2:(o-e.getPixelForTick(a-1))/2,(o+=a<t?r:-r)<l-1e-6||o>s+1e-6)))return o}function sn(e,t,n,r){var i,a,o,l,s,u,c,d,f,h,p,g,m,v=n.length,y=[],b=[],x=[],_=0,w=0;for(i=0;i<v;++i){if(l=n[i].label,s=n[i].major?t.major:t.minor,e.font=u=s.string,c=r[u]=r[u]||{data:{},gc:[]},d=s.lineHeight,f=h=0,rn(l)||nn(l)){if(nn(l))for(a=0,o=l.length;a<o;++a)p=l[a],rn(p)||nn(p)||(f=V.measureText(e,c.data,c.gc,f,p),h+=d)}else f=V.measureText(e,c.data,c.gc,f,l),h=d;y.push(f),b.push(h),x.push(d/2),_=Math.max(f,_),w=Math.max(h,w)}function k(e){return{width:y[e]||0,height:b[e]||0,offset:x[e]||0}}return function(e,t){V.each(e,(function(e){var n,r=e.gc,i=r.length/2;if(i>t){for(n=0;n<i;++n)delete e.data[r[n]];r.splice(0,i)}}))}(r,v),g=y.indexOf(_),m=b.indexOf(w),{first:k(0),last:k(v-1),widest:k(g),highest:k(m)}}function un(e){return e.drawTicks?e.tickMarkLength:0}function cn(e){var t,n;return e.display?(t=V.options._parseFont(e),n=V.options.toPadding(e.padding),t.lineHeight+n.height):0}function dn(e,t){return V.extend(V.options._parseFont({fontFamily:an(t.fontFamily,e.fontFamily),fontSize:an(t.fontSize,e.fontSize),fontStyle:an(t.fontStyle,e.fontStyle),lineHeight:an(t.lineHeight,e.lineHeight)}),{color:V.options.resolve([t.fontColor,e.fontColor,R.global.defaultFontColor])})}function fn(e){var t=dn(e,e.minor);return{minor:t,major:e.major.enabled?dn(e,e.major):t}}function hn(e){var t,n,r,i=[];for(n=0,r=e.length;n<r;++n)"undefined"!==typeof(t=e[n])._index&&i.push(t);return i}function pn(e,t,n,r){var i,a,o,l,s=an(n,0),u=Math.min(an(r,e.length),e.length),c=0;for(t=Math.ceil(t),r&&(t=(i=r-n)/Math.floor(i/t)),l=s;l<0;)c++,l=Math.round(s+c*t);for(a=Math.max(s,0);a<u;a++)o=e[a],a===l?(o._index=a,c++,l=Math.round(s+c*t)):delete o.label}R._set("scale",{display:!0,position:"left",offset:!1,gridLines:{display:!0,color:"rgba(0,0,0,0.1)",lineWidth:1,drawBorder:!0,drawOnChartArea:!0,drawTicks:!0,tickMarkLength:10,zeroLineWidth:1,zeroLineColor:"rgba(0,0,0,0.25)",zeroLineBorderDash:[],zeroLineBorderDashOffset:0,offsetGridLines:!1,borderDash:[],borderDashOffset:0},scaleLabel:{display:!1,labelString:"",padding:{top:4,bottom:4}},ticks:{beginAtZero:!1,minRotation:0,maxRotation:50,mirror:!1,padding:0,reverse:!1,display:!0,autoSkip:!0,autoSkipPadding:0,labelOffset:0,callback:tn.formatters.values,minor:{},major:{}}});var gn=G.extend({zeroLineIndex:0,getPadding:function(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}},getTicks:function(){return this._ticks},_getLabels:function(){var e=this.chart.data;return this.options.labels||(this.isHorizontal()?e.xLabels:e.yLabels)||e.labels||[]},mergeTicksOptions:function(){},beforeUpdate:function(){V.callback(this.options.beforeUpdate,[this])},update:function(e,t,n){var r,i,a,o,l,s=this,u=s.options.ticks,c=u.sampleSize;if(s.beforeUpdate(),s.maxWidth=e,s.maxHeight=t,s.margins=V.extend({left:0,right:0,top:0,bottom:0},n),s._ticks=null,s.ticks=null,s._labelSizes=null,s._maxLabelLines=0,s.longestLabelWidth=0,s.longestTextCache=s.longestTextCache||{},s._gridLineItems=null,s._labelItems=null,s.beforeSetDimensions(),s.setDimensions(),s.afterSetDimensions(),s.beforeDataLimits(),s.determineDataLimits(),s.afterDataLimits(),s.beforeBuildTicks(),o=s.buildTicks()||[],(!(o=s.afterBuildTicks(o)||o)||!o.length)&&s.ticks)for(o=[],r=0,i=s.ticks.length;r<i;++r)o.push({value:s.ticks[r],major:!1});return s._ticks=o,l=c<o.length,a=s._convertTicksToLabels(l?function(e,t){for(var n=[],r=e.length/t,i=0,a=e.length;i<a;i+=r)n.push(e[Math.floor(i)]);return n}(o,c):o),s._configure(),s.beforeCalculateTickRotation(),s.calculateTickRotation(),s.afterCalculateTickRotation(),s.beforeFit(),s.fit(),s.afterFit(),s._ticksToDraw=u.display&&(u.autoSkip||"auto"===u.source)?s._autoSkip(o):o,l&&(a=s._convertTicksToLabels(s._ticksToDraw)),s.ticks=a,s.afterUpdate(),s.minSize},_configure:function(){var e,t,n=this,r=n.options.ticks.reverse;n.isHorizontal()?(e=n.left,t=n.right):(e=n.top,t=n.bottom,r=!r),n._startPixel=e,n._endPixel=t,n._reversePixels=r,n._length=t-e},afterUpdate:function(){V.callback(this.options.afterUpdate,[this])},beforeSetDimensions:function(){V.callback(this.options.beforeSetDimensions,[this])},setDimensions:function(){var e=this;e.isHorizontal()?(e.width=e.maxWidth,e.left=0,e.right=e.width):(e.height=e.maxHeight,e.top=0,e.bottom=e.height),e.paddingLeft=0,e.paddingTop=0,e.paddingRight=0,e.paddingBottom=0},afterSetDimensions:function(){V.callback(this.options.afterSetDimensions,[this])},beforeDataLimits:function(){V.callback(this.options.beforeDataLimits,[this])},determineDataLimits:V.noop,afterDataLimits:function(){V.callback(this.options.afterDataLimits,[this])},beforeBuildTicks:function(){V.callback(this.options.beforeBuildTicks,[this])},buildTicks:V.noop,afterBuildTicks:function(e){var t=this;return nn(e)&&e.length?V.callback(t.options.afterBuildTicks,[t,e]):(t.ticks=V.callback(t.options.afterBuildTicks,[t,t.ticks])||t.ticks,e)},beforeTickToLabelConversion:function(){V.callback(this.options.beforeTickToLabelConversion,[this])},convertTicksToLabels:function(){var e=this.options.ticks;this.ticks=this.ticks.map(e.userCallback||e.callback,this)},afterTickToLabelConversion:function(){V.callback(this.options.afterTickToLabelConversion,[this])},beforeCalculateTickRotation:function(){V.callback(this.options.beforeCalculateTickRotation,[this])},calculateTickRotation:function(){var e,t,n,r,i,a,o,l=this,s=l.options,u=s.ticks,c=l.getTicks().length,d=u.minRotation||0,f=u.maxRotation,h=d;!l._isVisible()||!u.display||d>=f||c<=1||!l.isHorizontal()?l.labelRotation=d:(t=(e=l._getLabelSizes()).widest.width,n=e.highest.height-e.highest.offset,r=Math.min(l.maxWidth,l.chart.width-t),t+6>(i=s.offset?l.maxWidth/c:r/(c-1))&&(i=r/(c-(s.offset?.5:1)),a=l.maxHeight-un(s.gridLines)-u.padding-cn(s.scaleLabel),o=Math.sqrt(t*t+n*n),h=V.toDegrees(Math.min(Math.asin(Math.min((e.highest.height+6)/i,1)),Math.asin(Math.min(a/o,1))-Math.asin(n/o))),h=Math.max(d,Math.min(f,h))),l.labelRotation=h)},afterCalculateTickRotation:function(){V.callback(this.options.afterCalculateTickRotation,[this])},beforeFit:function(){V.callback(this.options.beforeFit,[this])},fit:function(){var e=this,t=e.minSize={width:0,height:0},n=e.chart,r=e.options,i=r.ticks,a=r.scaleLabel,o=r.gridLines,l=e._isVisible(),s="bottom"===r.position,u=e.isHorizontal();if(u?t.width=e.maxWidth:l&&(t.width=un(o)+cn(a)),u?l&&(t.height=un(o)+cn(a)):t.height=e.maxHeight,i.display&&l){var c=fn(i),d=e._getLabelSizes(),f=d.first,h=d.last,p=d.widest,g=d.highest,m=.4*c.minor.lineHeight,v=i.padding;if(u){var y=0!==e.labelRotation,b=V.toRadians(e.labelRotation),x=Math.cos(b),_=Math.sin(b),w=_*p.width+x*(g.height-(y?g.offset:0))+(y?0:m);t.height=Math.min(e.maxHeight,t.height+w+v);var k,S,T=e.getPixelForTick(0)-e.left,M=e.right-e.getPixelForTick(e.getTicks().length-1);y?(k=s?x*f.width+_*f.offset:_*(f.height-f.offset),S=s?_*(h.height-h.offset):x*h.width+_*h.offset):(k=f.width/2,S=h.width/2),e.paddingLeft=Math.max((k-T)*e.width/(e.width-T),0)+3,e.paddingRight=Math.max((S-M)*e.width/(e.width-M),0)+3}else{var C=i.mirror?0:p.width+v+m;t.width=Math.min(e.maxWidth,t.width+C),e.paddingTop=f.height/2,e.paddingBottom=h.height/2}}e.handleMargins(),u?(e.width=e._length=n.width-e.margins.left-e.margins.right,e.height=t.height):(e.width=t.width,e.height=e._length=n.height-e.margins.top-e.margins.bottom)},handleMargins:function(){var e=this;e.margins&&(e.margins.left=Math.max(e.paddingLeft,e.margins.left),e.margins.top=Math.max(e.paddingTop,e.margins.top),e.margins.right=Math.max(e.paddingRight,e.margins.right),e.margins.bottom=Math.max(e.paddingBottom,e.margins.bottom))},afterFit:function(){V.callback(this.options.afterFit,[this])},isHorizontal:function(){var e=this.options.position;return"top"===e||"bottom"===e},isFullWidth:function(){return this.options.fullWidth},getRightValue:function(e){if(rn(e))return NaN;if(("number"===typeof e||e instanceof Number)&&!isFinite(e))return NaN;if(e)if(this.isHorizontal()){if(void 0!==e.x)return this.getRightValue(e.x)}else if(void 0!==e.y)return this.getRightValue(e.y);return e},_convertTicksToLabels:function(e){var t,n,r,i=this;for(i.ticks=e.map((function(e){return e.value})),i.beforeTickToLabelConversion(),t=i.convertTicksToLabels(e)||i.ticks,i.afterTickToLabelConversion(),n=0,r=e.length;n<r;++n)e[n].label=t[n];return t},_getLabelSizes:function(){var e=this,t=e._labelSizes;return t||(e._labelSizes=t=sn(e.ctx,fn(e.options.ticks),e.getTicks(),e.longestTextCache),e.longestLabelWidth=t.widest.width),t},_parseValue:function(e){var t,n,r,i;return nn(e)?(t=+this.getRightValue(e[0]),n=+this.getRightValue(e[1]),r=Math.min(t,n),i=Math.max(t,n)):(t=void 0,n=e=+this.getRightValue(e),r=e,i=e),{min:r,max:i,start:t,end:n}},_getScaleLabel:function(e){var t=this._parseValue(e);return void 0!==t.start?"["+t.start+", "+t.end+"]":+this.getRightValue(e)},getLabelForIndex:V.noop,getPixelForValue:V.noop,getValueForPixel:V.noop,getPixelForTick:function(e){var t=this.options.offset,n=this._ticks.length,r=1/Math.max(n-(t?0:1),1);return e<0||e>n-1?null:this.getPixelForDecimal(e*r+(t?r/2:0))},getPixelForDecimal:function(e){return this._reversePixels&&(e=1-e),this._startPixel+e*this._length},getDecimalForPixel:function(e){var t=(e-this._startPixel)/this._length;return this._reversePixels?1-t:t},getBasePixel:function(){return this.getPixelForValue(this.getBaseValue())},getBaseValue:function(){var e=this.min,t=this.max;return this.beginAtZero?0:e<0&&t<0?t:e>0&&t>0?e:0},_autoSkip:function(e){var t,n,r,i,a=this.options.ticks,o=this._length,l=a.maxTicksLimit||o/this._tickSize()+1,s=a.major.enabled?function(e){var t,n,r=[];for(t=0,n=e.length;t<n;t++)e[t].major&&r.push(t);return r}(e):[],u=s.length,c=s[0],d=s[u-1];if(u>l)return function(e,t,n){var r,i,a=0,o=t[0];for(n=Math.ceil(n),r=0;r<e.length;r++)i=e[r],r===o?(i._index=r,o=t[++a*n]):delete i.label}(e,s,u/l),hn(e);if(r=function(e,t,n,r){var i,a,o,l,s=function(e){var t,n,r=e.length;if(r<2)return!1;for(n=e[0],t=1;t<r;++t)if(e[t]-e[t-1]!==n)return!1;return n}(e),u=(t.length-1)/r;if(!s)return Math.max(u,1);for(o=0,l=(i=V.math._factorize(s)).length-1;o<l;o++)if((a=i[o])>u)return a;return Math.max(u,1)}(s,e,0,l),u>0){for(t=0,n=u-1;t<n;t++)pn(e,r,s[t],s[t+1]);return i=u>1?(d-c)/(u-1):null,pn(e,r,V.isNullOrUndef(i)?0:c-i,c),pn(e,r,d,V.isNullOrUndef(i)?e.length:d+i),hn(e)}return pn(e,r),hn(e)},_tickSize:function(){var e=this.options.ticks,t=V.toRadians(this.labelRotation),n=Math.abs(Math.cos(t)),r=Math.abs(Math.sin(t)),i=this._getLabelSizes(),a=e.autoSkipPadding||0,o=i?i.widest.width+a:0,l=i?i.highest.height+a:0;return this.isHorizontal()?l*n>o*r?o/n:l/r:l*r<o*n?l/n:o/r},_isVisible:function(){var e,t,n,r=this.chart,i=this.options.display;if("auto"!==i)return!!i;for(e=0,t=r.data.datasets.length;e<t;++e)if(r.isDatasetVisible(e)&&((n=r.getDatasetMeta(e)).xAxisID===this.id||n.yAxisID===this.id))return!0;return!1},_computeGridLineItems:function(e){var t,n,r,i,a,o,l,s,u,c,d,f,h,p,g,m,v,y=this,b=y.chart,x=y.options,_=x.gridLines,w=x.position,k=_.offsetGridLines,S=y.isHorizontal(),T=y._ticksToDraw,M=T.length+(k?1:0),C=un(_),P=[],D=_.drawBorder?on(_.lineWidth,0,0):0,E=D/2,O=V._alignPixel,N=function(e){return O(b,e,D)};for("top"===w?(t=N(y.bottom),l=y.bottom-C,u=t-E,d=N(e.top)+E,h=e.bottom):"bottom"===w?(t=N(y.top),d=e.top,h=N(e.bottom)-E,l=t+E,u=y.top+C):"left"===w?(t=N(y.right),o=y.right-C,s=t-E,c=N(e.left)+E,f=e.right):(t=N(y.left),c=e.left,f=N(e.right)-E,o=t+E,s=y.left+C),n=0;n<M;++n)r=T[n]||{},rn(r.label)&&n<T.length||(n===y.zeroLineIndex&&x.offset===k?(p=_.zeroLineWidth,g=_.zeroLineColor,m=_.zeroLineBorderDash||[],v=_.zeroLineBorderDashOffset||0):(p=on(_.lineWidth,n,1),g=on(_.color,n,"rgba(0,0,0,0.1)"),m=_.borderDash||[],v=_.borderDashOffset||0),void 0!==(i=ln(y,r._index||n,k))&&(a=O(b,i,p),S?o=s=c=f=a:l=u=d=h=a,P.push({tx1:o,ty1:l,tx2:s,ty2:u,x1:c,y1:d,x2:f,y2:h,width:p,color:g,borderDash:m,borderDashOffset:v})));return P.ticksLength=M,P.borderValue=t,P},_computeLabelItems:function(){var e,t,n,r,i,a,o,l,s,u,c,d,f=this,h=f.options,p=h.ticks,g=h.position,m=p.mirror,v=f.isHorizontal(),y=f._ticksToDraw,b=fn(p),x=p.padding,_=un(h.gridLines),w=-V.toRadians(f.labelRotation),k=[];for("top"===g?(a=f.bottom-_-x,o=w?"left":"center"):"bottom"===g?(a=f.top+_+x,o=w?"right":"center"):"left"===g?(i=f.right-(m?0:_)-x,o=m?"left":"right"):(i=f.left+(m?0:_)+x,o=m?"right":"left"),e=0,t=y.length;e<t;++e)r=(n=y[e]).label,rn(r)||(l=f.getPixelForTick(n._index||e)+p.labelOffset,u=(s=n.major?b.major:b.minor).lineHeight,c=nn(r)?r.length:1,v?(i=l,d="top"===g?((w?1:.5)-c)*u:(w?0:.5)*u):(a=l,d=(1-c)*u/2),k.push({x:i,y:a,rotation:w,label:r,font:s,textOffset:d,textAlign:o}));return k},_drawGrid:function(e){var t=this,n=t.options.gridLines;if(n.display){var r,i,a,o,l,s=t.ctx,u=t.chart,c=V._alignPixel,d=n.drawBorder?on(n.lineWidth,0,0):0,f=t._gridLineItems||(t._gridLineItems=t._computeGridLineItems(e));for(a=0,o=f.length;a<o;++a)r=(l=f[a]).width,i=l.color,r&&i&&(s.save(),s.lineWidth=r,s.strokeStyle=i,s.setLineDash&&(s.setLineDash(l.borderDash),s.lineDashOffset=l.borderDashOffset),s.beginPath(),n.drawTicks&&(s.moveTo(l.tx1,l.ty1),s.lineTo(l.tx2,l.ty2)),n.drawOnChartArea&&(s.moveTo(l.x1,l.y1),s.lineTo(l.x2,l.y2)),s.stroke(),s.restore());if(d){var h,p,g,m,v=d,y=on(n.lineWidth,f.ticksLength-1,1),b=f.borderValue;t.isHorizontal()?(h=c(u,t.left,v)-v/2,p=c(u,t.right,y)+y/2,g=m=b):(g=c(u,t.top,v)-v/2,m=c(u,t.bottom,y)+y/2,h=p=b),s.lineWidth=d,s.strokeStyle=on(n.color,0),s.beginPath(),s.moveTo(h,g),s.lineTo(p,m),s.stroke()}}},_drawLabels:function(){var e=this;if(e.options.ticks.display){var t,n,r,i,a,o,l,s,u=e.ctx,c=e._labelItems||(e._labelItems=e._computeLabelItems());for(t=0,r=c.length;t<r;++t){if(o=(a=c[t]).font,u.save(),u.translate(a.x,a.y),u.rotate(a.rotation),u.font=o.string,u.fillStyle=o.color,u.textBaseline="middle",u.textAlign=a.textAlign,l=a.label,s=a.textOffset,nn(l))for(n=0,i=l.length;n<i;++n)u.fillText(""+l[n],0,s),s+=o.lineHeight;else u.fillText(l,0,s);u.restore()}}},_drawTitle:function(){var e=this,t=e.ctx,n=e.options,r=n.scaleLabel;if(r.display){var i,a,o=an(r.fontColor,R.global.defaultFontColor),l=V.options._parseFont(r),s=V.options.toPadding(r.padding),u=l.lineHeight/2,c=n.position,d=0;if(e.isHorizontal())i=e.left+e.width/2,a="bottom"===c?e.bottom-u-s.bottom:e.top+u+s.top;else{var f="left"===c;i=f?e.left+u+s.top:e.right-u-s.top,a=e.top+e.height/2,d=f?-.5*Math.PI:.5*Math.PI}t.save(),t.translate(i,a),t.rotate(d),t.textAlign="center",t.textBaseline="middle",t.fillStyle=o,t.font=l.string,t.fillText(r.labelString,0,0),t.restore()}},draw:function(e){this._isVisible()&&(this._drawGrid(e),this._drawTitle(),this._drawLabels())},_layers:function(){var e=this,t=e.options,n=t.ticks&&t.ticks.z||0,r=t.gridLines&&t.gridLines.z||0;return e._isVisible()&&n!==r&&e.draw===e._draw?[{z:r,draw:function(){e._drawGrid.apply(e,arguments),e._drawTitle.apply(e,arguments)}},{z:n,draw:function(){e._drawLabels.apply(e,arguments)}}]:[{z:n,draw:function(){e.draw.apply(e,arguments)}}]},_getMatchingVisibleMetas:function(e){var t=this,n=t.isHorizontal();return t.chart._getSortedVisibleDatasetMetas().filter((function(r){return(!e||r.type===e)&&(n?r.xAxisID===t.id:r.yAxisID===t.id)}))}});gn.prototype._draw=gn.prototype.draw;var mn=gn,vn=V.isNullOrUndef,yn=mn.extend({determineDataLimits:function(){var e,t=this,n=t._getLabels(),r=t.options.ticks,i=r.min,a=r.max,o=0,l=n.length-1;void 0!==i&&(e=n.indexOf(i))>=0&&(o=e),void 0!==a&&(e=n.indexOf(a))>=0&&(l=e),t.minIndex=o,t.maxIndex=l,t.min=n[o],t.max=n[l]},buildTicks:function(){var e=this._getLabels(),t=this.minIndex,n=this.maxIndex;this.ticks=0===t&&n===e.length-1?e:e.slice(t,n+1)},getLabelForIndex:function(e,t){var n=this.chart;return n.getDatasetMeta(t).controller._getValueScaleId()===this.id?this.getRightValue(n.data.datasets[t].data[e]):this._getLabels()[e]},_configure:function(){var e=this,t=e.options.offset,n=e.ticks;mn.prototype._configure.call(e),e.isHorizontal()||(e._reversePixels=!e._reversePixels),n&&(e._startValue=e.minIndex-(t?.5:0),e._valueRange=Math.max(n.length-(t?0:1),1))},getPixelForValue:function(e,t,n){var r,i,a,o=this;return vn(t)||vn(n)||(e=o.chart.data.datasets[n].data[t]),vn(e)||(r=o.isHorizontal()?e.x:e.y),(void 0!==r||void 0!==e&&isNaN(t))&&(i=o._getLabels(),e=V.valueOrDefault(r,e),t=-1!==(a=i.indexOf(e))?a:t,isNaN(t)&&(t=e)),o.getPixelForDecimal((t-o._startValue)/o._valueRange)},getPixelForTick:function(e){var t=this.ticks;return e<0||e>t.length-1?null:this.getPixelForValue(t[e],e+this.minIndex)},getValueForPixel:function(e){var t=Math.round(this._startValue+this.getDecimalForPixel(e)*this._valueRange);return Math.min(Math.max(t,0),this.ticks.length-1)},getBasePixel:function(){return this.bottom}}),bn={position:"bottom"};yn._defaults=bn;var xn=V.noop,_n=V.isNullOrUndef,wn=mn.extend({getRightValue:function(e){return"string"===typeof e?+e:mn.prototype.getRightValue.call(this,e)},handleTickRangeOptions:function(){var e=this,t=e.options.ticks;if(t.beginAtZero){var n=V.sign(e.min),r=V.sign(e.max);n<0&&r<0?e.max=0:n>0&&r>0&&(e.min=0)}var i=void 0!==t.min||void 0!==t.suggestedMin,a=void 0!==t.max||void 0!==t.suggestedMax;void 0!==t.min?e.min=t.min:void 0!==t.suggestedMin&&(null===e.min?e.min=t.suggestedMin:e.min=Math.min(e.min,t.suggestedMin)),void 0!==t.max?e.max=t.max:void 0!==t.suggestedMax&&(null===e.max?e.max=t.suggestedMax:e.max=Math.max(e.max,t.suggestedMax)),i!==a&&e.min>=e.max&&(i?e.max=e.min+1:e.min=e.max-1),e.min===e.max&&(e.max++,t.beginAtZero||e.min--)},getTickLimit:function(){var e,t=this.options.ticks,n=t.stepSize,r=t.maxTicksLimit;return n?e=Math.ceil(this.max/n)-Math.floor(this.min/n)+1:(e=this._computeTickLimit(),r=r||11),r&&(e=Math.min(r,e)),e},_computeTickLimit:function(){return Number.POSITIVE_INFINITY},handleDirectionalChanges:xn,buildTicks:function(){var e=this,t=e.options.ticks,n=e.getTickLimit(),r={maxTicks:n=Math.max(2,n),min:t.min,max:t.max,precision:t.precision,stepSize:V.valueOrDefault(t.fixedStepSize,t.stepSize)},i=e.ticks=function(e,t){var n,r,i,a,o=[],l=e.stepSize,s=l||1,u=e.maxTicks-1,c=e.min,d=e.max,f=e.precision,h=t.min,p=t.max,g=V.niceNum((p-h)/u/s)*s;if(g<1e-14&&_n(c)&&_n(d))return[h,p];(a=Math.ceil(p/g)-Math.floor(h/g))>u&&(g=V.niceNum(a*g/u/s)*s),l||_n(f)?n=Math.pow(10,V._decimalPlaces(g)):(n=Math.pow(10,f),g=Math.ceil(g*n)/n),r=Math.floor(h/g)*g,i=Math.ceil(p/g)*g,l&&(!_n(c)&&V.almostWhole(c/g,g/1e3)&&(r=c),!_n(d)&&V.almostWhole(d/g,g/1e3)&&(i=d)),a=(i-r)/g,a=V.almostEquals(a,Math.round(a),g/1e3)?Math.round(a):Math.ceil(a),r=Math.round(r*n)/n,i=Math.round(i*n)/n,o.push(_n(c)?r:c);for(var m=1;m<a;++m)o.push(Math.round((r+m*g)*n)/n);return o.push(_n(d)?i:d),o}(r,e);e.handleDirectionalChanges(),e.max=V.max(i),e.min=V.min(i),t.reverse?(i.reverse(),e.start=e.max,e.end=e.min):(e.start=e.min,e.end=e.max)},convertTicksToLabels:function(){var e=this;e.ticksAsNumbers=e.ticks.slice(),e.zeroLineIndex=e.ticks.indexOf(0),mn.prototype.convertTicksToLabels.call(e)},_configure:function(){var e,t=this,n=t.getTicks(),r=t.min,i=t.max;mn.prototype._configure.call(t),t.options.offset&&n.length&&(r-=e=(i-r)/Math.max(n.length-1,1)/2,i+=e),t._startValue=r,t._endValue=i,t._valueRange=i-r}}),kn={position:"left",ticks:{callback:tn.formatters.linear}};function Sn(e,t,n,r){var i,a,o=e.options,l=function(e,t,n){var r=[n.type,void 0===t&&void 0===n.stack?n.index:"",n.stack].join(".");return void 0===e[r]&&(e[r]={pos:[],neg:[]}),e[r]}(t,o.stacked,n),s=l.pos,u=l.neg,c=r.length;for(i=0;i<c;++i)a=e._parseValue(r[i]),isNaN(a.min)||isNaN(a.max)||n.data[i].hidden||(s[i]=s[i]||0,u[i]=u[i]||0,o.relativePoints?s[i]=100:a.min<0||a.max<0?u[i]+=a.min:s[i]+=a.max)}function Tn(e,t,n){var r,i,a=n.length;for(r=0;r<a;++r)i=e._parseValue(n[r]),isNaN(i.min)||isNaN(i.max)||t.data[r].hidden||(e.min=Math.min(e.min,i.min),e.max=Math.max(e.max,i.max))}var Mn=wn.extend({determineDataLimits:function(){var e,t,n,r,i=this,a=i.options,o=i.chart.data.datasets,l=i._getMatchingVisibleMetas(),s=a.stacked,u={},c=l.length;if(i.min=Number.POSITIVE_INFINITY,i.max=Number.NEGATIVE_INFINITY,void 0===s)for(e=0;!s&&e<c;++e)s=void 0!==(t=l[e]).stack;for(e=0;e<c;++e)n=o[(t=l[e]).index].data,s?Sn(i,u,t,n):Tn(i,t,n);V.each(u,(function(e){r=e.pos.concat(e.neg),i.min=Math.min(i.min,V.min(r)),i.max=Math.max(i.max,V.max(r))})),i.min=V.isFinite(i.min)&&!isNaN(i.min)?i.min:0,i.max=V.isFinite(i.max)&&!isNaN(i.max)?i.max:1,i.handleTickRangeOptions()},_computeTickLimit:function(){var e;return this.isHorizontal()?Math.ceil(this.width/40):(e=V.options._parseFont(this.options.ticks),Math.ceil(this.height/e.lineHeight))},handleDirectionalChanges:function(){this.isHorizontal()||this.ticks.reverse()},getLabelForIndex:function(e,t){return this._getScaleLabel(this.chart.data.datasets[t].data[e])},getPixelForValue:function(e){return this.getPixelForDecimal((+this.getRightValue(e)-this._startValue)/this._valueRange)},getValueForPixel:function(e){return this._startValue+this.getDecimalForPixel(e)*this._valueRange},getPixelForTick:function(e){var t=this.ticksAsNumbers;return e<0||e>t.length-1?null:this.getPixelForValue(t[e])}}),Cn=kn;Mn._defaults=Cn;var Pn=V.valueOrDefault,Dn=V.math.log10,En={position:"left",ticks:{callback:tn.formatters.logarithmic}};function On(e,t){return V.isFinite(e)&&e>=0?e:t}var Nn=mn.extend({determineDataLimits:function(){var e,t,n,r,i,a,o=this,l=o.options,s=o.chart,u=s.data.datasets,c=o.isHorizontal();function d(e){return c?e.xAxisID===o.id:e.yAxisID===o.id}o.min=Number.POSITIVE_INFINITY,o.max=Number.NEGATIVE_INFINITY,o.minNotZero=Number.POSITIVE_INFINITY;var f=l.stacked;if(void 0===f)for(e=0;e<u.length;e++)if(t=s.getDatasetMeta(e),s.isDatasetVisible(e)&&d(t)&&void 0!==t.stack){f=!0;break}if(l.stacked||f){var h={};for(e=0;e<u.length;e++){var p=[(t=s.getDatasetMeta(e)).type,void 0===l.stacked&&void 0===t.stack?e:"",t.stack].join(".");if(s.isDatasetVisible(e)&&d(t))for(void 0===h[p]&&(h[p]=[]),i=0,a=(r=u[e].data).length;i<a;i++){var g=h[p];n=o._parseValue(r[i]),isNaN(n.min)||isNaN(n.max)||t.data[i].hidden||n.min<0||n.max<0||(g[i]=g[i]||0,g[i]+=n.max)}}V.each(h,(function(e){if(e.length>0){var t=V.min(e),n=V.max(e);o.min=Math.min(o.min,t),o.max=Math.max(o.max,n)}}))}else for(e=0;e<u.length;e++)if(t=s.getDatasetMeta(e),s.isDatasetVisible(e)&&d(t))for(i=0,a=(r=u[e].data).length;i<a;i++)n=o._parseValue(r[i]),isNaN(n.min)||isNaN(n.max)||t.data[i].hidden||n.min<0||n.max<0||(o.min=Math.min(n.min,o.min),o.max=Math.max(n.max,o.max),0!==n.min&&(o.minNotZero=Math.min(n.min,o.minNotZero)));o.min=V.isFinite(o.min)?o.min:null,o.max=V.isFinite(o.max)?o.max:null,o.minNotZero=V.isFinite(o.minNotZero)?o.minNotZero:null,this.handleTickRangeOptions()},handleTickRangeOptions:function(){var e=this,t=e.options.ticks;e.min=On(t.min,e.min),e.max=On(t.max,e.max),e.min===e.max&&(0!==e.min&&null!==e.min?(e.min=Math.pow(10,Math.floor(Dn(e.min))-1),e.max=Math.pow(10,Math.floor(Dn(e.max))+1)):(e.min=1,e.max=10)),null===e.min&&(e.min=Math.pow(10,Math.floor(Dn(e.max))-1)),null===e.max&&(e.max=0!==e.min?Math.pow(10,Math.floor(Dn(e.min))+1):10),null===e.minNotZero&&(e.min>0?e.minNotZero=e.min:e.max<1?e.minNotZero=Math.pow(10,Math.floor(Dn(e.max))):e.minNotZero=1)},buildTicks:function(){var e=this,t=e.options.ticks,n=!e.isHorizontal(),r={min:On(t.min),max:On(t.max)},i=e.ticks=function(e,t){var n,r,i=[],a=Pn(e.min,Math.pow(10,Math.floor(Dn(t.min)))),o=Math.floor(Dn(t.max)),l=Math.ceil(t.max/Math.pow(10,o));0===a?(n=Math.floor(Dn(t.minNotZero)),r=Math.floor(t.minNotZero/Math.pow(10,n)),i.push(a),a=r*Math.pow(10,n)):(n=Math.floor(Dn(a)),r=Math.floor(a/Math.pow(10,n)));var s=n<0?Math.pow(10,Math.abs(n)):1;do{i.push(a),10===++r&&(r=1,s=++n>=0?1:s),a=Math.round(r*Math.pow(10,n)*s)/s}while(n<o||n===o&&r<l);var u=Pn(e.max,a);return i.push(u),i}(r,e);e.max=V.max(i),e.min=V.min(i),t.reverse?(n=!n,e.start=e.max,e.end=e.min):(e.start=e.min,e.end=e.max),n&&i.reverse()},convertTicksToLabels:function(){this.tickValues=this.ticks.slice(),mn.prototype.convertTicksToLabels.call(this)},getLabelForIndex:function(e,t){return this._getScaleLabel(this.chart.data.datasets[t].data[e])},getPixelForTick:function(e){var t=this.tickValues;return e<0||e>t.length-1?null:this.getPixelForValue(t[e])},_getFirstTickValue:function(e){var t=Math.floor(Dn(e));return Math.floor(e/Math.pow(10,t))*Math.pow(10,t)},_configure:function(){var e=this,t=e.min,n=0;mn.prototype._configure.call(e),0===t&&(t=e._getFirstTickValue(e.minNotZero),n=Pn(e.options.ticks.fontSize,R.global.defaultFontSize)/e._length),e._startValue=Dn(t),e._valueOffset=n,e._valueRange=(Dn(e.max)-Dn(t))/(1-n)},getPixelForValue:function(e){var t=this,n=0;return(e=+t.getRightValue(e))>t.min&&e>0&&(n=(Dn(e)-t._startValue)/t._valueRange+t._valueOffset),t.getPixelForDecimal(n)},getValueForPixel:function(e){var t=this,n=t.getDecimalForPixel(e);return 0===n&&0===t.min?0:Math.pow(10,t._startValue+(n-t._valueOffset)*t._valueRange)}}),In=En;Nn._defaults=In;var An=V.valueOrDefault,Fn=V.valueAtIndexOrDefault,Rn=V.options.resolve,Ln={display:!0,animate:!0,position:"chartArea",angleLines:{display:!0,color:"rgba(0,0,0,0.1)",lineWidth:1,borderDash:[],borderDashOffset:0},gridLines:{circular:!1},ticks:{showLabelBackdrop:!0,backdropColor:"rgba(255,255,255,0.75)",backdropPaddingY:2,backdropPaddingX:2,callback:tn.formatters.linear},pointLabels:{display:!0,fontSize:10,callback:function(e){return e}}};function zn(e){var t=e.ticks;return t.display&&e.display?An(t.fontSize,R.global.defaultFontSize)+2*t.backdropPaddingY:0}function jn(e,t,n,r,i){return e===r||e===i?{start:t-n/2,end:t+n/2}:e<r||e>i?{start:t-n,end:t}:{start:t,end:t+n}}function Wn(e){return 0===e||180===e?"center":e<180?"left":"right"}function Vn(e,t,n,r){var i,a,o=n.y+r/2;if(V.isArray(t))for(i=0,a=t.length;i<a;++i)e.fillText(t[i],n.x,o),o+=r;else e.fillText(t,n.x,o)}function Yn(e,t,n){90===e||270===e?n.y-=t.h/2:(e>270||e<90)&&(n.y-=t.h)}function Bn(e){return V.isNumber(e)?e:0}var Hn=wn.extend({setDimensions:function(){var e=this;e.width=e.maxWidth,e.height=e.maxHeight,e.paddingTop=zn(e.options)/2,e.xCenter=Math.floor(e.width/2),e.yCenter=Math.floor((e.height-e.paddingTop)/2),e.drawingArea=Math.min(e.height-e.paddingTop,e.width)/2},determineDataLimits:function(){var e=this,t=e.chart,n=Number.POSITIVE_INFINITY,r=Number.NEGATIVE_INFINITY;V.each(t.data.datasets,(function(i,a){if(t.isDatasetVisible(a)){var o=t.getDatasetMeta(a);V.each(i.data,(function(t,i){var a=+e.getRightValue(t);isNaN(a)||o.data[i].hidden||(n=Math.min(a,n),r=Math.max(a,r))}))}})),e.min=n===Number.POSITIVE_INFINITY?0:n,e.max=r===Number.NEGATIVE_INFINITY?0:r,e.handleTickRangeOptions()},_computeTickLimit:function(){return Math.ceil(this.drawingArea/zn(this.options))},convertTicksToLabels:function(){var e=this;wn.prototype.convertTicksToLabels.call(e),e.pointLabels=e.chart.data.labels.map((function(){var t=V.callback(e.options.pointLabels.callback,arguments,e);return t||0===t?t:""}))},getLabelForIndex:function(e,t){return+this.getRightValue(this.chart.data.datasets[t].data[e])},fit:function(){var e=this.options;e.display&&e.pointLabels.display?function(e){var t,n,r,i=V.options._parseFont(e.options.pointLabels),a={l:0,r:e.width,t:0,b:e.height-e.paddingTop},o={};e.ctx.font=i.string,e._pointLabelSizes=[];var l,s,u,c=e.chart.data.labels.length;for(t=0;t<c;t++){r=e.getPointPosition(t,e.drawingArea+5),l=e.ctx,s=i.lineHeight,u=e.pointLabels[t],n=V.isArray(u)?{w:V.longestText(l,l.font,u),h:u.length*s}:{w:l.measureText(u).width,h:s},e._pointLabelSizes[t]=n;var d=e.getIndexAngle(t),f=V.toDegrees(d)%360,h=jn(f,r.x,n.w,0,180),p=jn(f,r.y,n.h,90,270);h.start<a.l&&(a.l=h.start,o.l=d),h.end>a.r&&(a.r=h.end,o.r=d),p.start<a.t&&(a.t=p.start,o.t=d),p.end>a.b&&(a.b=p.end,o.b=d)}e.setReductions(e.drawingArea,a,o)}(this):this.setCenterPoint(0,0,0,0)},setReductions:function(e,t,n){var r=this,i=t.l/Math.sin(n.l),a=Math.max(t.r-r.width,0)/Math.sin(n.r),o=-t.t/Math.cos(n.t),l=-Math.max(t.b-(r.height-r.paddingTop),0)/Math.cos(n.b);i=Bn(i),a=Bn(a),o=Bn(o),l=Bn(l),r.drawingArea=Math.min(Math.floor(e-(i+a)/2),Math.floor(e-(o+l)/2)),r.setCenterPoint(i,a,o,l)},setCenterPoint:function(e,t,n,r){var i=this,a=i.width-t-i.drawingArea,o=e+i.drawingArea,l=n+i.drawingArea,s=i.height-i.paddingTop-r-i.drawingArea;i.xCenter=Math.floor((o+a)/2+i.left),i.yCenter=Math.floor((l+s)/2+i.top+i.paddingTop)},getIndexAngle:function(e){var t=this.chart,n=(e*(360/t.data.labels.length)+((t.options||{}).startAngle||0))%360;return(n<0?n+360:n)*Math.PI*2/360},getDistanceFromCenterForValue:function(e){var t=this;if(V.isNullOrUndef(e))return NaN;var n=t.drawingArea/(t.max-t.min);return t.options.ticks.reverse?(t.max-e)*n:(e-t.min)*n},getPointPosition:function(e,t){var n=this.getIndexAngle(e)-Math.PI/2;return{x:Math.cos(n)*t+this.xCenter,y:Math.sin(n)*t+this.yCenter}},getPointPositionForValue:function(e,t){return this.getPointPosition(e,this.getDistanceFromCenterForValue(t))},getBasePosition:function(e){var t=this.min,n=this.max;return this.getPointPositionForValue(e||0,this.beginAtZero?0:t<0&&n<0?n:t>0&&n>0?t:0)},_drawGrid:function(){var e,t,n,r=this,i=r.ctx,a=r.options,o=a.gridLines,l=a.angleLines,s=An(l.lineWidth,o.lineWidth),u=An(l.color,o.color);if(a.pointLabels.display&&function(e){var t=e.ctx,n=e.options,r=n.pointLabels,i=zn(n),a=e.getDistanceFromCenterForValue(n.ticks.reverse?e.min:e.max),o=V.options._parseFont(r);t.save(),t.font=o.string,t.textBaseline="middle";for(var l=e.chart.data.labels.length-1;l>=0;l--){var s=0===l?i/2:0,u=e.getPointPosition(l,a+s+5),c=Fn(r.fontColor,l,R.global.defaultFontColor);t.fillStyle=c;var d=e.getIndexAngle(l),f=V.toDegrees(d);t.textAlign=Wn(f),Yn(f,e._pointLabelSizes[l],u),Vn(t,e.pointLabels[l],u,o.lineHeight)}t.restore()}(r),o.display&&V.each(r.ticks,(function(e,n){0!==n&&(t=r.getDistanceFromCenterForValue(r.ticksAsNumbers[n]),function(e,t,n,r){var i,a=e.ctx,o=t.circular,l=e.chart.data.labels.length,s=Fn(t.color,r-1),u=Fn(t.lineWidth,r-1);if((o||l)&&s&&u){if(a.save(),a.strokeStyle=s,a.lineWidth=u,a.setLineDash&&(a.setLineDash(t.borderDash||[]),a.lineDashOffset=t.borderDashOffset||0),a.beginPath(),o)a.arc(e.xCenter,e.yCenter,n,0,2*Math.PI);else{i=e.getPointPosition(0,n),a.moveTo(i.x,i.y);for(var c=1;c<l;c++)i=e.getPointPosition(c,n),a.lineTo(i.x,i.y)}a.closePath(),a.stroke(),a.restore()}}(r,o,t,n))})),l.display&&s&&u){for(i.save(),i.lineWidth=s,i.strokeStyle=u,i.setLineDash&&(i.setLineDash(Rn([l.borderDash,o.borderDash,[]])),i.lineDashOffset=Rn([l.borderDashOffset,o.borderDashOffset,0])),e=r.chart.data.labels.length-1;e>=0;e--)t=r.getDistanceFromCenterForValue(a.ticks.reverse?r.min:r.max),n=r.getPointPosition(e,t),i.beginPath(),i.moveTo(r.xCenter,r.yCenter),i.lineTo(n.x,n.y),i.stroke();i.restore()}},_drawLabels:function(){var e=this,t=e.ctx,n=e.options.ticks;if(n.display){var r,i,a=e.getIndexAngle(0),o=V.options._parseFont(n),l=An(n.fontColor,R.global.defaultFontColor);t.save(),t.font=o.string,t.translate(e.xCenter,e.yCenter),t.rotate(a),t.textAlign="center",t.textBaseline="middle",V.each(e.ticks,(function(a,s){(0!==s||n.reverse)&&(r=e.getDistanceFromCenterForValue(e.ticksAsNumbers[s]),n.showLabelBackdrop&&(i=t.measureText(a).width,t.fillStyle=n.backdropColor,t.fillRect(-i/2-n.backdropPaddingX,-r-o.size/2-n.backdropPaddingY,i+2*n.backdropPaddingX,o.size+2*n.backdropPaddingY)),t.fillStyle=l,t.fillText(a,0,-r))})),t.restore()}},_drawTitle:V.noop}),Un=Ln;Hn._defaults=Un;var qn=V._deprecated,$n=V.options.resolve,Gn=V.valueOrDefault,Qn=Number.MIN_SAFE_INTEGER||-9007199254740991,Kn=Number.MAX_SAFE_INTEGER||9007199254740991,Zn={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},Xn=Object.keys(Zn);function Jn(e,t){return e-t}function er(e){return V.valueOrDefault(e.time.min,e.ticks.min)}function tr(e){return V.valueOrDefault(e.time.max,e.ticks.max)}function nr(e,t,n,r){var i=function(e,t,n){for(var r,i,a,o=0,l=e.length-1;o>=0&&o<=l;){if(i=e[(r=o+l>>1)-1]||null,a=e[r],!i)return{lo:null,hi:a};if(a[t]<n)o=r+1;else{if(!(i[t]>n))return{lo:i,hi:a};l=r-1}}return{lo:a,hi:null}}(e,t,n),a=i.lo?i.hi?i.lo:e[e.length-2]:e[0],o=i.lo?i.hi?i.hi:e[e.length-1]:e[1],l=o[t]-a[t],s=l?(n-a[t])/l:0,u=(o[r]-a[r])*s;return a[r]+u}function rr(e,t){var n=e._adapter,r=e.options.time,i=r.parser,a=i||r.format,o=t;return"function"===typeof i&&(o=i(o)),V.isFinite(o)||(o="string"===typeof a?n.parse(o,a):n.parse(o)),null!==o?+o:(i||"function"!==typeof a||(o=a(t),V.isFinite(o)||(o=n.parse(o))),o)}function ir(e,t){if(V.isNullOrUndef(t))return null;var n=e.options.time,r=rr(e,e.getRightValue(t));return null===r||n.round&&(r=+e._adapter.startOf(r,n.round)),r}function ar(e,t,n,r){var i,a,o,l=Xn.length;for(i=Xn.indexOf(e);i<l-1;++i)if(o=(a=Zn[Xn[i]]).steps?a.steps:Kn,a.common&&Math.ceil((n-t)/(o*a.size))<=r)return Xn[i];return Xn[l-1]}function or(e,t,n){var r,i,a=[],o={},l=t.length;for(r=0;r<l;++r)o[i=t[r]]=r,a.push({value:i,major:!1});return 0!==l&&n?function(e,t,n,r){var i,a,o=e._adapter,l=+o.startOf(t[0].value,r),s=t[t.length-1].value;for(i=l;i<=s;i=+o.add(i,1,r))(a=n[i])>=0&&(t[a].major=!0);return t}(e,a,o,n):a}var lr=mn.extend({initialize:function(){this.mergeTicksOptions(),mn.prototype.initialize.call(this)},update:function(){var e=this,t=e.options,n=t.time||(t.time={}),r=e._adapter=new en._date(t.adapters.date);return qn("time scale",n.format,"time.format","time.parser"),qn("time scale",n.min,"time.min","ticks.min"),qn("time scale",n.max,"time.max","ticks.max"),V.mergeIf(n.displayFormats,r.formats()),mn.prototype.update.apply(e,arguments)},getRightValue:function(e){return e&&void 0!==e.t&&(e=e.t),mn.prototype.getRightValue.call(this,e)},determineDataLimits:function(){var e,t,n,r,i,a,o,l=this,s=l.chart,u=l._adapter,c=l.options,d=c.time.unit||"day",f=Kn,h=Qn,p=[],g=[],m=[],v=l._getLabels();for(e=0,n=v.length;e<n;++e)m.push(ir(l,v[e]));for(e=0,n=(s.data.datasets||[]).length;e<n;++e)if(s.isDatasetVisible(e))if(i=s.data.datasets[e].data,V.isObject(i[0]))for(g[e]=[],t=0,r=i.length;t<r;++t)a=ir(l,i[t]),p.push(a),g[e][t]=a;else g[e]=m.slice(0),o||(p=p.concat(m),o=!0);else g[e]=[];m.length&&(f=Math.min(f,m[0]),h=Math.max(h,m[m.length-1])),p.length&&(p=n>1?function(e){var t,n,r,i={},a=[];for(t=0,n=e.length;t<n;++t)i[r=e[t]]||(i[r]=!0,a.push(r));return a}(p).sort(Jn):p.sort(Jn),f=Math.min(f,p[0]),h=Math.max(h,p[p.length-1])),f=ir(l,er(c))||f,h=ir(l,tr(c))||h,f=f===Kn?+u.startOf(Date.now(),d):f,h=h===Qn?+u.endOf(Date.now(),d)+1:h,l.min=Math.min(f,h),l.max=Math.max(f+1,h),l._table=[],l._timestamps={data:p,datasets:g,labels:m}},buildTicks:function(){var e,t,n,r=this,i=r.min,a=r.max,o=r.options,l=o.ticks,s=o.time,u=r._timestamps,c=[],d=r.getLabelCapacity(i),f=l.source,h=o.distribution;for(u="data"===f||"auto"===f&&"series"===h?u.data:"labels"===f?u.labels:function(e,t,n,r){var i,a=e._adapter,o=e.options,l=o.time,s=l.unit||ar(l.minUnit,t,n,r),u=$n([l.stepSize,l.unitStepSize,1]),c="week"===s&&l.isoWeekday,d=t,f=[];if(c&&(d=+a.startOf(d,"isoWeek",c)),d=+a.startOf(d,c?"day":s),a.diff(n,t,s)>1e5*u)throw t+" and "+n+" are too far apart with stepSize of "+u+" "+s;for(i=d;i<n;i=+a.add(i,u,s))f.push(i);return i!==n&&"ticks"!==o.bounds||f.push(i),f}(r,i,a,d),"ticks"===o.bounds&&u.length&&(i=u[0],a=u[u.length-1]),i=ir(r,er(o))||i,a=ir(r,tr(o))||a,e=0,t=u.length;e<t;++e)(n=u[e])>=i&&n<=a&&c.push(n);return r.min=i,r.max=a,r._unit=s.unit||(l.autoSkip?ar(s.minUnit,r.min,r.max,d):function(e,t,n,r,i){var a,o;for(a=Xn.length-1;a>=Xn.indexOf(n);a--)if(o=Xn[a],Zn[o].common&&e._adapter.diff(i,r,o)>=t-1)return o;return Xn[n?Xn.indexOf(n):0]}(r,c.length,s.minUnit,r.min,r.max)),r._majorUnit=l.major.enabled&&"year"!==r._unit?function(e){for(var t=Xn.indexOf(e)+1,n=Xn.length;t<n;++t)if(Zn[Xn[t]].common)return Xn[t]}(r._unit):void 0,r._table=function(e,t,n,r){if("linear"===r||!e.length)return[{time:t,pos:0},{time:n,pos:1}];var i,a,o,l,s,u=[],c=[t];for(i=0,a=e.length;i<a;++i)(l=e[i])>t&&l<n&&c.push(l);for(c.push(n),i=0,a=c.length;i<a;++i)s=c[i+1],o=c[i-1],l=c[i],void 0!==o&&void 0!==s&&Math.round((s+o)/2)===l||u.push({time:l,pos:i/(a-1)});return u}(r._timestamps.data,i,a,h),r._offsets=function(e,t,n,r,i){var a,o,l=0,s=0;return i.offset&&t.length&&(a=nr(e,"time",t[0],"pos"),l=1===t.length?1-a:(nr(e,"time",t[1],"pos")-a)/2,o=nr(e,"time",t[t.length-1],"pos"),s=1===t.length?o:(o-nr(e,"time",t[t.length-2],"pos"))/2),{start:l,end:s,factor:1/(l+1+s)}}(r._table,c,0,0,o),l.reverse&&c.reverse(),or(r,c,r._majorUnit)},getLabelForIndex:function(e,t){var n=this,r=n._adapter,i=n.chart.data,a=n.options.time,o=i.labels&&e<i.labels.length?i.labels[e]:"",l=i.datasets[t].data[e];return V.isObject(l)&&(o=n.getRightValue(l)),a.tooltipFormat?r.format(rr(n,o),a.tooltipFormat):"string"===typeof o?o:r.format(rr(n,o),a.displayFormats.datetime)},tickFormatFunction:function(e,t,n,r){var i=this._adapter,a=this.options,o=a.time.displayFormats,l=o[this._unit],s=this._majorUnit,u=o[s],c=n[t],d=a.ticks,f=s&&u&&c&&c.major,h=i.format(e,r||(f?u:l)),p=f?d.major:d.minor,g=$n([p.callback,p.userCallback,d.callback,d.userCallback]);return g?g(h,t,n):h},convertTicksToLabels:function(e){var t,n,r=[];for(t=0,n=e.length;t<n;++t)r.push(this.tickFormatFunction(e[t].value,t,e));return r},getPixelForOffset:function(e){var t=this._offsets,n=nr(this._table,"time",e,"pos");return this.getPixelForDecimal((t.start+n)*t.factor)},getPixelForValue:function(e,t,n){var r=null;if(void 0!==t&&void 0!==n&&(r=this._timestamps.datasets[n][t]),null===r&&(r=ir(this,e)),null!==r)return this.getPixelForOffset(r)},getPixelForTick:function(e){var t=this.getTicks();return e>=0&&e<t.length?this.getPixelForOffset(t[e].value):null},getValueForPixel:function(e){var t=this._offsets,n=this.getDecimalForPixel(e)/t.factor-t.end,r=nr(this._table,"pos",n,"time");return this._adapter._create(r)},_getLabelSize:function(e){var t=this.options.ticks,n=this.ctx.measureText(e).width,r=V.toRadians(this.isHorizontal()?t.maxRotation:t.minRotation),i=Math.cos(r),a=Math.sin(r),o=Gn(t.fontSize,R.global.defaultFontSize);return{w:n*i+o*a,h:n*a+o*i}},getLabelWidth:function(e){return this._getLabelSize(e).w},getLabelCapacity:function(e){var t=this,n=t.options.time,r=n.displayFormats,i=r[n.unit]||r.millisecond,a=t.tickFormatFunction(e,0,or(t,[e],t._majorUnit),i),o=t._getLabelSize(a),l=Math.floor(t.isHorizontal()?t.width/o.w:t.height/o.h);return t.options.offset&&l--,l>0?l:1}}),sr={position:"bottom",distribution:"linear",bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,displayFormat:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{autoSkip:!1,source:"auto",major:{enabled:!1}}};lr._defaults=sr;var ur={category:yn,linear:Mn,logarithmic:Nn,radialLinear:Hn,time:lr},cr={datetime:"MMM D, YYYY, h:mm:ss a",millisecond:"h:mm:ss.SSS a",second:"h:mm:ss a",minute:"h:mm a",hour:"hA",day:"MMM D",week:"ll",month:"MMM YYYY",quarter:"[Q]Q - YYYY",year:"YYYY"};en._date.override("function"===typeof e?{_id:"moment",formats:function(){return cr},parse:function(t,n){return"string"===typeof t&&"string"===typeof n?t=e(t,n):t instanceof e||(t=e(t)),t.isValid()?t.valueOf():null},format:function(t,n){return e(t).format(n)},add:function(t,n,r){return e(t).add(n,r).valueOf()},diff:function(t,n,r){return e(t).diff(e(n),r)},startOf:function(t,n,r){return t=e(t),"isoWeek"===n?t.isoWeekday(r).valueOf():t.startOf(n).valueOf()},endOf:function(t,n){return e(t).endOf(n).valueOf()},_create:function(t){return e(t)}}:{}),R._set("global",{plugins:{filler:{propagate:!0}}});var dr={dataset:function(e){var t=e.fill,n=e.chart,r=n.getDatasetMeta(t),i=r&&n.isDatasetVisible(t)&&r.dataset._children||[],a=i.length||0;return a?function(e,t){return t<a&&i[t]._view||null}:null},boundary:function(e){var t=e.boundary,n=t?t.x:null,r=t?t.y:null;return V.isArray(t)?function(e,n){return t[n]}:function(e){return{x:null===n?e.x:n,y:null===r?e.y:r}}}};function fr(e,t,n){var r,i=e._model||{},a=i.fill;if(void 0===a&&(a=!!i.backgroundColor),!1===a||null===a)return!1;if(!0===a)return"origin";if(r=parseFloat(a,10),isFinite(r)&&Math.floor(r)===r)return"-"!==a[0]&&"+"!==a[0]||(r=t+r),!(r===t||r<0||r>=n)&&r;switch(a){case"bottom":return"start";case"top":return"end";case"zero":return"origin";case"origin":case"start":case"end":return a;default:return!1}}function hr(e){return(e.el._scale||{}).getPointPositionForValue?function(e){var t,n,r,i,a,o=e.el._scale,l=o.options,s=o.chart.data.labels.length,u=e.fill,c=[];if(!s)return null;for(t=l.ticks.reverse?o.max:o.min,n=l.ticks.reverse?o.min:o.max,r=o.getPointPositionForValue(0,t),i=0;i<s;++i)a="start"===u||"end"===u?o.getPointPositionForValue(i,"start"===u?t:n):o.getBasePosition(i),l.gridLines.circular&&(a.cx=r.x,a.cy=r.y,a.angle=o.getIndexAngle(i)-Math.PI/2),c.push(a);return c}(e):function(e){var t,n=e.el._model||{},r=e.el._scale||{},i=e.fill,a=null;if(isFinite(i))return null;if("start"===i?a=void 0===n.scaleBottom?r.bottom:n.scaleBottom:"end"===i?a=void 0===n.scaleTop?r.top:n.scaleTop:void 0!==n.scaleZero?a=n.scaleZero:r.getBasePixel&&(a=r.getBasePixel()),void 0!==a&&null!==a){if(void 0!==a.x&&void 0!==a.y)return a;if(V.isFinite(a))return{x:(t=r.isHorizontal())?a:null,y:t?null:a}}return null}(e)}function pr(e,t,n){var r,i=e[t].fill,a=[t];if(!n)return i;for(;!1!==i&&-1===a.indexOf(i);){if(!isFinite(i))return i;if(!(r=e[i]))return!1;if(r.visible)return i;a.push(i),i=r.fill}return!1}function gr(e){var t=e.fill,n="dataset";return!1===t?null:(isFinite(t)||(n="boundary"),dr[n](e))}function mr(e){return e&&!e.skip}function vr(e,t,n,r,i){var a,o,l,s;if(r&&i){for(e.moveTo(t[0].x,t[0].y),a=1;a<r;++a)V.canvas.lineTo(e,t[a-1],t[a]);if(void 0===n[0].angle)for(e.lineTo(n[i-1].x,n[i-1].y),a=i-1;a>0;--a)V.canvas.lineTo(e,n[a],n[a-1],!0);else for(o=n[0].cx,l=n[0].cy,s=Math.sqrt(Math.pow(n[0].x-o,2)+Math.pow(n[0].y-l,2)),a=i-1;a>0;--a)e.arc(o,l,s,n[a].angle,n[a-1].angle,!0)}}function yr(e,t,n,r,i,a){var o,l,s,u,c,d,f,h,p=t.length,g=r.spanGaps,m=[],v=[],y=0,b=0;for(e.beginPath(),o=0,l=p;o<l;++o)c=n(u=t[s=o%p]._view,s,r),d=mr(u),f=mr(c),a&&void 0===h&&d&&(l=p+(h=o+1)),d&&f?(y=m.push(u),b=v.push(c)):y&&b&&(g?(d&&m.push(u),f&&v.push(c)):(vr(e,m,v,y,b),y=b=0,m=[],v=[]));vr(e,m,v,y,b),e.closePath(),e.fillStyle=i,e.fill()}var br={id:"filler",afterDatasetsUpdate:function(e,t){var n,r,i,a,o=(e.data.datasets||[]).length,l=t.propagate,s=[];for(r=0;r<o;++r)a=null,(i=(n=e.getDatasetMeta(r)).dataset)&&i._model&&i instanceof _e.Line&&(a={visible:e.isDatasetVisible(r),fill:fr(i,r,o),chart:e,el:i}),n.$filler=a,s.push(a);for(r=0;r<o;++r)(a=s[r])&&(a.fill=pr(s,r,l),a.boundary=hr(a),a.mapper=gr(a))},beforeDatasetsDraw:function(e){var t,n,r,i,a,o,l,s=e._getSortedVisibleDatasetMetas(),u=e.ctx;for(n=s.length-1;n>=0;--n)(t=s[n].$filler)&&t.visible&&(i=(r=t.el)._view,a=r._children||[],o=t.mapper,l=i.backgroundColor||R.global.defaultColor,o&&l&&a.length&&(V.canvas.clipArea(u,e.chartArea),yr(u,a,o,i,l,r._loop),V.canvas.unclipArea(u)))}},xr=V.rtl.getRtlAdapter,_r=V.noop,wr=V.valueOrDefault;function kr(e,t){return e.usePointStyle&&e.boxWidth>t?t:e.boxWidth}R._set("global",{legend:{display:!0,position:"top",align:"center",fullWidth:!0,reverse:!1,weight:1e3,onClick:function(e,t){var n=t.datasetIndex,r=this.chart,i=r.getDatasetMeta(n);i.hidden=null===i.hidden?!r.data.datasets[n].hidden:null,r.update()},onHover:null,onLeave:null,labels:{boxWidth:40,padding:10,generateLabels:function(e){var t=e.data.datasets,n=e.options.legend||{},r=n.labels&&n.labels.usePointStyle;return e._getSortedDatasetMetas().map((function(n){var i=n.controller.getStyle(r?0:void 0);return{text:t[n.index].label,fillStyle:i.backgroundColor,hidden:!e.isDatasetVisible(n.index),lineCap:i.borderCapStyle,lineDash:i.borderDash,lineDashOffset:i.borderDashOffset,lineJoin:i.borderJoinStyle,lineWidth:i.borderWidth,strokeStyle:i.borderColor,pointStyle:i.pointStyle,rotation:i.rotation,datasetIndex:n.index}}),this)}}},legendCallback:function(e){var t,n,r,i=document.createElement("ul"),a=e.data.datasets;for(i.setAttribute("class",e.id+"-legend"),t=0,n=a.length;t<n;t++)(r=i.appendChild(document.createElement("li"))).appendChild(document.createElement("span")).style.backgroundColor=a[t].backgroundColor,a[t].label&&r.appendChild(document.createTextNode(a[t].label));return i.outerHTML}});var Sr=G.extend({initialize:function(e){V.extend(this,e),this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1},beforeUpdate:_r,update:function(e,t,n){var r=this;return r.beforeUpdate(),r.maxWidth=e,r.maxHeight=t,r.margins=n,r.beforeSetDimensions(),r.setDimensions(),r.afterSetDimensions(),r.beforeBuildLabels(),r.buildLabels(),r.afterBuildLabels(),r.beforeFit(),r.fit(),r.afterFit(),r.afterUpdate(),r.minSize},afterUpdate:_r,beforeSetDimensions:_r,setDimensions:function(){var e=this;e.isHorizontal()?(e.width=e.maxWidth,e.left=0,e.right=e.width):(e.height=e.maxHeight,e.top=0,e.bottom=e.height),e.paddingLeft=0,e.paddingTop=0,e.paddingRight=0,e.paddingBottom=0,e.minSize={width:0,height:0}},afterSetDimensions:_r,beforeBuildLabels:_r,buildLabels:function(){var e=this,t=e.options.labels||{},n=V.callback(t.generateLabels,[e.chart],e)||[];t.filter&&(n=n.filter((function(n){return t.filter(n,e.chart.data)}))),e.options.reverse&&n.reverse(),e.legendItems=n},afterBuildLabels:_r,beforeFit:_r,fit:function(){var e=this,t=e.options,n=t.labels,r=t.display,i=e.ctx,a=V.options._parseFont(n),o=a.size,l=e.legendHitBoxes=[],s=e.minSize,u=e.isHorizontal();if(u?(s.width=e.maxWidth,s.height=r?10:0):(s.width=r?10:0,s.height=e.maxHeight),r){if(i.font=a.string,u){var c=e.lineWidths=[0],d=0;i.textAlign="left",i.textBaseline="middle",V.each(e.legendItems,(function(e,t){var r=kr(n,o)+o/2+i.measureText(e.text).width;(0===t||c[c.length-1]+r+2*n.padding>s.width)&&(d+=o+n.padding,c[c.length-(t>0?0:1)]=0),l[t]={left:0,top:0,width:r,height:o},c[c.length-1]+=r+n.padding})),s.height+=d}else{var f=n.padding,h=e.columnWidths=[],p=e.columnHeights=[],g=n.padding,m=0,v=0;V.each(e.legendItems,(function(e,t){var r=kr(n,o)+o/2+i.measureText(e.text).width;t>0&&v+o+2*f>s.height&&(g+=m+n.padding,h.push(m),p.push(v),m=0,v=0),m=Math.max(m,r),v+=o+f,l[t]={left:0,top:0,width:r,height:o}})),g+=m,h.push(m),p.push(v),s.width+=g}e.width=s.width,e.height=s.height}else e.width=s.width=e.height=s.height=0},afterFit:_r,isHorizontal:function(){return"top"===this.options.position||"bottom"===this.options.position},draw:function(){var e=this,t=e.options,n=t.labels,r=R.global,i=r.defaultColor,a=r.elements.line,o=e.height,l=e.columnHeights,s=e.width,u=e.lineWidths;if(t.display){var c,d=xr(t.rtl,e.left,e.minSize.width),f=e.ctx,h=wr(n.fontColor,r.defaultFontColor),p=V.options._parseFont(n),g=p.size;f.textAlign=d.textAlign("left"),f.textBaseline="middle",f.lineWidth=.5,f.strokeStyle=h,f.fillStyle=h,f.font=p.string;var m=kr(n,g),v=e.legendHitBoxes,y=function(e,r){switch(t.align){case"start":return n.padding;case"end":return e-r;default:return(e-r+n.padding)/2}},b=e.isHorizontal();c=b?{x:e.left+y(s,u[0]),y:e.top+n.padding,line:0}:{x:e.left+n.padding,y:e.top+y(o,l[0]),line:0},V.rtl.overrideTextDirection(e.ctx,t.textDirection);var x=g+n.padding;V.each(e.legendItems,(function(t,r){var h=f.measureText(t.text).width,p=m+g/2+h,_=c.x,w=c.y;d.setWidth(e.minSize.width),b?r>0&&_+p+n.padding>e.left+e.minSize.width&&(w=c.y+=x,c.line++,_=c.x=e.left+y(s,u[c.line])):r>0&&w+x>e.top+e.minSize.height&&(_=c.x=_+e.columnWidths[c.line]+n.padding,c.line++,w=c.y=e.top+y(o,l[c.line]));var k=d.x(_);!function(e,t,r){if(!(isNaN(m)||m<=0)){f.save();var o=wr(r.lineWidth,a.borderWidth);if(f.fillStyle=wr(r.fillStyle,i),f.lineCap=wr(r.lineCap,a.borderCapStyle),f.lineDashOffset=wr(r.lineDashOffset,a.borderDashOffset),f.lineJoin=wr(r.lineJoin,a.borderJoinStyle),f.lineWidth=o,f.strokeStyle=wr(r.strokeStyle,i),f.setLineDash&&f.setLineDash(wr(r.lineDash,a.borderDash)),n&&n.usePointStyle){var l=m*Math.SQRT2/2,s=d.xPlus(e,m/2),u=t+g/2;V.canvas.drawPoint(f,r.pointStyle,l,s,u,r.rotation)}else f.fillRect(d.leftForLtr(e,m),t,m,g),0!==o&&f.strokeRect(d.leftForLtr(e,m),t,m,g);f.restore()}}(k,w,t),v[r].left=d.leftForLtr(k,v[r].width),v[r].top=w,function(e,t,n,r){var i=g/2,a=d.xPlus(e,m+i),o=t+i;f.fillText(n.text,a,o),n.hidden&&(f.beginPath(),f.lineWidth=2,f.moveTo(a,o),f.lineTo(d.xPlus(a,r),o),f.stroke())}(k,w,t,h),b?c.x+=p+n.padding:c.y+=x})),V.rtl.restoreTextDirection(e.ctx,t.textDirection)}},_getLegendItemAt:function(e,t){var n,r,i,a=this;if(e>=a.left&&e<=a.right&&t>=a.top&&t<=a.bottom)for(i=a.legendHitBoxes,n=0;n<i.length;++n)if(e>=(r=i[n]).left&&e<=r.left+r.width&&t>=r.top&&t<=r.top+r.height)return a.legendItems[n];return null},handleEvent:function(e){var t,n=this,r=n.options,i="mouseup"===e.type?"click":e.type;if("mousemove"===i){if(!r.onHover&&!r.onLeave)return}else{if("click"!==i)return;if(!r.onClick)return}t=n._getLegendItemAt(e.x,e.y),"click"===i?t&&r.onClick&&r.onClick.call(n,e.native,t):(r.onLeave&&t!==n._hoveredItem&&(n._hoveredItem&&r.onLeave.call(n,e.native,n._hoveredItem),n._hoveredItem=t),r.onHover&&t&&r.onHover.call(n,e.native,t))}});function Tr(e,t){var n=new Sr({ctx:e.ctx,options:t,chart:e});pt.configure(e,n,t),pt.addBox(e,n),e.legend=n}var Mr={id:"legend",_element:Sr,beforeInit:function(e){var t=e.options.legend;t&&Tr(e,t)},beforeUpdate:function(e){var t=e.options.legend,n=e.legend;t?(V.mergeIf(t,R.global.legend),n?(pt.configure(e,n,t),n.options=t):Tr(e,t)):n&&(pt.removeBox(e,n),delete e.legend)},afterEvent:function(e,t){var n=e.legend;n&&n.handleEvent(t)}},Cr=V.noop;R._set("global",{title:{display:!1,fontStyle:"bold",fullWidth:!0,padding:10,position:"top",text:"",weight:2e3}});var Pr=G.extend({initialize:function(e){V.extend(this,e),this.legendHitBoxes=[]},beforeUpdate:Cr,update:function(e,t,n){var r=this;return r.beforeUpdate(),r.maxWidth=e,r.maxHeight=t,r.margins=n,r.beforeSetDimensions(),r.setDimensions(),r.afterSetDimensions(),r.beforeBuildLabels(),r.buildLabels(),r.afterBuildLabels(),r.beforeFit(),r.fit(),r.afterFit(),r.afterUpdate(),r.minSize},afterUpdate:Cr,beforeSetDimensions:Cr,setDimensions:function(){var e=this;e.isHorizontal()?(e.width=e.maxWidth,e.left=0,e.right=e.width):(e.height=e.maxHeight,e.top=0,e.bottom=e.height),e.paddingLeft=0,e.paddingTop=0,e.paddingRight=0,e.paddingBottom=0,e.minSize={width:0,height:0}},afterSetDimensions:Cr,beforeBuildLabels:Cr,buildLabels:Cr,afterBuildLabels:Cr,beforeFit:Cr,fit:function(){var e,t=this,n=t.options,r=t.minSize={},i=t.isHorizontal();n.display?(e=(V.isArray(n.text)?n.text.length:1)*V.options._parseFont(n).lineHeight+2*n.padding,t.width=r.width=i?t.maxWidth:e,t.height=r.height=i?e:t.maxHeight):t.width=r.width=t.height=r.height=0},afterFit:Cr,isHorizontal:function(){var e=this.options.position;return"top"===e||"bottom"===e},draw:function(){var e=this,t=e.ctx,n=e.options;if(n.display){var r,i,a,o=V.options._parseFont(n),l=o.lineHeight,s=l/2+n.padding,u=0,c=e.top,d=e.left,f=e.bottom,h=e.right;t.fillStyle=V.valueOrDefault(n.fontColor,R.global.defaultFontColor),t.font=o.string,e.isHorizontal()?(i=d+(h-d)/2,a=c+s,r=h-d):(i="left"===n.position?d+s:h-s,a=c+(f-c)/2,r=f-c,u=Math.PI*("left"===n.position?-.5:.5)),t.save(),t.translate(i,a),t.rotate(u),t.textAlign="center",t.textBaseline="middle";var p=n.text;if(V.isArray(p))for(var g=0,m=0;m<p.length;++m)t.fillText(p[m],0,g,r),g+=l;else t.fillText(p,0,0,r);t.restore()}}});function Dr(e,t){var n=new Pr({ctx:e.ctx,options:t,chart:e});pt.configure(e,n,t),pt.addBox(e,n),e.titleBlock=n}var Er={},Or=br,Nr=Mr,Ir={id:"title",_element:Pr,beforeInit:function(e){var t=e.options.title;t&&Dr(e,t)},beforeUpdate:function(e){var t=e.options.title,n=e.titleBlock;t?(V.mergeIf(t,R.global.title),n?(pt.configure(e,n,t),n.options=t):Dr(e,t)):n&&(pt.removeBox(e,n),delete e.titleBlock)}};for(var Ar in Er.filler=Or,Er.legend=Nr,Er.title=Ir,Zt.helpers=V,function(){function e(e,t,n){var r;return"string"===typeof e?(r=parseInt(e,10),-1!==e.indexOf("%")&&(r=r/100*t.parentNode[n])):r=e,r}function t(e){return void 0!==e&&null!==e&&"none"!==e}function n(n,r,i){var a=document.defaultView,o=V._getParentNode(n),l=a.getComputedStyle(n)[r],s=a.getComputedStyle(o)[r],u=t(l),c=t(s),d=Number.POSITIVE_INFINITY;return u||c?Math.min(u?e(l,n,i):d,c?e(s,o,i):d):"none"}V.where=function(e,t){if(V.isArray(e)&&Array.prototype.filter)return e.filter(t);var n=[];return V.each(e,(function(e){t(e)&&n.push(e)})),n},V.findIndex=Array.prototype.findIndex?function(e,t,n){return e.findIndex(t,n)}:function(e,t,n){n=void 0===n?e:n;for(var r=0,i=e.length;r<i;++r)if(t.call(n,e[r],r,e))return r;return-1},V.findNextWhere=function(e,t,n){V.isNullOrUndef(n)&&(n=-1);for(var r=n+1;r<e.length;r++){var i=e[r];if(t(i))return i}},V.findPreviousWhere=function(e,t,n){V.isNullOrUndef(n)&&(n=e.length);for(var r=n-1;r>=0;r--){var i=e[r];if(t(i))return i}},V.isNumber=function(e){return!isNaN(parseFloat(e))&&isFinite(e)},V.almostEquals=function(e,t,n){return Math.abs(e-t)<n},V.almostWhole=function(e,t){var n=Math.round(e);return n-t<=e&&n+t>=e},V.max=function(e){return e.reduce((function(e,t){return isNaN(t)?e:Math.max(e,t)}),Number.NEGATIVE_INFINITY)},V.min=function(e){return e.reduce((function(e,t){return isNaN(t)?e:Math.min(e,t)}),Number.POSITIVE_INFINITY)},V.sign=Math.sign?function(e){return Math.sign(e)}:function(e){return 0===(e=+e)||isNaN(e)?e:e>0?1:-1},V.toRadians=function(e){return e*(Math.PI/180)},V.toDegrees=function(e){return e*(180/Math.PI)},V._decimalPlaces=function(e){if(V.isFinite(e)){for(var t=1,n=0;Math.round(e*t)/t!==e;)t*=10,n++;return n}},V.getAngleFromPoint=function(e,t){var n=t.x-e.x,r=t.y-e.y,i=Math.sqrt(n*n+r*r),a=Math.atan2(r,n);return a<-.5*Math.PI&&(a+=2*Math.PI),{angle:a,distance:i}},V.distanceBetweenPoints=function(e,t){return Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2))},V.aliasPixel=function(e){return e%2===0?0:.5},V._alignPixel=function(e,t,n){var r=e.currentDevicePixelRatio,i=n/2;return Math.round((t-i)*r)/r+i},V.splineCurve=function(e,t,n,r){var i=e.skip?t:e,a=t,o=n.skip?t:n,l=Math.sqrt(Math.pow(a.x-i.x,2)+Math.pow(a.y-i.y,2)),s=Math.sqrt(Math.pow(o.x-a.x,2)+Math.pow(o.y-a.y,2)),u=l/(l+s),c=s/(l+s),d=r*(u=isNaN(u)?0:u),f=r*(c=isNaN(c)?0:c);return{previous:{x:a.x-d*(o.x-i.x),y:a.y-d*(o.y-i.y)},next:{x:a.x+f*(o.x-i.x),y:a.y+f*(o.y-i.y)}}},V.EPSILON=Number.EPSILON||1e-14,V.splineCurveMonotone=function(e){var t,n,r,i,a,o,l,s,u,c=(e||[]).map((function(e){return{model:e._model,deltaK:0,mK:0}})),d=c.length;for(t=0;t<d;++t)if(!(r=c[t]).model.skip){if(n=t>0?c[t-1]:null,(i=t<d-1?c[t+1]:null)&&!i.model.skip){var f=i.model.x-r.model.x;r.deltaK=0!==f?(i.model.y-r.model.y)/f:0}!n||n.model.skip?r.mK=r.deltaK:!i||i.model.skip?r.mK=n.deltaK:this.sign(n.deltaK)!==this.sign(r.deltaK)?r.mK=0:r.mK=(n.deltaK+r.deltaK)/2}for(t=0;t<d-1;++t)r=c[t],i=c[t+1],r.model.skip||i.model.skip||(V.almostEquals(r.deltaK,0,this.EPSILON)?r.mK=i.mK=0:(a=r.mK/r.deltaK,o=i.mK/r.deltaK,(s=Math.pow(a,2)+Math.pow(o,2))<=9||(l=3/Math.sqrt(s),r.mK=a*l*r.deltaK,i.mK=o*l*r.deltaK)));for(t=0;t<d;++t)(r=c[t]).model.skip||(n=t>0?c[t-1]:null,i=t<d-1?c[t+1]:null,n&&!n.model.skip&&(u=(r.model.x-n.model.x)/3,r.model.controlPointPreviousX=r.model.x-u,r.model.controlPointPreviousY=r.model.y-u*r.mK),i&&!i.model.skip&&(u=(i.model.x-r.model.x)/3,r.model.controlPointNextX=r.model.x+u,r.model.controlPointNextY=r.model.y+u*r.mK))},V.nextItem=function(e,t,n){return n?t>=e.length-1?e[0]:e[t+1]:t>=e.length-1?e[e.length-1]:e[t+1]},V.previousItem=function(e,t,n){return n?t<=0?e[e.length-1]:e[t-1]:t<=0?e[0]:e[t-1]},V.niceNum=function(e,t){var n=Math.floor(V.log10(e)),r=e/Math.pow(10,n);return(t?r<1.5?1:r<3?2:r<7?5:10:r<=1?1:r<=2?2:r<=5?5:10)*Math.pow(10,n)},V.requestAnimFrame="undefined"===typeof window?function(e){e()}:window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame||function(e){return window.setTimeout(e,1e3/60)},V.getRelativePosition=function(e,t){var n,r,i=e.originalEvent||e,a=e.target||e.srcElement,o=a.getBoundingClientRect(),l=i.touches;l&&l.length>0?(n=l[0].clientX,r=l[0].clientY):(n=i.clientX,r=i.clientY);var s=parseFloat(V.getStyle(a,"padding-left")),u=parseFloat(V.getStyle(a,"padding-top")),c=parseFloat(V.getStyle(a,"padding-right")),d=parseFloat(V.getStyle(a,"padding-bottom")),f=o.right-o.left-s-c,h=o.bottom-o.top-u-d;return{x:n=Math.round((n-o.left-s)/f*a.width/t.currentDevicePixelRatio),y:r=Math.round((r-o.top-u)/h*a.height/t.currentDevicePixelRatio)}},V.getConstraintWidth=function(e){return n(e,"max-width","clientWidth")},V.getConstraintHeight=function(e){return n(e,"max-height","clientHeight")},V._calculatePadding=function(e,t,n){return(t=V.getStyle(e,t)).indexOf("%")>-1?n*parseInt(t,10)/100:parseInt(t,10)},V._getParentNode=function(e){var t=e.parentNode;return t&&"[object ShadowRoot]"===t.toString()&&(t=t.host),t},V.getMaximumWidth=function(e){var t=V._getParentNode(e);if(!t)return e.clientWidth;var n=t.clientWidth,r=n-V._calculatePadding(t,"padding-left",n)-V._calculatePadding(t,"padding-right",n),i=V.getConstraintWidth(e);return isNaN(i)?r:Math.min(r,i)},V.getMaximumHeight=function(e){var t=V._getParentNode(e);if(!t)return e.clientHeight;var n=t.clientHeight,r=n-V._calculatePadding(t,"padding-top",n)-V._calculatePadding(t,"padding-bottom",n),i=V.getConstraintHeight(e);return isNaN(i)?r:Math.min(r,i)},V.getStyle=function(e,t){return e.currentStyle?e.currentStyle[t]:document.defaultView.getComputedStyle(e,null).getPropertyValue(t)},V.retinaScale=function(e,t){var n=e.currentDevicePixelRatio=t||"undefined"!==typeof window&&window.devicePixelRatio||1;if(1!==n){var r=e.canvas,i=e.height,a=e.width;r.height=i*n,r.width=a*n,e.ctx.scale(n,n),r.style.height||r.style.width||(r.style.height=i+"px",r.style.width=a+"px")}},V.fontString=function(e,t,n){return t+" "+e+"px "+n},V.longestText=function(e,t,n,r){var i=(r=r||{}).data=r.data||{},a=r.garbageCollect=r.garbageCollect||[];r.font!==t&&(i=r.data={},a=r.garbageCollect=[],r.font=t),e.font=t;var o,l,s,u,c,d=0,f=n.length;for(o=0;o<f;o++)if(void 0!==(u=n[o])&&null!==u&&!0!==V.isArray(u))d=V.measureText(e,i,a,d,u);else if(V.isArray(u))for(l=0,s=u.length;l<s;l++)void 0===(c=u[l])||null===c||V.isArray(c)||(d=V.measureText(e,i,a,d,c));var h=a.length/2;if(h>n.length){for(o=0;o<h;o++)delete i[a[o]];a.splice(0,h)}return d},V.measureText=function(e,t,n,r,i){var a=t[i];return a||(a=t[i]=e.measureText(i).width,n.push(i)),a>r&&(r=a),r},V.numberOfLabelLines=function(e){var t=1;return V.each(e,(function(e){V.isArray(e)&&e.length>t&&(t=e.length)})),t},V.color=_?function(e){return e instanceof CanvasGradient&&(e=R.global.defaultColor),_(e)}:function(e){return console.error("Color.js not found!"),e},V.getHoverColor=function(e){return e instanceof CanvasPattern||e instanceof CanvasGradient?e:V.color(e).saturate(.5).darken(.1).rgbString()}}(),Zt._adapters=en,Zt.Animation=K,Zt.animationService=Z,Zt.controllers=Ze,Zt.DatasetController=ne,Zt.defaults=R,Zt.Element=G,Zt.elements=_e,Zt.Interaction=it,Zt.layouts=pt,Zt.platform=Pt,Zt.plugins=Dt,Zt.Scale=mn,Zt.scaleService=Et,Zt.Ticks=tn,Zt.Tooltip=Yt,Zt.helpers.each(ur,(function(e,t){Zt.scaleService.registerScaleType(t,e,e._defaults)})),Er)Er.hasOwnProperty(Ar)&&Zt.plugins.register(Er[Ar]);Zt.platform.initialize();var Fr=Zt;return"undefined"!==typeof window&&(window.Chart=Zt),Zt.Chart=Zt,Zt.Legend=Er.legend._element,Zt.Title=Er.title._element,Zt.pluginService=Zt.plugins,Zt.PluginBase=Zt.Element.extend({}),Zt.canvasHelpers=Zt.helpers.canvas,Zt.layoutService=Zt.layouts,Zt.LinearScaleBase=wn,Zt.helpers.each(["Bar","Bubble","Doughnut","Line","PolarArea","Radar","Scatter"],(function(e){Zt[e]=function(t,n){return new Zt(t,Zt.helpers.merge(n||{},{type:e.charAt(0).toLowerCase()+e.slice(1)}))}})),Fr}(function(){try{return n(61)}catch(e){}}())},function(e,t,n){(function(e){e.exports=function(){"use strict";var t,n;function r(){return t.apply(null,arguments)}function i(e){return e instanceof Array||"[object Array]"===Object.prototype.toString.call(e)}function a(e){return null!=e&&"[object Object]"===Object.prototype.toString.call(e)}function o(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function l(e){if(Object.getOwnPropertyNames)return 0===Object.getOwnPropertyNames(e).length;var t;for(t in e)if(o(e,t))return!1;return!0}function s(e){return void 0===e}function u(e){return"number"===typeof e||"[object Number]"===Object.prototype.toString.call(e)}function c(e){return e instanceof Date||"[object Date]"===Object.prototype.toString.call(e)}function d(e,t){var n,r=[];for(n=0;n<e.length;++n)r.push(t(e[n],n));return r}function f(e,t){for(var n in t)o(t,n)&&(e[n]=t[n]);return o(t,"toString")&&(e.toString=t.toString),o(t,"valueOf")&&(e.valueOf=t.valueOf),e}function h(e,t,n,r){return St(e,t,n,r,!0).utc()}function p(e){return null==e._pf&&(e._pf={empty:!1,unusedTokens:[],unusedInput:[],overflow:-2,charsLeftOver:0,nullInput:!1,invalidEra:null,invalidMonth:null,invalidFormat:!1,userInvalidated:!1,iso:!1,parsedDateParts:[],era:null,meridiem:null,rfc2822:!1,weekdayMismatch:!1}),e._pf}function g(e){if(null==e._isValid){var t=p(e),r=n.call(t.parsedDateParts,(function(e){return null!=e})),i=!isNaN(e._d.getTime())&&t.overflow<0&&!t.empty&&!t.invalidEra&&!t.invalidMonth&&!t.invalidWeekday&&!t.weekdayMismatch&&!t.nullInput&&!t.invalidFormat&&!t.userInvalidated&&(!t.meridiem||t.meridiem&&r);if(e._strict&&(i=i&&0===t.charsLeftOver&&0===t.unusedTokens.length&&void 0===t.bigHour),null!=Object.isFrozen&&Object.isFrozen(e))return i;e._isValid=i}return e._isValid}function m(e){var t=h(NaN);return null!=e?f(p(t),e):p(t).userInvalidated=!0,t}n=Array.prototype.some?Array.prototype.some:function(e){var t,n=Object(this),r=n.length>>>0;for(t=0;t<r;t++)if(t in n&&e.call(this,n[t],t,n))return!0;return!1};var v=r.momentProperties=[],y=!1;function b(e,t){var n,r,i;if(s(t._isAMomentObject)||(e._isAMomentObject=t._isAMomentObject),s(t._i)||(e._i=t._i),s(t._f)||(e._f=t._f),s(t._l)||(e._l=t._l),s(t._strict)||(e._strict=t._strict),s(t._tzm)||(e._tzm=t._tzm),s(t._isUTC)||(e._isUTC=t._isUTC),s(t._offset)||(e._offset=t._offset),s(t._pf)||(e._pf=p(t)),s(t._locale)||(e._locale=t._locale),v.length>0)for(n=0;n<v.length;n++)s(i=t[r=v[n]])||(e[r]=i);return e}function x(e){b(this,e),this._d=new Date(null!=e._d?e._d.getTime():NaN),this.isValid()||(this._d=new Date(NaN)),!1===y&&(y=!0,r.updateOffset(this),y=!1)}function _(e){return e instanceof x||null!=e&&null!=e._isAMomentObject}function w(e){!1===r.suppressDeprecationWarnings&&"undefined"!==typeof console&&console.warn&&console.warn("Deprecation warning: "+e)}function k(e,t){var n=!0;return f((function(){if(null!=r.deprecationHandler&&r.deprecationHandler(null,e),n){var i,a,l,s=[];for(a=0;a<arguments.length;a++){if(i="","object"===typeof arguments[a]){for(l in i+="\n["+a+"] ",arguments[0])o(arguments[0],l)&&(i+=l+": "+arguments[0][l]+", ");i=i.slice(0,-2)}else i=arguments[a];s.push(i)}w(e+"\nArguments: "+Array.prototype.slice.call(s).join("")+"\n"+(new Error).stack),n=!1}return t.apply(this,arguments)}),t)}var S,T={};function M(e,t){null!=r.deprecationHandler&&r.deprecationHandler(e,t),T[e]||(w(t),T[e]=!0)}function C(e){return"undefined"!==typeof Function&&e instanceof Function||"[object Function]"===Object.prototype.toString.call(e)}function P(e,t){var n,r=f({},e);for(n in t)o(t,n)&&(a(e[n])&&a(t[n])?(r[n]={},f(r[n],e[n]),f(r[n],t[n])):null!=t[n]?r[n]=t[n]:delete r[n]);for(n in e)o(e,n)&&!o(t,n)&&a(e[n])&&(r[n]=f({},r[n]));return r}function D(e){null!=e&&this.set(e)}function E(e,t,n){var r=""+Math.abs(e),i=t-r.length;return(e>=0?n?"+":"":"-")+Math.pow(10,Math.max(0,i)).toString().substr(1)+r}r.suppressDeprecationWarnings=!1,r.deprecationHandler=null,S=Object.keys?Object.keys:function(e){var t,n=[];for(t in e)o(e,t)&&n.push(t);return n};var O=/(\[[^\[]*\])|(\\)?([Hh]mm(ss)?|Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Qo?|N{1,5}|YYYYYY|YYYYY|YYYY|YY|y{2,4}|yo?|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|kk?|mm?|ss?|S{1,9}|x|X|zz?|ZZ?|.)/g,N=/(\[[^\[]*\])|(\\)?(LTS|LT|LL?L?L?|l{1,4})/g,I={},A={};function F(e,t,n,r){var i=r;"string"===typeof r&&(i=function(){return this[r]()}),e&&(A[e]=i),t&&(A[t[0]]=function(){return E(i.apply(this,arguments),t[1],t[2])}),n&&(A[n]=function(){return this.localeData().ordinal(i.apply(this,arguments),e)})}function R(e,t){return e.isValid()?(t=L(t,e.localeData()),I[t]=I[t]||function(e){var t,n,r,i=e.match(O);for(t=0,n=i.length;t<n;t++)A[i[t]]?i[t]=A[i[t]]:i[t]=(r=i[t]).match(/\[[\s\S]/)?r.replace(/^\[|\]$/g,""):r.replace(/\\/g,"");return function(t){var r,a="";for(r=0;r<n;r++)a+=C(i[r])?i[r].call(t,e):i[r];return a}}(t),I[t](e)):e.localeData().invalidDate()}function L(e,t){var n=5;function r(e){return t.longDateFormat(e)||e}for(N.lastIndex=0;n>=0&&N.test(e);)e=e.replace(N,r),N.lastIndex=0,n-=1;return e}var z={};function j(e,t){var n=e.toLowerCase();z[n]=z[n+"s"]=z[t]=e}function W(e){return"string"===typeof e?z[e]||z[e.toLowerCase()]:void 0}function V(e){var t,n,r={};for(n in e)o(e,n)&&(t=W(n))&&(r[t]=e[n]);return r}var Y={};function B(e,t){Y[e]=t}function H(e){return e%4===0&&e%100!==0||e%400===0}function U(e){return e<0?Math.ceil(e)||0:Math.floor(e)}function q(e){var t=+e,n=0;return 0!==t&&isFinite(t)&&(n=U(t)),n}function $(e,t){return function(n){return null!=n?(Q(this,e,n),r.updateOffset(this,t),this):G(this,e)}}function G(e,t){return e.isValid()?e._d["get"+(e._isUTC?"UTC":"")+t]():NaN}function Q(e,t,n){e.isValid()&&!isNaN(n)&&("FullYear"===t&&H(e.year())&&1===e.month()&&29===e.date()?(n=q(n),e._d["set"+(e._isUTC?"UTC":"")+t](n,e.month(),_e(n,e.month()))):e._d["set"+(e._isUTC?"UTC":"")+t](n))}var K,Z=/\d/,X=/\d\d/,J=/\d{3}/,ee=/\d{4}/,te=/[+-]?\d{6}/,ne=/\d\d?/,re=/\d\d\d\d?/,ie=/\d\d\d\d\d\d?/,ae=/\d{1,3}/,oe=/\d{1,4}/,le=/[+-]?\d{1,6}/,se=/\d+/,ue=/[+-]?\d+/,ce=/Z|[+-]\d\d:?\d\d/gi,de=/Z|[+-]\d\d(?::?\d\d)?/gi,fe=/[0-9]{0,256}['a-z\u00A0-\u05FF\u0700-\uD7FF\uF900-\uFDCF\uFDF0-\uFF07\uFF10-\uFFEF]{1,256}|[\u0600-\u06FF\/]{1,256}(\s*?[\u0600-\u06FF]{1,256}){1,2}/i;function he(e,t,n){K[e]=C(t)?t:function(e,r){return e&&n?n:t}}function pe(e,t){return o(K,e)?K[e](t._strict,t._locale):new RegExp(ge(e.replace("\\","").replace(/\\(\[)|\\(\])|\[([^\]\[]*)\]|\\(.)/g,(function(e,t,n,r,i){return t||n||r||i}))))}function ge(e){return e.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")}K={};var me,ve={};function ye(e,t){var n,r=t;for("string"===typeof e&&(e=[e]),u(t)&&(r=function(e,n){n[t]=q(e)}),n=0;n<e.length;n++)ve[e[n]]=r}function be(e,t){ye(e,(function(e,n,r,i){r._w=r._w||{},t(e,r._w,r,i)}))}function xe(e,t,n){null!=t&&o(ve,e)&&ve[e](t,n._a,n,e)}function _e(e,t){if(isNaN(e)||isNaN(t))return NaN;var n,r=(t%(n=12)+n)%n;return e+=(t-r)/12,1===r?H(e)?29:28:31-r%7%2}me=Array.prototype.indexOf?Array.prototype.indexOf:function(e){var t;for(t=0;t<this.length;++t)if(this[t]===e)return t;return-1},F("M",["MM",2],"Mo",(function(){return this.month()+1})),F("MMM",0,0,(function(e){return this.localeData().monthsShort(this,e)})),F("MMMM",0,0,(function(e){return this.localeData().months(this,e)})),j("month","M"),B("month",8),he("M",ne),he("MM",ne,X),he("MMM",(function(e,t){return t.monthsShortRegex(e)})),he("MMMM",(function(e,t){return t.monthsRegex(e)})),ye(["M","MM"],(function(e,t){t[1]=q(e)-1})),ye(["MMM","MMMM"],(function(e,t,n,r){var i=n._locale.monthsParse(e,r,n._strict);null!=i?t[1]=i:p(n).invalidMonth=e}));var we="January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ke="Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),Se=/D[oD]?(\[[^\[\]]*\]|\s)+MMMM?/,Te=fe,Me=fe;function Ce(e,t,n){var r,i,a,o=e.toLocaleLowerCase();if(!this._monthsParse)for(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[],r=0;r<12;++r)a=h([2e3,r]),this._shortMonthsParse[r]=this.monthsShort(a,"").toLocaleLowerCase(),this._longMonthsParse[r]=this.months(a,"").toLocaleLowerCase();return n?"MMM"===t?-1!==(i=me.call(this._shortMonthsParse,o))?i:null:-1!==(i=me.call(this._longMonthsParse,o))?i:null:"MMM"===t?-1!==(i=me.call(this._shortMonthsParse,o))||-1!==(i=me.call(this._longMonthsParse,o))?i:null:-1!==(i=me.call(this._longMonthsParse,o))||-1!==(i=me.call(this._shortMonthsParse,o))?i:null}function Pe(e,t){var n;if(!e.isValid())return e;if("string"===typeof t)if(/^\d+$/.test(t))t=q(t);else if(!u(t=e.localeData().monthsParse(t)))return e;return n=Math.min(e.date(),_e(e.year(),t)),e._d["set"+(e._isUTC?"UTC":"")+"Month"](t,n),e}function De(e){return null!=e?(Pe(this,e),r.updateOffset(this,!0),this):G(this,"Month")}function Ee(){function e(e,t){return t.length-e.length}var t,n,r=[],i=[],a=[];for(t=0;t<12;t++)n=h([2e3,t]),r.push(this.monthsShort(n,"")),i.push(this.months(n,"")),a.push(this.months(n,"")),a.push(this.monthsShort(n,""));for(r.sort(e),i.sort(e),a.sort(e),t=0;t<12;t++)r[t]=ge(r[t]),i[t]=ge(i[t]);for(t=0;t<24;t++)a[t]=ge(a[t]);this._monthsRegex=new RegExp("^("+a.join("|")+")","i"),this._monthsShortRegex=this._monthsRegex,this._monthsStrictRegex=new RegExp("^("+i.join("|")+")","i"),this._monthsShortStrictRegex=new RegExp("^("+r.join("|")+")","i")}function Oe(e){return H(e)?366:365}F("Y",0,0,(function(){var e=this.year();return e<=9999?E(e,4):"+"+e})),F(0,["YY",2],0,(function(){return this.year()%100})),F(0,["YYYY",4],0,"year"),F(0,["YYYYY",5],0,"year"),F(0,["YYYYYY",6,!0],0,"year"),j("year","y"),B("year",1),he("Y",ue),he("YY",ne,X),he("YYYY",oe,ee),he("YYYYY",le,te),he("YYYYYY",le,te),ye(["YYYYY","YYYYYY"],0),ye("YYYY",(function(e,t){t[0]=2===e.length?r.parseTwoDigitYear(e):q(e)})),ye("YY",(function(e,t){t[0]=r.parseTwoDigitYear(e)})),ye("Y",(function(e,t){t[0]=parseInt(e,10)})),r.parseTwoDigitYear=function(e){return q(e)+(q(e)>68?1900:2e3)};var Ne=$("FullYear",!0);function Ie(e,t,n,r,i,a,o){var l;return e<100&&e>=0?(l=new Date(e+400,t,n,r,i,a,o),isFinite(l.getFullYear())&&l.setFullYear(e)):l=new Date(e,t,n,r,i,a,o),l}function Ae(e){var t,n;return e<100&&e>=0?((n=Array.prototype.slice.call(arguments))[0]=e+400,t=new Date(Date.UTC.apply(null,n)),isFinite(t.getUTCFullYear())&&t.setUTCFullYear(e)):t=new Date(Date.UTC.apply(null,arguments)),t}function Fe(e,t,n){var r=7+t-n;return-(7+Ae(e,0,r).getUTCDay()-t)%7+r-1}function Re(e,t,n,r,i){var a,o,l=1+7*(t-1)+(7+n-r)%7+Fe(e,r,i);return l<=0?o=Oe(a=e-1)+l:l>Oe(e)?(a=e+1,o=l-Oe(e)):(a=e,o=l),{year:a,dayOfYear:o}}function Le(e,t,n){var r,i,a=Fe(e.year(),t,n),o=Math.floor((e.dayOfYear()-a-1)/7)+1;return o<1?r=o+ze(i=e.year()-1,t,n):o>ze(e.year(),t,n)?(r=o-ze(e.year(),t,n),i=e.year()+1):(i=e.year(),r=o),{week:r,year:i}}function ze(e,t,n){var r=Fe(e,t,n),i=Fe(e+1,t,n);return(Oe(e)-r+i)/7}function je(e,t){return e.slice(t,7).concat(e.slice(0,t))}F("w",["ww",2],"wo","week"),F("W",["WW",2],"Wo","isoWeek"),j("week","w"),j("isoWeek","W"),B("week",5),B("isoWeek",5),he("w",ne),he("ww",ne,X),he("W",ne),he("WW",ne,X),be(["w","ww","W","WW"],(function(e,t,n,r){t[r.substr(0,1)]=q(e)})),F("d",0,"do","day"),F("dd",0,0,(function(e){return this.localeData().weekdaysMin(this,e)})),F("ddd",0,0,(function(e){return this.localeData().weekdaysShort(this,e)})),F("dddd",0,0,(function(e){return this.localeData().weekdays(this,e)})),F("e",0,0,"weekday"),F("E",0,0,"isoWeekday"),j("day","d"),j("weekday","e"),j("isoWeekday","E"),B("day",11),B("weekday",11),B("isoWeekday",11),he("d",ne),he("e",ne),he("E",ne),he("dd",(function(e,t){return t.weekdaysMinRegex(e)})),he("ddd",(function(e,t){return t.weekdaysShortRegex(e)})),he("dddd",(function(e,t){return t.weekdaysRegex(e)})),be(["dd","ddd","dddd"],(function(e,t,n,r){var i=n._locale.weekdaysParse(e,r,n._strict);null!=i?t.d=i:p(n).invalidWeekday=e})),be(["d","e","E"],(function(e,t,n,r){t[r]=q(e)}));var We="Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),Ve="Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),Ye="Su_Mo_Tu_We_Th_Fr_Sa".split("_"),Be=fe,He=fe,Ue=fe;function qe(e,t,n){var r,i,a,o=e.toLocaleLowerCase();if(!this._weekdaysParse)for(this._weekdaysParse=[],this._shortWeekdaysParse=[],this._minWeekdaysParse=[],r=0;r<7;++r)a=h([2e3,1]).day(r),this._minWeekdaysParse[r]=this.weekdaysMin(a,"").toLocaleLowerCase(),this._shortWeekdaysParse[r]=this.weekdaysShort(a,"").toLocaleLowerCase(),this._weekdaysParse[r]=this.weekdays(a,"").toLocaleLowerCase();return n?"dddd"===t?-1!==(i=me.call(this._weekdaysParse,o))?i:null:"ddd"===t?-1!==(i=me.call(this._shortWeekdaysParse,o))?i:null:-1!==(i=me.call(this._minWeekdaysParse,o))?i:null:"dddd"===t?-1!==(i=me.call(this._weekdaysParse,o))||-1!==(i=me.call(this._shortWeekdaysParse,o))||-1!==(i=me.call(this._minWeekdaysParse,o))?i:null:"ddd"===t?-1!==(i=me.call(this._shortWeekdaysParse,o))||-1!==(i=me.call(this._weekdaysParse,o))||-1!==(i=me.call(this._minWeekdaysParse,o))?i:null:-1!==(i=me.call(this._minWeekdaysParse,o))||-1!==(i=me.call(this._weekdaysParse,o))||-1!==(i=me.call(this._shortWeekdaysParse,o))?i:null}function $e(){function e(e,t){return t.length-e.length}var t,n,r,i,a,o=[],l=[],s=[],u=[];for(t=0;t<7;t++)n=h([2e3,1]).day(t),r=ge(this.weekdaysMin(n,"")),i=ge(this.weekdaysShort(n,"")),a=ge(this.weekdays(n,"")),o.push(r),l.push(i),s.push(a),u.push(r),u.push(i),u.push(a);o.sort(e),l.sort(e),s.sort(e),u.sort(e),this._weekdaysRegex=new RegExp("^("+u.join("|")+")","i"),this._weekdaysShortRegex=this._weekdaysRegex,this._weekdaysMinRegex=this._weekdaysRegex,this._weekdaysStrictRegex=new RegExp("^("+s.join("|")+")","i"),this._weekdaysShortStrictRegex=new RegExp("^("+l.join("|")+")","i"),this._weekdaysMinStrictRegex=new RegExp("^("+o.join("|")+")","i")}function Ge(){return this.hours()%12||12}function Qe(e,t){F(e,0,0,(function(){return this.localeData().meridiem(this.hours(),this.minutes(),t)}))}function Ke(e,t){return t._meridiemParse}F("H",["HH",2],0,"hour"),F("h",["hh",2],0,Ge),F("k",["kk",2],0,(function(){return this.hours()||24})),F("hmm",0,0,(function(){return""+Ge.apply(this)+E(this.minutes(),2)})),F("hmmss",0,0,(function(){return""+Ge.apply(this)+E(this.minutes(),2)+E(this.seconds(),2)})),F("Hmm",0,0,(function(){return""+this.hours()+E(this.minutes(),2)})),F("Hmmss",0,0,(function(){return""+this.hours()+E(this.minutes(),2)+E(this.seconds(),2)})),Qe("a",!0),Qe("A",!1),j("hour","h"),B("hour",13),he("a",Ke),he("A",Ke),he("H",ne),he("h",ne),he("k",ne),he("HH",ne,X),he("hh",ne,X),he("kk",ne,X),he("hmm",re),he("hmmss",ie),he("Hmm",re),he("Hmmss",ie),ye(["H","HH"],3),ye(["k","kk"],(function(e,t,n){var r=q(e);t[3]=24===r?0:r})),ye(["a","A"],(function(e,t,n){n._isPm=n._locale.isPM(e),n._meridiem=e})),ye(["h","hh"],(function(e,t,n){t[3]=q(e),p(n).bigHour=!0})),ye("hmm",(function(e,t,n){var r=e.length-2;t[3]=q(e.substr(0,r)),t[4]=q(e.substr(r)),p(n).bigHour=!0})),ye("hmmss",(function(e,t,n){var r=e.length-4,i=e.length-2;t[3]=q(e.substr(0,r)),t[4]=q(e.substr(r,2)),t[5]=q(e.substr(i)),p(n).bigHour=!0})),ye("Hmm",(function(e,t,n){var r=e.length-2;t[3]=q(e.substr(0,r)),t[4]=q(e.substr(r))})),ye("Hmmss",(function(e,t,n){var r=e.length-4,i=e.length-2;t[3]=q(e.substr(0,r)),t[4]=q(e.substr(r,2)),t[5]=q(e.substr(i))}));var Ze,Xe=$("Hours",!0),Je={calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},longDateFormat:{LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},invalidDate:"Invalid date",ordinal:"%d",dayOfMonthOrdinalParse:/\d{1,2}/,relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",ss:"%d seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",w:"a week",ww:"%d weeks",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},months:we,monthsShort:ke,week:{dow:0,doy:6},weekdays:We,weekdaysMin:Ye,weekdaysShort:Ve,meridiemParse:/[ap]\.?m?\.?/i},et={},tt={};function nt(e,t){var n,r=Math.min(e.length,t.length);for(n=0;n<r;n+=1)if(e[n]!==t[n])return n;return r}function rt(e){return e?e.toLowerCase().replace("_","-"):e}function it(t){var n=null;if(void 0===et[t]&&"undefined"!==typeof e&&e&&e.exports)try{n=Ze._abbr,function(){var e=new Error("Cannot find module 'undefined'");throw e.code="MODULE_NOT_FOUND",e}(),at(n)}catch(r){et[t]=null}return et[t]}function at(e,t){var n;return e&&((n=s(t)?lt(e):ot(e,t))?Ze=n:"undefined"!==typeof console&&console.warn&&console.warn("Locale "+e+" not found. Did you forget to load it?")),Ze._abbr}function ot(e,t){if(null!==t){var n,r=Je;if(t.abbr=e,null!=et[e])M("defineLocaleOverride","use moment.updateLocale(localeName, config) to change an existing locale. moment.defineLocale(localeName, config) should only be used for creating a new locale See http://momentjs.com/guides/#/warnings/define-locale/ for more info."),r=et[e]._config;else if(null!=t.parentLocale)if(null!=et[t.parentLocale])r=et[t.parentLocale]._config;else{if(null==(n=it(t.parentLocale)))return tt[t.parentLocale]||(tt[t.parentLocale]=[]),tt[t.parentLocale].push({name:e,config:t}),null;r=n._config}return et[e]=new D(P(r,t)),tt[e]&&tt[e].forEach((function(e){ot(e.name,e.config)})),at(e),et[e]}return delete et[e],null}function lt(e){var t;if(e&&e._locale&&e._locale._abbr&&(e=e._locale._abbr),!e)return Ze;if(!i(e)){if(t=it(e))return t;e=[e]}return function(e){for(var t,n,r,i,a=0;a<e.length;){for(t=(i=rt(e[a]).split("-")).length,n=(n=rt(e[a+1]))?n.split("-"):null;t>0;){if(r=it(i.slice(0,t).join("-")))return r;if(n&&n.length>=t&&nt(i,n)>=t-1)break;t--}a++}return Ze}(e)}function st(e){var t,n=e._a;return n&&-2===p(e).overflow&&(t=n[1]<0||n[1]>11?1:n[2]<1||n[2]>_e(n[0],n[1])?2:n[3]<0||n[3]>24||24===n[3]&&(0!==n[4]||0!==n[5]||0!==n[6])?3:n[4]<0||n[4]>59?4:n[5]<0||n[5]>59?5:n[6]<0||n[6]>999?6:-1,p(e)._overflowDayOfYear&&(t<0||t>2)&&(t=2),p(e)._overflowWeeks&&-1===t&&(t=7),p(e)._overflowWeekday&&-1===t&&(t=8),p(e).overflow=t),e}var ut=/^\s*((?:[+-]\d{6}|\d{4})-(?:\d\d-\d\d|W\d\d-\d|W\d\d|\d\d\d|\d\d))(?:(T| )(\d\d(?::\d\d(?::\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/,ct=/^\s*((?:[+-]\d{6}|\d{4})(?:\d\d\d\d|W\d\d\d|W\d\d|\d\d\d|\d\d|))(?:(T| )(\d\d(?:\d\d(?:\d\d(?:[.,]\d+)?)?)?)([+-]\d\d(?::?\d\d)?|\s*Z)?)?$/,dt=/Z|[+-]\d\d(?::?\d\d)?/,ft=[["YYYYYY-MM-DD",/[+-]\d{6}-\d\d-\d\d/],["YYYY-MM-DD",/\d{4}-\d\d-\d\d/],["GGGG-[W]WW-E",/\d{4}-W\d\d-\d/],["GGGG-[W]WW",/\d{4}-W\d\d/,!1],["YYYY-DDD",/\d{4}-\d{3}/],["YYYY-MM",/\d{4}-\d\d/,!1],["YYYYYYMMDD",/[+-]\d{10}/],["YYYYMMDD",/\d{8}/],["GGGG[W]WWE",/\d{4}W\d{3}/],["GGGG[W]WW",/\d{4}W\d{2}/,!1],["YYYYDDD",/\d{7}/],["YYYYMM",/\d{6}/,!1],["YYYY",/\d{4}/,!1]],ht=[["HH:mm:ss.SSSS",/\d\d:\d\d:\d\d\.\d+/],["HH:mm:ss,SSSS",/\d\d:\d\d:\d\d,\d+/],["HH:mm:ss",/\d\d:\d\d:\d\d/],["HH:mm",/\d\d:\d\d/],["HHmmss.SSSS",/\d\d\d\d\d\d\.\d+/],["HHmmss,SSSS",/\d\d\d\d\d\d,\d+/],["HHmmss",/\d\d\d\d\d\d/],["HHmm",/\d\d\d\d/],["HH",/\d\d/]],pt=/^\/?Date\((-?\d+)/i,gt=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),?\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|([+-]\d{4}))$/,mt={UT:0,GMT:0,EDT:-240,EST:-300,CDT:-300,CST:-360,MDT:-360,MST:-420,PDT:-420,PST:-480};function vt(e){var t,n,r,i,a,o,l=e._i,s=ut.exec(l)||ct.exec(l);if(s){for(p(e).iso=!0,t=0,n=ft.length;t<n;t++)if(ft[t][1].exec(s[1])){i=ft[t][0],r=!1!==ft[t][2];break}if(null==i)return void(e._isValid=!1);if(s[3]){for(t=0,n=ht.length;t<n;t++)if(ht[t][1].exec(s[3])){a=(s[2]||" ")+ht[t][0];break}if(null==a)return void(e._isValid=!1)}if(!r&&null!=a)return void(e._isValid=!1);if(s[4]){if(!dt.exec(s[4]))return void(e._isValid=!1);o="Z"}e._f=i+(a||"")+(o||""),wt(e)}else e._isValid=!1}function yt(e){var t=parseInt(e,10);return t<=49?2e3+t:t<=999?1900+t:t}function bt(e){var t,n=gt.exec(e._i.replace(/\([^)]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").replace(/^\s\s*/,"").replace(/\s\s*$/,""));if(n){if(t=function(e,t,n,r,i,a){var o=[yt(e),ke.indexOf(t),parseInt(n,10),parseInt(r,10),parseInt(i,10)];return a&&o.push(parseInt(a,10)),o}(n[4],n[3],n[2],n[5],n[6],n[7]),!function(e,t,n){return!e||Ve.indexOf(e)===new Date(t[0],t[1],t[2]).getDay()||(p(n).weekdayMismatch=!0,n._isValid=!1,!1)}(n[1],t,e))return;e._a=t,e._tzm=function(e,t,n){if(e)return mt[e];if(t)return 0;var r=parseInt(n,10),i=r%100;return(r-i)/100*60+i}(n[8],n[9],n[10]),e._d=Ae.apply(null,e._a),e._d.setUTCMinutes(e._d.getUTCMinutes()-e._tzm),p(e).rfc2822=!0}else e._isValid=!1}function xt(e,t,n){return null!=e?e:null!=t?t:n}function _t(e){var t,n,i,a,o,l=[];if(!e._d){for(i=function(e){var t=new Date(r.now());return e._useUTC?[t.getUTCFullYear(),t.getUTCMonth(),t.getUTCDate()]:[t.getFullYear(),t.getMonth(),t.getDate()]}(e),e._w&&null==e._a[2]&&null==e._a[1]&&function(e){var t,n,r,i,a,o,l,s,u;null!=(t=e._w).GG||null!=t.W||null!=t.E?(a=1,o=4,n=xt(t.GG,e._a[0],Le(Tt(),1,4).year),r=xt(t.W,1),((i=xt(t.E,1))<1||i>7)&&(s=!0)):(a=e._locale._week.dow,o=e._locale._week.doy,u=Le(Tt(),a,o),n=xt(t.gg,e._a[0],u.year),r=xt(t.w,u.week),null!=t.d?((i=t.d)<0||i>6)&&(s=!0):null!=t.e?(i=t.e+a,(t.e<0||t.e>6)&&(s=!0)):i=a),r<1||r>ze(n,a,o)?p(e)._overflowWeeks=!0:null!=s?p(e)._overflowWeekday=!0:(l=Re(n,r,i,a,o),e._a[0]=l.year,e._dayOfYear=l.dayOfYear)}(e),null!=e._dayOfYear&&(o=xt(e._a[0],i[0]),(e._dayOfYear>Oe(o)||0===e._dayOfYear)&&(p(e)._overflowDayOfYear=!0),n=Ae(o,0,e._dayOfYear),e._a[1]=n.getUTCMonth(),e._a[2]=n.getUTCDate()),t=0;t<3&&null==e._a[t];++t)e._a[t]=l[t]=i[t];for(;t<7;t++)e._a[t]=l[t]=null==e._a[t]?2===t?1:0:e._a[t];24===e._a[3]&&0===e._a[4]&&0===e._a[5]&&0===e._a[6]&&(e._nextDay=!0,e._a[3]=0),e._d=(e._useUTC?Ae:Ie).apply(null,l),a=e._useUTC?e._d.getUTCDay():e._d.getDay(),null!=e._tzm&&e._d.setUTCMinutes(e._d.getUTCMinutes()-e._tzm),e._nextDay&&(e._a[3]=24),e._w&&"undefined"!==typeof e._w.d&&e._w.d!==a&&(p(e).weekdayMismatch=!0)}}function wt(e){if(e._f!==r.ISO_8601)if(e._f!==r.RFC_2822){e._a=[],p(e).empty=!0;var t,n,i,a,o,l,s=""+e._i,u=s.length,c=0;for(i=L(e._f,e._locale).match(O)||[],t=0;t<i.length;t++)a=i[t],(n=(s.match(pe(a,e))||[])[0])&&((o=s.substr(0,s.indexOf(n))).length>0&&p(e).unusedInput.push(o),s=s.slice(s.indexOf(n)+n.length),c+=n.length),A[a]?(n?p(e).empty=!1:p(e).unusedTokens.push(a),xe(a,n,e)):e._strict&&!n&&p(e).unusedTokens.push(a);p(e).charsLeftOver=u-c,s.length>0&&p(e).unusedInput.push(s),e._a[3]<=12&&!0===p(e).bigHour&&e._a[3]>0&&(p(e).bigHour=void 0),p(e).parsedDateParts=e._a.slice(0),p(e).meridiem=e._meridiem,e._a[3]=function(e,t,n){var r;return null==n?t:null!=e.meridiemHour?e.meridiemHour(t,n):null!=e.isPM?((r=e.isPM(n))&&t<12&&(t+=12),r||12!==t||(t=0),t):t}(e._locale,e._a[3],e._meridiem),null!==(l=p(e).era)&&(e._a[0]=e._locale.erasConvertYear(l,e._a[0])),_t(e),st(e)}else bt(e);else vt(e)}function kt(e){var t=e._i,n=e._f;return e._locale=e._locale||lt(e._l),null===t||void 0===n&&""===t?m({nullInput:!0}):("string"===typeof t&&(e._i=t=e._locale.preparse(t)),_(t)?new x(st(t)):(c(t)?e._d=t:i(n)?function(e){var t,n,r,i,a,o,l=!1;if(0===e._f.length)return p(e).invalidFormat=!0,void(e._d=new Date(NaN));for(i=0;i<e._f.length;i++)a=0,o=!1,t=b({},e),null!=e._useUTC&&(t._useUTC=e._useUTC),t._f=e._f[i],wt(t),g(t)&&(o=!0),a+=p(t).charsLeftOver,a+=10*p(t).unusedTokens.length,p(t).score=a,l?a<r&&(r=a,n=t):(null==r||a<r||o)&&(r=a,n=t,o&&(l=!0));f(e,n||t)}(e):n?wt(e):function(e){var t=e._i;s(t)?e._d=new Date(r.now()):c(t)?e._d=new Date(t.valueOf()):"string"===typeof t?function(e){var t=pt.exec(e._i);null===t?(vt(e),!1===e._isValid&&(delete e._isValid,bt(e),!1===e._isValid&&(delete e._isValid,e._strict?e._isValid=!1:r.createFromInputFallback(e)))):e._d=new Date(+t[1])}(e):i(t)?(e._a=d(t.slice(0),(function(e){return parseInt(e,10)})),_t(e)):a(t)?function(e){if(!e._d){var t=V(e._i),n=void 0===t.day?t.date:t.day;e._a=d([t.year,t.month,n,t.hour,t.minute,t.second,t.millisecond],(function(e){return e&&parseInt(e,10)})),_t(e)}}(e):u(t)?e._d=new Date(t):r.createFromInputFallback(e)}(e),g(e)||(e._d=null),e))}function St(e,t,n,r,o){var s={};return!0!==t&&!1!==t||(r=t,t=void 0),!0!==n&&!1!==n||(r=n,n=void 0),(a(e)&&l(e)||i(e)&&0===e.length)&&(e=void 0),s._isAMomentObject=!0,s._useUTC=s._isUTC=o,s._l=n,s._i=e,s._f=t,s._strict=r,function(e){var t=new x(st(kt(e)));return t._nextDay&&(t.add(1,"d"),t._nextDay=void 0),t}(s)}function Tt(e,t,n,r){return St(e,t,n,r,!1)}r.createFromInputFallback=k("value provided is not in a recognized RFC2822 or ISO format. moment construction falls back to js Date(), which is not reliable across all browsers and versions. Non RFC2822/ISO date formats are discouraged. Please refer to http://momentjs.com/guides/#/warnings/js-date/ for more info.",(function(e){e._d=new Date(e._i+(e._useUTC?" UTC":""))})),r.ISO_8601=function(){},r.RFC_2822=function(){};var Mt=k("moment().min is deprecated, use moment.max instead. http://momentjs.com/guides/#/warnings/min-max/",(function(){var e=Tt.apply(null,arguments);return this.isValid()&&e.isValid()?e<this?this:e:m()})),Ct=k("moment().max is deprecated, use moment.min instead. http://momentjs.com/guides/#/warnings/min-max/",(function(){var e=Tt.apply(null,arguments);return this.isValid()&&e.isValid()?e>this?this:e:m()}));function Pt(e,t){var n,r;if(1===t.length&&i(t[0])&&(t=t[0]),!t.length)return Tt();for(n=t[0],r=1;r<t.length;++r)t[r].isValid()&&!t[r][e](n)||(n=t[r]);return n}var Dt=["year","quarter","month","week","day","hour","minute","second","millisecond"];function Et(e){var t=V(e),n=t.year||0,r=t.quarter||0,i=t.month||0,a=t.week||t.isoWeek||0,l=t.day||0,s=t.hour||0,u=t.minute||0,c=t.second||0,d=t.millisecond||0;this._isValid=function(e){var t,n,r=!1;for(t in e)if(o(e,t)&&(-1===me.call(Dt,t)||null!=e[t]&&isNaN(e[t])))return!1;for(n=0;n<Dt.length;++n)if(e[Dt[n]]){if(r)return!1;parseFloat(e[Dt[n]])!==q(e[Dt[n]])&&(r=!0)}return!0}(t),this._milliseconds=+d+1e3*c+6e4*u+1e3*s*60*60,this._days=+l+7*a,this._months=+i+3*r+12*n,this._data={},this._locale=lt(),this._bubble()}function Ot(e){return e instanceof Et}function Nt(e){return e<0?-1*Math.round(-1*e):Math.round(e)}function It(e,t){F(e,0,0,(function(){var e=this.utcOffset(),n="+";return e<0&&(e=-e,n="-"),n+E(~~(e/60),2)+t+E(~~e%60,2)}))}It("Z",":"),It("ZZ",""),he("Z",de),he("ZZ",de),ye(["Z","ZZ"],(function(e,t,n){n._useUTC=!0,n._tzm=Ft(de,e)}));var At=/([\+\-]|\d\d)/gi;function Ft(e,t){var n,r,i=(t||"").match(e);return null===i?null:0===(r=60*(n=((i[i.length-1]||[])+"").match(At)||["-",0,0])[1]+q(n[2]))?0:"+"===n[0]?r:-r}function Rt(e,t){var n,i;return t._isUTC?(n=t.clone(),i=(_(e)||c(e)?e.valueOf():Tt(e).valueOf())-n.valueOf(),n._d.setTime(n._d.valueOf()+i),r.updateOffset(n,!1),n):Tt(e).local()}function Lt(e){return-Math.round(e._d.getTimezoneOffset())}function zt(){return!!this.isValid()&&this._isUTC&&0===this._offset}r.updateOffset=function(){};var jt=/^(-|\+)?(?:(\d*)[. ])?(\d+):(\d+)(?::(\d+)(\.\d*)?)?$/,Wt=/^(-|\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/;function Vt(e,t){var n,r,i,a=e,l=null;return Ot(e)?a={ms:e._milliseconds,d:e._days,M:e._months}:u(e)||!isNaN(+e)?(a={},t?a[t]=+e:a.milliseconds=+e):(l=jt.exec(e))?(n="-"===l[1]?-1:1,a={y:0,d:q(l[2])*n,h:q(l[3])*n,m:q(l[4])*n,s:q(l[5])*n,ms:q(Nt(1e3*l[6]))*n}):(l=Wt.exec(e))?(n="-"===l[1]?-1:1,a={y:Yt(l[2],n),M:Yt(l[3],n),w:Yt(l[4],n),d:Yt(l[5],n),h:Yt(l[6],n),m:Yt(l[7],n),s:Yt(l[8],n)}):null==a?a={}:"object"===typeof a&&("from"in a||"to"in a)&&(i=function(e,t){var n;return e.isValid()&&t.isValid()?(t=Rt(t,e),e.isBefore(t)?n=Bt(e,t):((n=Bt(t,e)).milliseconds=-n.milliseconds,n.months=-n.months),n):{milliseconds:0,months:0}}(Tt(a.from),Tt(a.to)),(a={}).ms=i.milliseconds,a.M=i.months),r=new Et(a),Ot(e)&&o(e,"_locale")&&(r._locale=e._locale),Ot(e)&&o(e,"_isValid")&&(r._isValid=e._isValid),r}function Yt(e,t){var n=e&&parseFloat(e.replace(",","."));return(isNaN(n)?0:n)*t}function Bt(e,t){var n={};return n.months=t.month()-e.month()+12*(t.year()-e.year()),e.clone().add(n.months,"M").isAfter(t)&&--n.months,n.milliseconds=+t-+e.clone().add(n.months,"M"),n}function Ht(e,t){return function(n,r){var i;return null===r||isNaN(+r)||(M(t,"moment()."+t+"(period, number) is deprecated. Please use moment()."+t+"(number, period). See http://momentjs.com/guides/#/warnings/add-inverted-param/ for more info."),i=n,n=r,r=i),Ut(this,Vt(n,r),e),this}}function Ut(e,t,n,i){var a=t._milliseconds,o=Nt(t._days),l=Nt(t._months);e.isValid()&&(i=null==i||i,l&&Pe(e,G(e,"Month")+l*n),o&&Q(e,"Date",G(e,"Date")+o*n),a&&e._d.setTime(e._d.valueOf()+a*n),i&&r.updateOffset(e,o||l))}Vt.fn=Et.prototype,Vt.invalid=function(){return Vt(NaN)};var qt=Ht(1,"add"),$t=Ht(-1,"subtract");function Gt(e){return"string"===typeof e||e instanceof String}function Qt(e){return _(e)||c(e)||Gt(e)||u(e)||function(e){var t=i(e),n=!1;return t&&(n=0===e.filter((function(t){return!u(t)&&Gt(e)})).length),t&&n}(e)||function(e){var t,n,r=a(e)&&!l(e),i=!1,s=["years","year","y","months","month","M","days","day","d","dates","date","D","hours","hour","h","minutes","minute","m","seconds","second","s","milliseconds","millisecond","ms"];for(t=0;t<s.length;t+=1)n=s[t],i=i||o(e,n);return r&&i}(e)||null===e||void 0===e}function Kt(e){var t,n=a(e)&&!l(e),r=!1,i=["sameDay","nextDay","lastDay","nextWeek","lastWeek","sameElse"];for(t=0;t<i.length;t+=1)r=r||o(e,i[t]);return n&&r}function Zt(e,t){if(e.date()<t.date())return-Zt(t,e);var n=12*(t.year()-e.year())+(t.month()-e.month()),r=e.clone().add(n,"months");return-(n+(t-r<0?(t-r)/(r-e.clone().add(n-1,"months")):(t-r)/(e.clone().add(n+1,"months")-r)))||0}function Xt(e){var t;return void 0===e?this._locale._abbr:(null!=(t=lt(e))&&(this._locale=t),this)}r.defaultFormat="YYYY-MM-DDTHH:mm:ssZ",r.defaultFormatUtc="YYYY-MM-DDTHH:mm:ss[Z]";var Jt=k("moment().lang() is deprecated. Instead, use moment().localeData() to get the language configuration. Use moment().locale() to change languages.",(function(e){return void 0===e?this.localeData():this.locale(e)}));function en(){return this._locale}function tn(e,t){return(e%t+t)%t}function nn(e,t,n){return e<100&&e>=0?new Date(e+400,t,n)-126227808e5:new Date(e,t,n).valueOf()}function rn(e,t,n){return e<100&&e>=0?Date.UTC(e+400,t,n)-126227808e5:Date.UTC(e,t,n)}function an(e,t){return t.erasAbbrRegex(e)}function on(){var e,t,n=[],r=[],i=[],a=[],o=this.eras();for(e=0,t=o.length;e<t;++e)r.push(ge(o[e].name)),n.push(ge(o[e].abbr)),i.push(ge(o[e].narrow)),a.push(ge(o[e].name)),a.push(ge(o[e].abbr)),a.push(ge(o[e].narrow));this._erasRegex=new RegExp("^("+a.join("|")+")","i"),this._erasNameRegex=new RegExp("^("+r.join("|")+")","i"),this._erasAbbrRegex=new RegExp("^("+n.join("|")+")","i"),this._erasNarrowRegex=new RegExp("^("+i.join("|")+")","i")}function ln(e,t){F(0,[e,e.length],0,t)}function sn(e,t,n,r,i){var a;return null==e?Le(this,r,i).year:(t>(a=ze(e,r,i))&&(t=a),un.call(this,e,t,n,r,i))}function un(e,t,n,r,i){var a=Re(e,t,n,r,i),o=Ae(a.year,0,a.dayOfYear);return this.year(o.getUTCFullYear()),this.month(o.getUTCMonth()),this.date(o.getUTCDate()),this}F("N",0,0,"eraAbbr"),F("NN",0,0,"eraAbbr"),F("NNN",0,0,"eraAbbr"),F("NNNN",0,0,"eraName"),F("NNNNN",0,0,"eraNarrow"),F("y",["y",1],"yo","eraYear"),F("y",["yy",2],0,"eraYear"),F("y",["yyy",3],0,"eraYear"),F("y",["yyyy",4],0,"eraYear"),he("N",an),he("NN",an),he("NNN",an),he("NNNN",(function(e,t){return t.erasNameRegex(e)})),he("NNNNN",(function(e,t){return t.erasNarrowRegex(e)})),ye(["N","NN","NNN","NNNN","NNNNN"],(function(e,t,n,r){var i=n._locale.erasParse(e,r,n._strict);i?p(n).era=i:p(n).invalidEra=e})),he("y",se),he("yy",se),he("yyy",se),he("yyyy",se),he("yo",(function(e,t){return t._eraYearOrdinalRegex||se})),ye(["y","yy","yyy","yyyy"],0),ye(["yo"],(function(e,t,n,r){var i;n._locale._eraYearOrdinalRegex&&(i=e.match(n._locale._eraYearOrdinalRegex)),n._locale.eraYearOrdinalParse?t[0]=n._locale.eraYearOrdinalParse(e,i):t[0]=parseInt(e,10)})),F(0,["gg",2],0,(function(){return this.weekYear()%100})),F(0,["GG",2],0,(function(){return this.isoWeekYear()%100})),ln("gggg","weekYear"),ln("ggggg","weekYear"),ln("GGGG","isoWeekYear"),ln("GGGGG","isoWeekYear"),j("weekYear","gg"),j("isoWeekYear","GG"),B("weekYear",1),B("isoWeekYear",1),he("G",ue),he("g",ue),he("GG",ne,X),he("gg",ne,X),he("GGGG",oe,ee),he("gggg",oe,ee),he("GGGGG",le,te),he("ggggg",le,te),be(["gggg","ggggg","GGGG","GGGGG"],(function(e,t,n,r){t[r.substr(0,2)]=q(e)})),be(["gg","GG"],(function(e,t,n,i){t[i]=r.parseTwoDigitYear(e)})),F("Q",0,"Qo","quarter"),j("quarter","Q"),B("quarter",7),he("Q",Z),ye("Q",(function(e,t){t[1]=3*(q(e)-1)})),F("D",["DD",2],"Do","date"),j("date","D"),B("date",9),he("D",ne),he("DD",ne,X),he("Do",(function(e,t){return e?t._dayOfMonthOrdinalParse||t._ordinalParse:t._dayOfMonthOrdinalParseLenient})),ye(["D","DD"],2),ye("Do",(function(e,t){t[2]=q(e.match(ne)[0])}));var cn=$("Date",!0);F("DDD",["DDDD",3],"DDDo","dayOfYear"),j("dayOfYear","DDD"),B("dayOfYear",4),he("DDD",ae),he("DDDD",J),ye(["DDD","DDDD"],(function(e,t,n){n._dayOfYear=q(e)})),F("m",["mm",2],0,"minute"),j("minute","m"),B("minute",14),he("m",ne),he("mm",ne,X),ye(["m","mm"],4);var dn=$("Minutes",!1);F("s",["ss",2],0,"second"),j("second","s"),B("second",15),he("s",ne),he("ss",ne,X),ye(["s","ss"],5);var fn,hn,pn=$("Seconds",!1);for(F("S",0,0,(function(){return~~(this.millisecond()/100)})),F(0,["SS",2],0,(function(){return~~(this.millisecond()/10)})),F(0,["SSS",3],0,"millisecond"),F(0,["SSSS",4],0,(function(){return 10*this.millisecond()})),F(0,["SSSSS",5],0,(function(){return 100*this.millisecond()})),F(0,["SSSSSS",6],0,(function(){return 1e3*this.millisecond()})),F(0,["SSSSSSS",7],0,(function(){return 1e4*this.millisecond()})),F(0,["SSSSSSSS",8],0,(function(){return 1e5*this.millisecond()})),F(0,["SSSSSSSSS",9],0,(function(){return 1e6*this.millisecond()})),j("millisecond","ms"),B("millisecond",16),he("S",ae,Z),he("SS",ae,X),he("SSS",ae,J),fn="SSSS";fn.length<=9;fn+="S")he(fn,se);function gn(e,t){t[6]=q(1e3*("0."+e))}for(fn="S";fn.length<=9;fn+="S")ye(fn,gn);hn=$("Milliseconds",!1),F("z",0,0,"zoneAbbr"),F("zz",0,0,"zoneName");var mn=x.prototype;function vn(e){return e}mn.add=qt,mn.calendar=function(e,t){1===arguments.length&&(arguments[0]?Qt(arguments[0])?(e=arguments[0],t=void 0):Kt(arguments[0])&&(t=arguments[0],e=void 0):(e=void 0,t=void 0));var n=e||Tt(),i=Rt(n,this).startOf("day"),a=r.calendarFormat(this,i)||"sameElse",o=t&&(C(t[a])?t[a].call(this,n):t[a]);return this.format(o||this.localeData().calendar(a,this,Tt(n)))},mn.clone=function(){return new x(this)},mn.diff=function(e,t,n){var r,i,a;if(!this.isValid())return NaN;if(!(r=Rt(e,this)).isValid())return NaN;switch(i=6e4*(r.utcOffset()-this.utcOffset()),t=W(t)){case"year":a=Zt(this,r)/12;break;case"month":a=Zt(this,r);break;case"quarter":a=Zt(this,r)/3;break;case"second":a=(this-r)/1e3;break;case"minute":a=(this-r)/6e4;break;case"hour":a=(this-r)/36e5;break;case"day":a=(this-r-i)/864e5;break;case"week":a=(this-r-i)/6048e5;break;default:a=this-r}return n?a:U(a)},mn.endOf=function(e){var t,n;if(void 0===(e=W(e))||"millisecond"===e||!this.isValid())return this;switch(n=this._isUTC?rn:nn,e){case"year":t=n(this.year()+1,0,1)-1;break;case"quarter":t=n(this.year(),this.month()-this.month()%3+3,1)-1;break;case"month":t=n(this.year(),this.month()+1,1)-1;break;case"week":t=n(this.year(),this.month(),this.date()-this.weekday()+7)-1;break;case"isoWeek":t=n(this.year(),this.month(),this.date()-(this.isoWeekday()-1)+7)-1;break;case"day":case"date":t=n(this.year(),this.month(),this.date()+1)-1;break;case"hour":t=this._d.valueOf(),t+=36e5-tn(t+(this._isUTC?0:6e4*this.utcOffset()),36e5)-1;break;case"minute":t=this._d.valueOf(),t+=6e4-tn(t,6e4)-1;break;case"second":t=this._d.valueOf(),t+=1e3-tn(t,1e3)-1}return this._d.setTime(t),r.updateOffset(this,!0),this},mn.format=function(e){e||(e=this.isUtc()?r.defaultFormatUtc:r.defaultFormat);var t=R(this,e);return this.localeData().postformat(t)},mn.from=function(e,t){return this.isValid()&&(_(e)&&e.isValid()||Tt(e).isValid())?Vt({to:this,from:e}).locale(this.locale()).humanize(!t):this.localeData().invalidDate()},mn.fromNow=function(e){return this.from(Tt(),e)},mn.to=function(e,t){return this.isValid()&&(_(e)&&e.isValid()||Tt(e).isValid())?Vt({from:this,to:e}).locale(this.locale()).humanize(!t):this.localeData().invalidDate()},mn.toNow=function(e){return this.to(Tt(),e)},mn.get=function(e){return C(this[e=W(e)])?this[e]():this},mn.invalidAt=function(){return p(this).overflow},mn.isAfter=function(e,t){var n=_(e)?e:Tt(e);return!(!this.isValid()||!n.isValid())&&("millisecond"===(t=W(t)||"millisecond")?this.valueOf()>n.valueOf():n.valueOf()<this.clone().startOf(t).valueOf())},mn.isBefore=function(e,t){var n=_(e)?e:Tt(e);return!(!this.isValid()||!n.isValid())&&("millisecond"===(t=W(t)||"millisecond")?this.valueOf()<n.valueOf():this.clone().endOf(t).valueOf()<n.valueOf())},mn.isBetween=function(e,t,n,r){var i=_(e)?e:Tt(e),a=_(t)?t:Tt(t);return!!(this.isValid()&&i.isValid()&&a.isValid())&&(("("===(r=r||"()")[0]?this.isAfter(i,n):!this.isBefore(i,n))&&(")"===r[1]?this.isBefore(a,n):!this.isAfter(a,n)))},mn.isSame=function(e,t){var n,r=_(e)?e:Tt(e);return!(!this.isValid()||!r.isValid())&&("millisecond"===(t=W(t)||"millisecond")?this.valueOf()===r.valueOf():(n=r.valueOf(),this.clone().startOf(t).valueOf()<=n&&n<=this.clone().endOf(t).valueOf()))},mn.isSameOrAfter=function(e,t){return this.isSame(e,t)||this.isAfter(e,t)},mn.isSameOrBefore=function(e,t){return this.isSame(e,t)||this.isBefore(e,t)},mn.isValid=function(){return g(this)},mn.lang=Jt,mn.locale=Xt,mn.localeData=en,mn.max=Ct,mn.min=Mt,mn.parsingFlags=function(){return f({},p(this))},mn.set=function(e,t){if("object"===typeof e){var n,r=function(e){var t,n=[];for(t in e)o(e,t)&&n.push({unit:t,priority:Y[t]});return n.sort((function(e,t){return e.priority-t.priority})),n}(e=V(e));for(n=0;n<r.length;n++)this[r[n].unit](e[r[n].unit])}else if(C(this[e=W(e)]))return this[e](t);return this},mn.startOf=function(e){var t,n;if(void 0===(e=W(e))||"millisecond"===e||!this.isValid())return this;switch(n=this._isUTC?rn:nn,e){case"year":t=n(this.year(),0,1);break;case"quarter":t=n(this.year(),this.month()-this.month()%3,1);break;case"month":t=n(this.year(),this.month(),1);break;case"week":t=n(this.year(),this.month(),this.date()-this.weekday());break;case"isoWeek":t=n(this.year(),this.month(),this.date()-(this.isoWeekday()-1));break;case"day":case"date":t=n(this.year(),this.month(),this.date());break;case"hour":t=this._d.valueOf(),t-=tn(t+(this._isUTC?0:6e4*this.utcOffset()),36e5);break;case"minute":t=this._d.valueOf(),t-=tn(t,6e4);break;case"second":t=this._d.valueOf(),t-=tn(t,1e3)}return this._d.setTime(t),r.updateOffset(this,!0),this},mn.subtract=$t,mn.toArray=function(){var e=this;return[e.year(),e.month(),e.date(),e.hour(),e.minute(),e.second(),e.millisecond()]},mn.toObject=function(){var e=this;return{years:e.year(),months:e.month(),date:e.date(),hours:e.hours(),minutes:e.minutes(),seconds:e.seconds(),milliseconds:e.milliseconds()}},mn.toDate=function(){return new Date(this.valueOf())},mn.toISOString=function(e){if(!this.isValid())return null;var t=!0!==e,n=t?this.clone().utc():this;return n.year()<0||n.year()>9999?R(n,t?"YYYYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYYYY-MM-DD[T]HH:mm:ss.SSSZ"):C(Date.prototype.toISOString)?t?this.toDate().toISOString():new Date(this.valueOf()+60*this.utcOffset()*1e3).toISOString().replace("Z",R(n,"Z")):R(n,t?"YYYY-MM-DD[T]HH:mm:ss.SSS[Z]":"YYYY-MM-DD[T]HH:mm:ss.SSSZ")},mn.inspect=function(){if(!this.isValid())return"moment.invalid(/* "+this._i+" */)";var e,t,n,r="moment",i="";return this.isLocal()||(r=0===this.utcOffset()?"moment.utc":"moment.parseZone",i="Z"),e="["+r+'("]',t=0<=this.year()&&this.year()<=9999?"YYYY":"YYYYYY",n=i+'[")]',this.format(e+t+"-MM-DD[T]HH:mm:ss.SSS"+n)},"undefined"!==typeof Symbol&&null!=Symbol.for&&(mn[Symbol.for("nodejs.util.inspect.custom")]=function(){return"Moment<"+this.format()+">"}),mn.toJSON=function(){return this.isValid()?this.toISOString():null},mn.toString=function(){return this.clone().locale("en").format("ddd MMM DD YYYY HH:mm:ss [GMT]ZZ")},mn.unix=function(){return Math.floor(this.valueOf()/1e3)},mn.valueOf=function(){return this._d.valueOf()-6e4*(this._offset||0)},mn.creationData=function(){return{input:this._i,format:this._f,locale:this._locale,isUTC:this._isUTC,strict:this._strict}},mn.eraName=function(){var e,t,n,r=this.localeData().eras();for(e=0,t=r.length;e<t;++e){if(n=this.clone().startOf("day").valueOf(),r[e].since<=n&&n<=r[e].until)return r[e].name;if(r[e].until<=n&&n<=r[e].since)return r[e].name}return""},mn.eraNarrow=function(){var e,t,n,r=this.localeData().eras();for(e=0,t=r.length;e<t;++e){if(n=this.clone().startOf("day").valueOf(),r[e].since<=n&&n<=r[e].until)return r[e].narrow;if(r[e].until<=n&&n<=r[e].since)return r[e].narrow}return""},mn.eraAbbr=function(){var e,t,n,r=this.localeData().eras();for(e=0,t=r.length;e<t;++e){if(n=this.clone().startOf("day").valueOf(),r[e].since<=n&&n<=r[e].until)return r[e].abbr;if(r[e].until<=n&&n<=r[e].since)return r[e].abbr}return""},mn.eraYear=function(){var e,t,n,i,a=this.localeData().eras();for(e=0,t=a.length;e<t;++e)if(n=a[e].since<=a[e].until?1:-1,i=this.clone().startOf("day").valueOf(),a[e].since<=i&&i<=a[e].until||a[e].until<=i&&i<=a[e].since)return(this.year()-r(a[e].since).year())*n+a[e].offset;return this.year()},mn.year=Ne,mn.isLeapYear=function(){return H(this.year())},mn.weekYear=function(e){return sn.call(this,e,this.week(),this.weekday(),this.localeData()._week.dow,this.localeData()._week.doy)},mn.isoWeekYear=function(e){return sn.call(this,e,this.isoWeek(),this.isoWeekday(),1,4)},mn.quarter=mn.quarters=function(e){return null==e?Math.ceil((this.month()+1)/3):this.month(3*(e-1)+this.month()%3)},mn.month=De,mn.daysInMonth=function(){return _e(this.year(),this.month())},mn.week=mn.weeks=function(e){var t=this.localeData().week(this);return null==e?t:this.add(7*(e-t),"d")},mn.isoWeek=mn.isoWeeks=function(e){var t=Le(this,1,4).week;return null==e?t:this.add(7*(e-t),"d")},mn.weeksInYear=function(){var e=this.localeData()._week;return ze(this.year(),e.dow,e.doy)},mn.weeksInWeekYear=function(){var e=this.localeData()._week;return ze(this.weekYear(),e.dow,e.doy)},mn.isoWeeksInYear=function(){return ze(this.year(),1,4)},mn.isoWeeksInISOWeekYear=function(){return ze(this.isoWeekYear(),1,4)},mn.date=cn,mn.day=mn.days=function(e){if(!this.isValid())return null!=e?this:NaN;var t=this._isUTC?this._d.getUTCDay():this._d.getDay();return null!=e?(e=function(e,t){return"string"!==typeof e?e:isNaN(e)?"number"===typeof(e=t.weekdaysParse(e))?e:null:parseInt(e,10)}(e,this.localeData()),this.add(e-t,"d")):t},mn.weekday=function(e){if(!this.isValid())return null!=e?this:NaN;var t=(this.day()+7-this.localeData()._week.dow)%7;return null==e?t:this.add(e-t,"d")},mn.isoWeekday=function(e){if(!this.isValid())return null!=e?this:NaN;if(null!=e){var t=function(e,t){return"string"===typeof e?t.weekdaysParse(e)%7||7:isNaN(e)?null:e}(e,this.localeData());return this.day(this.day()%7?t:t-7)}return this.day()||7},mn.dayOfYear=function(e){var t=Math.round((this.clone().startOf("day")-this.clone().startOf("year"))/864e5)+1;return null==e?t:this.add(e-t,"d")},mn.hour=mn.hours=Xe,mn.minute=mn.minutes=dn,mn.second=mn.seconds=pn,mn.millisecond=mn.milliseconds=hn,mn.utcOffset=function(e,t,n){var i,a=this._offset||0;if(!this.isValid())return null!=e?this:NaN;if(null!=e){if("string"===typeof e){if(null===(e=Ft(de,e)))return this}else Math.abs(e)<16&&!n&&(e*=60);return!this._isUTC&&t&&(i=Lt(this)),this._offset=e,this._isUTC=!0,null!=i&&this.add(i,"m"),a!==e&&(!t||this._changeInProgress?Ut(this,Vt(e-a,"m"),1,!1):this._changeInProgress||(this._changeInProgress=!0,r.updateOffset(this,!0),this._changeInProgress=null)),this}return this._isUTC?a:Lt(this)},mn.utc=function(e){return this.utcOffset(0,e)},mn.local=function(e){return this._isUTC&&(this.utcOffset(0,e),this._isUTC=!1,e&&this.subtract(Lt(this),"m")),this},mn.parseZone=function(){if(null!=this._tzm)this.utcOffset(this._tzm,!1,!0);else if("string"===typeof this._i){var e=Ft(ce,this._i);null!=e?this.utcOffset(e):this.utcOffset(0,!0)}return this},mn.hasAlignedHourOffset=function(e){return!!this.isValid()&&(e=e?Tt(e).utcOffset():0,(this.utcOffset()-e)%60===0)},mn.isDST=function(){return this.utcOffset()>this.clone().month(0).utcOffset()||this.utcOffset()>this.clone().month(5).utcOffset()},mn.isLocal=function(){return!!this.isValid()&&!this._isUTC},mn.isUtcOffset=function(){return!!this.isValid()&&this._isUTC},mn.isUtc=zt,mn.isUTC=zt,mn.zoneAbbr=function(){return this._isUTC?"UTC":""},mn.zoneName=function(){return this._isUTC?"Coordinated Universal Time":""},mn.dates=k("dates accessor is deprecated. Use date instead.",cn),mn.months=k("months accessor is deprecated. Use month instead",De),mn.years=k("years accessor is deprecated. Use year instead",Ne),mn.zone=k("moment().zone is deprecated, use moment().utcOffset instead. http://momentjs.com/guides/#/warnings/zone/",(function(e,t){return null!=e?("string"!==typeof e&&(e=-e),this.utcOffset(e,t),this):-this.utcOffset()})),mn.isDSTShifted=k("isDSTShifted is deprecated. See http://momentjs.com/guides/#/warnings/dst-shifted/ for more information",(function(){if(!s(this._isDSTShifted))return this._isDSTShifted;var e,t={};return b(t,this),(t=kt(t))._a?(e=t._isUTC?h(t._a):Tt(t._a),this._isDSTShifted=this.isValid()&&function(e,t,n){var r,i=Math.min(e.length,t.length),a=Math.abs(e.length-t.length),o=0;for(r=0;r<i;r++)(n&&e[r]!==t[r]||!n&&q(e[r])!==q(t[r]))&&o++;return o+a}(t._a,e.toArray())>0):this._isDSTShifted=!1,this._isDSTShifted}));var yn=D.prototype;function bn(e,t,n,r){var i=lt(),a=h().set(r,t);return i[n](a,e)}function xn(e,t,n){if(u(e)&&(t=e,e=void 0),e=e||"",null!=t)return bn(e,t,n,"month");var r,i=[];for(r=0;r<12;r++)i[r]=bn(e,r,n,"month");return i}function _n(e,t,n,r){"boolean"===typeof e?(u(t)&&(n=t,t=void 0),t=t||""):(n=t=e,e=!1,u(t)&&(n=t,t=void 0),t=t||"");var i,a=lt(),o=e?a._week.dow:0,l=[];if(null!=n)return bn(t,(n+o)%7,r,"day");for(i=0;i<7;i++)l[i]=bn(t,(i+o)%7,r,"day");return l}yn.calendar=function(e,t,n){var r=this._calendar[e]||this._calendar.sameElse;return C(r)?r.call(t,n):r},yn.longDateFormat=function(e){var t=this._longDateFormat[e],n=this._longDateFormat[e.toUpperCase()];return t||!n?t:(this._longDateFormat[e]=n.match(O).map((function(e){return"MMMM"===e||"MM"===e||"DD"===e||"dddd"===e?e.slice(1):e})).join(""),this._longDateFormat[e])},yn.invalidDate=function(){return this._invalidDate},yn.ordinal=function(e){return this._ordinal.replace("%d",e)},yn.preparse=vn,yn.postformat=vn,yn.relativeTime=function(e,t,n,r){var i=this._relativeTime[n];return C(i)?i(e,t,n,r):i.replace(/%d/i,e)},yn.pastFuture=function(e,t){var n=this._relativeTime[e>0?"future":"past"];return C(n)?n(t):n.replace(/%s/i,t)},yn.set=function(e){var t,n;for(n in e)o(e,n)&&(C(t=e[n])?this[n]=t:this["_"+n]=t);this._config=e,this._dayOfMonthOrdinalParseLenient=new RegExp((this._dayOfMonthOrdinalParse.source||this._ordinalParse.source)+"|"+/\d{1,2}/.source)},yn.eras=function(e,t){var n,i,a,o=this._eras||lt("en")._eras;for(n=0,i=o.length;n<i;++n){switch(typeof o[n].since){case"string":a=r(o[n].since).startOf("day"),o[n].since=a.valueOf()}switch(typeof o[n].until){case"undefined":o[n].until=1/0;break;case"string":a=r(o[n].until).startOf("day").valueOf(),o[n].until=a.valueOf()}}return o},yn.erasParse=function(e,t,n){var r,i,a,o,l,s=this.eras();for(e=e.toUpperCase(),r=0,i=s.length;r<i;++r)if(a=s[r].name.toUpperCase(),o=s[r].abbr.toUpperCase(),l=s[r].narrow.toUpperCase(),n)switch(t){case"N":case"NN":case"NNN":if(o===e)return s[r];break;case"NNNN":if(a===e)return s[r];break;case"NNNNN":if(l===e)return s[r]}else if([a,o,l].indexOf(e)>=0)return s[r]},yn.erasConvertYear=function(e,t){var n=e.since<=e.until?1:-1;return void 0===t?r(e.since).year():r(e.since).year()+(t-e.offset)*n},yn.erasAbbrRegex=function(e){return o(this,"_erasAbbrRegex")||on.call(this),e?this._erasAbbrRegex:this._erasRegex},yn.erasNameRegex=function(e){return o(this,"_erasNameRegex")||on.call(this),e?this._erasNameRegex:this._erasRegex},yn.erasNarrowRegex=function(e){return o(this,"_erasNarrowRegex")||on.call(this),e?this._erasNarrowRegex:this._erasRegex},yn.months=function(e,t){return e?i(this._months)?this._months[e.month()]:this._months[(this._months.isFormat||Se).test(t)?"format":"standalone"][e.month()]:i(this._months)?this._months:this._months.standalone},yn.monthsShort=function(e,t){return e?i(this._monthsShort)?this._monthsShort[e.month()]:this._monthsShort[Se.test(t)?"format":"standalone"][e.month()]:i(this._monthsShort)?this._monthsShort:this._monthsShort.standalone},yn.monthsParse=function(e,t,n){var r,i,a;if(this._monthsParseExact)return Ce.call(this,e,t,n);for(this._monthsParse||(this._monthsParse=[],this._longMonthsParse=[],this._shortMonthsParse=[]),r=0;r<12;r++){if(i=h([2e3,r]),n&&!this._longMonthsParse[r]&&(this._longMonthsParse[r]=new RegExp("^"+this.months(i,"").replace(".","")+"$","i"),this._shortMonthsParse[r]=new RegExp("^"+this.monthsShort(i,"").replace(".","")+"$","i")),n||this._monthsParse[r]||(a="^"+this.months(i,"")+"|^"+this.monthsShort(i,""),this._monthsParse[r]=new RegExp(a.replace(".",""),"i")),n&&"MMMM"===t&&this._longMonthsParse[r].test(e))return r;if(n&&"MMM"===t&&this._shortMonthsParse[r].test(e))return r;if(!n&&this._monthsParse[r].test(e))return r}},yn.monthsRegex=function(e){return this._monthsParseExact?(o(this,"_monthsRegex")||Ee.call(this),e?this._monthsStrictRegex:this._monthsRegex):(o(this,"_monthsRegex")||(this._monthsRegex=Me),this._monthsStrictRegex&&e?this._monthsStrictRegex:this._monthsRegex)},yn.monthsShortRegex=function(e){return this._monthsParseExact?(o(this,"_monthsRegex")||Ee.call(this),e?this._monthsShortStrictRegex:this._monthsShortRegex):(o(this,"_monthsShortRegex")||(this._monthsShortRegex=Te),this._monthsShortStrictRegex&&e?this._monthsShortStrictRegex:this._monthsShortRegex)},yn.week=function(e){return Le(e,this._week.dow,this._week.doy).week},yn.firstDayOfYear=function(){return this._week.doy},yn.firstDayOfWeek=function(){return this._week.dow},yn.weekdays=function(e,t){var n=i(this._weekdays)?this._weekdays:this._weekdays[e&&!0!==e&&this._weekdays.isFormat.test(t)?"format":"standalone"];return!0===e?je(n,this._week.dow):e?n[e.day()]:n},yn.weekdaysMin=function(e){return!0===e?je(this._weekdaysMin,this._week.dow):e?this._weekdaysMin[e.day()]:this._weekdaysMin},yn.weekdaysShort=function(e){return!0===e?je(this._weekdaysShort,this._week.dow):e?this._weekdaysShort[e.day()]:this._weekdaysShort},yn.weekdaysParse=function(e,t,n){var r,i,a;if(this._weekdaysParseExact)return qe.call(this,e,t,n);for(this._weekdaysParse||(this._weekdaysParse=[],this._minWeekdaysParse=[],this._shortWeekdaysParse=[],this._fullWeekdaysParse=[]),r=0;r<7;r++){if(i=h([2e3,1]).day(r),n&&!this._fullWeekdaysParse[r]&&(this._fullWeekdaysParse[r]=new RegExp("^"+this.weekdays(i,"").replace(".","\\.?")+"$","i"),this._shortWeekdaysParse[r]=new RegExp("^"+this.weekdaysShort(i,"").replace(".","\\.?")+"$","i"),this._minWeekdaysParse[r]=new RegExp("^"+this.weekdaysMin(i,"").replace(".","\\.?")+"$","i")),this._weekdaysParse[r]||(a="^"+this.weekdays(i,"")+"|^"+this.weekdaysShort(i,"")+"|^"+this.weekdaysMin(i,""),this._weekdaysParse[r]=new RegExp(a.replace(".",""),"i")),n&&"dddd"===t&&this._fullWeekdaysParse[r].test(e))return r;if(n&&"ddd"===t&&this._shortWeekdaysParse[r].test(e))return r;if(n&&"dd"===t&&this._minWeekdaysParse[r].test(e))return r;if(!n&&this._weekdaysParse[r].test(e))return r}},yn.weekdaysRegex=function(e){return this._weekdaysParseExact?(o(this,"_weekdaysRegex")||$e.call(this),e?this._weekdaysStrictRegex:this._weekdaysRegex):(o(this,"_weekdaysRegex")||(this._weekdaysRegex=Be),this._weekdaysStrictRegex&&e?this._weekdaysStrictRegex:this._weekdaysRegex)},yn.weekdaysShortRegex=function(e){return this._weekdaysParseExact?(o(this,"_weekdaysRegex")||$e.call(this),e?this._weekdaysShortStrictRegex:this._weekdaysShortRegex):(o(this,"_weekdaysShortRegex")||(this._weekdaysShortRegex=He),this._weekdaysShortStrictRegex&&e?this._weekdaysShortStrictRegex:this._weekdaysShortRegex)},yn.weekdaysMinRegex=function(e){return this._weekdaysParseExact?(o(this,"_weekdaysRegex")||$e.call(this),e?this._weekdaysMinStrictRegex:this._weekdaysMinRegex):(o(this,"_weekdaysMinRegex")||(this._weekdaysMinRegex=Ue),this._weekdaysMinStrictRegex&&e?this._weekdaysMinStrictRegex:this._weekdaysMinRegex)},yn.isPM=function(e){return"p"===(e+"").toLowerCase().charAt(0)},yn.meridiem=function(e,t,n){return e>11?n?"pm":"PM":n?"am":"AM"},at("en",{eras:[{since:"0001-01-01",until:1/0,offset:1,name:"Anno Domini",narrow:"AD",abbr:"AD"},{since:"0000-12-31",until:-1/0,offset:1,name:"Before Christ",narrow:"BC",abbr:"BC"}],dayOfMonthOrdinalParse:/\d{1,2}(th|st|nd|rd)/,ordinal:function(e){var t=e%10;return e+(1===q(e%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th")}}),r.lang=k("moment.lang is deprecated. Use moment.locale instead.",at),r.langData=k("moment.langData is deprecated. Use moment.localeData instead.",lt);var wn=Math.abs;function kn(e,t,n,r){var i=Vt(t,n);return e._milliseconds+=r*i._milliseconds,e._days+=r*i._days,e._months+=r*i._months,e._bubble()}function Sn(e){return e<0?Math.floor(e):Math.ceil(e)}function Tn(e){return 4800*e/146097}function Mn(e){return 146097*e/4800}function Cn(e){return function(){return this.as(e)}}var Pn=Cn("ms"),Dn=Cn("s"),En=Cn("m"),On=Cn("h"),Nn=Cn("d"),In=Cn("w"),An=Cn("M"),Fn=Cn("Q"),Rn=Cn("y");function Ln(e){return function(){return this.isValid()?this._data[e]:NaN}}var zn=Ln("milliseconds"),jn=Ln("seconds"),Wn=Ln("minutes"),Vn=Ln("hours"),Yn=Ln("days"),Bn=Ln("months"),Hn=Ln("years"),Un=Math.round,qn={ss:44,s:45,m:45,h:22,d:26,w:null,M:11};function $n(e,t,n,r,i){return i.relativeTime(t||1,!!n,e,r)}var Gn=Math.abs;function Qn(e){return(e>0)-(e<0)||+e}function Kn(){if(!this.isValid())return this.localeData().invalidDate();var e,t,n,r,i,a,o,l,s=Gn(this._milliseconds)/1e3,u=Gn(this._days),c=Gn(this._months),d=this.asSeconds();return d?(e=U(s/60),t=U(e/60),s%=60,e%=60,n=U(c/12),c%=12,r=s?s.toFixed(3).replace(/\.?0+$/,""):"",i=d<0?"-":"",a=Qn(this._months)!==Qn(d)?"-":"",o=Qn(this._days)!==Qn(d)?"-":"",l=Qn(this._milliseconds)!==Qn(d)?"-":"",i+"P"+(n?a+n+"Y":"")+(c?a+c+"M":"")+(u?o+u+"D":"")+(t||e||s?"T":"")+(t?l+t+"H":"")+(e?l+e+"M":"")+(s?l+r+"S":"")):"P0D"}var Zn=Et.prototype;return Zn.isValid=function(){return this._isValid},Zn.abs=function(){var e=this._data;return this._milliseconds=wn(this._milliseconds),this._days=wn(this._days),this._months=wn(this._months),e.milliseconds=wn(e.milliseconds),e.seconds=wn(e.seconds),e.minutes=wn(e.minutes),e.hours=wn(e.hours),e.months=wn(e.months),e.years=wn(e.years),this},Zn.add=function(e,t){return kn(this,e,t,1)},Zn.subtract=function(e,t){return kn(this,e,t,-1)},Zn.as=function(e){if(!this.isValid())return NaN;var t,n,r=this._milliseconds;if("month"===(e=W(e))||"quarter"===e||"year"===e)switch(t=this._days+r/864e5,n=this._months+Tn(t),e){case"month":return n;case"quarter":return n/3;case"year":return n/12}else switch(t=this._days+Math.round(Mn(this._months)),e){case"week":return t/7+r/6048e5;case"day":return t+r/864e5;case"hour":return 24*t+r/36e5;case"minute":return 1440*t+r/6e4;case"second":return 86400*t+r/1e3;case"millisecond":return Math.floor(864e5*t)+r;default:throw new Error("Unknown unit "+e)}},Zn.asMilliseconds=Pn,Zn.asSeconds=Dn,Zn.asMinutes=En,Zn.asHours=On,Zn.asDays=Nn,Zn.asWeeks=In,Zn.asMonths=An,Zn.asQuarters=Fn,Zn.asYears=Rn,Zn.valueOf=function(){return this.isValid()?this._milliseconds+864e5*this._days+this._months%12*2592e6+31536e6*q(this._months/12):NaN},Zn._bubble=function(){var e,t,n,r,i,a=this._milliseconds,o=this._days,l=this._months,s=this._data;return a>=0&&o>=0&&l>=0||a<=0&&o<=0&&l<=0||(a+=864e5*Sn(Mn(l)+o),o=0,l=0),s.milliseconds=a%1e3,e=U(a/1e3),s.seconds=e%60,t=U(e/60),s.minutes=t%60,n=U(t/60),s.hours=n%24,o+=U(n/24),i=U(Tn(o)),l+=i,o-=Sn(Mn(i)),r=U(l/12),l%=12,s.days=o,s.months=l,s.years=r,this},Zn.clone=function(){return Vt(this)},Zn.get=function(e){return e=W(e),this.isValid()?this[e+"s"]():NaN},Zn.milliseconds=zn,Zn.seconds=jn,Zn.minutes=Wn,Zn.hours=Vn,Zn.days=Yn,Zn.weeks=function(){return U(this.days()/7)},Zn.months=Bn,Zn.years=Hn,Zn.humanize=function(e,t){if(!this.isValid())return this.localeData().invalidDate();var n,r,i=!1,a=qn;return"object"===typeof e&&(t=e,e=!1),"boolean"===typeof e&&(i=e),"object"===typeof t&&(a=Object.assign({},qn,t),null!=t.s&&null==t.ss&&(a.ss=t.s-1)),n=this.localeData(),r=function(e,t,n,r){var i=Vt(e).abs(),a=Un(i.as("s")),o=Un(i.as("m")),l=Un(i.as("h")),s=Un(i.as("d")),u=Un(i.as("M")),c=Un(i.as("w")),d=Un(i.as("y")),f=a<=n.ss&&["s",a]||a<n.s&&["ss",a]||o<=1&&["m"]||o<n.m&&["mm",o]||l<=1&&["h"]||l<n.h&&["hh",l]||s<=1&&["d"]||s<n.d&&["dd",s];return null!=n.w&&(f=f||c<=1&&["w"]||c<n.w&&["ww",c]),(f=f||u<=1&&["M"]||u<n.M&&["MM",u]||d<=1&&["y"]||["yy",d])[2]=t,f[3]=+e>0,f[4]=r,$n.apply(null,f)}(this,!i,a,n),i&&(r=n.pastFuture(+this,r)),n.postformat(r)},Zn.toISOString=Kn,Zn.toString=Kn,Zn.toJSON=Kn,Zn.locale=Xt,Zn.localeData=en,Zn.toIsoString=k("toIsoString() is deprecated. Please use toISOString() instead (notice the capitals)",Kn),Zn.lang=Jt,F("X",0,0,"unix"),F("x",0,0,"valueOf"),he("x",ue),he("X",/[+-]?\d+(\.\d{1,3})?/),ye("X",(function(e,t,n){n._d=new Date(1e3*parseFloat(e))})),ye("x",(function(e,t,n){n._d=new Date(q(e))})),r.version="2.29.1",t=Tt,r.fn=mn,r.min=function(){var e=[].slice.call(arguments,0);return Pt("isBefore",e)},r.max=function(){var e=[].slice.call(arguments,0);return Pt("isAfter",e)},r.now=function(){return Date.now?Date.now():+new Date},r.utc=h,r.unix=function(e){return Tt(1e3*e)},r.months=function(e,t){return xn(e,t,"months")},r.isDate=c,r.locale=at,r.invalid=m,r.duration=Vt,r.isMoment=_,r.weekdays=function(e,t,n){return _n(e,t,n,"weekdays")},r.parseZone=function(){return Tt.apply(null,arguments).parseZone()},r.localeData=lt,r.isDuration=Ot,r.monthsShort=function(e,t){return xn(e,t,"monthsShort")},r.weekdaysMin=function(e,t,n){return _n(e,t,n,"weekdaysMin")},r.defineLocale=ot,r.updateLocale=function(e,t){if(null!=t){var n,r,i=Je;null!=et[e]&&null!=et[e].parentLocale?et[e].set(P(et[e]._config,t)):(null!=(r=it(e))&&(i=r._config),t=P(i,t),null==r&&(t.abbr=e),(n=new D(t)).parentLocale=et[e],et[e]=n),at(e)}else null!=et[e]&&(null!=et[e].parentLocale?(et[e]=et[e].parentLocale,e===at()&&at(e)):null!=et[e]&&delete et[e]);return et[e]},r.locales=function(){return S(et)},r.weekdaysShort=function(e,t,n){return _n(e,t,n,"weekdaysShort")},r.normalizeUnits=W,r.relativeTimeRounding=function(e){return void 0===e?Un:"function"===typeof e&&(Un=e,!0)},r.relativeTimeThreshold=function(e,t){return void 0!==qn[e]&&(void 0===t?qn[e]:(qn[e]=t,"s"===e&&(qn.ss=t-1),!0))},r.calendarFormat=function(e,t){var n=e.diff(t,"days",!0);return n<-6?"sameElse":n<-1?"lastWeek":n<0?"lastDay":n<1?"sameDay":n<2?"nextDay":n<7?"nextWeek":"sameElse"},r.prototype=mn,r.HTML5_FMT={DATETIME_LOCAL:"YYYY-MM-DDTHH:mm",DATETIME_LOCAL_SECONDS:"YYYY-MM-DDTHH:mm:ss",DATETIME_LOCAL_MS:"YYYY-MM-DDTHH:mm:ss.SSS",DATE:"YYYY-MM-DD",TIME:"HH:mm",TIME_SECONDS:"HH:mm:ss",TIME_MS:"HH:mm:ss.SSS",WEEK:"GGGG-[W]WW",MONTH:"YYYY-MM"},r}()}).call(this,n(19)(e))},function(e,t,n){var r=n(20);e.exports=function(e,t){return r(e,t)}},function(e,t,n){var r=n(30),i=n(35),a=n(98),o=n(102),l=n(120),s=n(3),u=n(37),c=n(39),d="[object Object]",f=Object.prototype.hasOwnProperty;e.exports=function(e,t,n,h,p,g){var m=s(e),v=s(t),y=m?"[object Array]":l(e),b=v?"[object Array]":l(t),x=(y="[object Arguments]"==y?d:y)==d,_=(b="[object Arguments]"==b?d:b)==d,w=y==b;if(w&&u(e)){if(!u(t))return!1;m=!0,x=!1}if(w&&!x)return g||(g=new r),m||c(e)?i(e,t,n,h,p,g):a(e,t,y,n,h,p,g);if(!(1&n)){var k=x&&f.call(e,"__wrapped__"),S=_&&f.call(t,"__wrapped__");if(k||S){var T=k?e.value():e,M=S?t.value():t;return g||(g=new r),p(T,M,n,h,g)}}return!!w&&(g||(g=new r),o(e,t,n,h,p,g))}},function(e,t){e.exports=function(){this.__data__=[],this.size=0}},function(e,t,n){var r=n(13),i=Array.prototype.splice;e.exports=function(e){var t=this.__data__,n=r(t,e);return!(n<0)&&(n==t.length-1?t.pop():i.call(t,n,1),--this.size,!0)}},function(e,t,n){var r=n(13);e.exports=function(e){var t=this.__data__,n=r(t,e);return n<0?void 0:t[n][1]}},function(e,t,n){var r=n(13);e.exports=function(e){return r(this.__data__,e)>-1}},function(e,t,n){var r=n(13);e.exports=function(e,t){var n=this.__data__,i=r(n,e);return i<0?(++this.size,n.push([e,t])):n[i][1]=t,this}},function(e,t,n){var r=n(12);e.exports=function(){this.__data__=new r,this.size=0}},function(e,t){e.exports=function(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n}},function(e,t){e.exports=function(e){return this.__data__.get(e)}},function(e,t){e.exports=function(e){return this.__data__.has(e)}},function(e,t,n){var r=n(12),i=n(21),a=n(23);e.exports=function(e,t){var n=this.__data__;if(n instanceof r){var o=n.__data__;if(!i||o.length<199)return o.push([e,t]),this.size=++n.size,this;n=this.__data__=new a(o)}return n.set(e,t),this.size=n.size,this}},function(e,t,n){var r=n(32),i=n(78),a=n(22),o=n(34),l=/^\[object .+?Constructor\]$/,s=Function.prototype,u=Object.prototype,c=s.toString,d=u.hasOwnProperty,f=RegExp("^"+c.call(d).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");e.exports=function(e){return!(!a(e)||i(e))&&(r(e)?f:l).test(o(e))}},function(e,t){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(r){"object"===typeof window&&(n=window)}e.exports=n},function(e,t,n){var r=n(14),i=Object.prototype,a=i.hasOwnProperty,o=i.toString,l=r?r.toStringTag:void 0;e.exports=function(e){var t=a.call(e,l),n=e[l];try{e[l]=void 0;var r=!0}catch(s){}var i=o.call(e);return r&&(t?e[l]=n:delete e[l]),i}},function(e,t){var n=Object.prototype.toString;e.exports=function(e){return n.call(e)}},function(e,t,n){var r=n(79),i=function(){var e=/[^.]+$/.exec(r&&r.keys&&r.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();e.exports=function(e){return!!i&&i in e}},function(e,t,n){var r=n(2)["__core-js_shared__"];e.exports=r},function(e,t){e.exports=function(e,t){return null==e?void 0:e[t]}},function(e,t,n){var r=n(82),i=n(12),a=n(21);e.exports=function(){this.size=0,this.__data__={hash:new r,map:new(a||i),string:new r}}},function(e,t,n){var r=n(83),i=n(84),a=n(85),o=n(86),l=n(87);function s(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}s.prototype.clear=r,s.prototype.delete=i,s.prototype.get=a,s.prototype.has=o,s.prototype.set=l,e.exports=s},function(e,t,n){var r=n(15);e.exports=function(){this.__data__=r?r(null):{},this.size=0}},function(e,t){e.exports=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}},function(e,t,n){var r=n(15),i=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;if(r){var n=t[e];return"__lodash_hash_undefined__"===n?void 0:n}return i.call(t,e)?t[e]:void 0}},function(e,t,n){var r=n(15),i=Object.prototype.hasOwnProperty;e.exports=function(e){var t=this.__data__;return r?void 0!==t[e]:i.call(t,e)}},function(e,t,n){var r=n(15);e.exports=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=r&&void 0===t?"__lodash_hash_undefined__":t,this}},function(e,t,n){var r=n(16);e.exports=function(e){var t=r(this,e).delete(e);return this.size-=t?1:0,t}},function(e,t){e.exports=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e}},function(e,t,n){var r=n(16);e.exports=function(e){return r(this,e).get(e)}},function(e,t,n){var r=n(16);e.exports=function(e){return r(this,e).has(e)}},function(e,t,n){var r=n(16);e.exports=function(e,t){var n=r(this,e),i=n.size;return n.set(e,t),this.size+=n.size==i?0:1,this}},function(e,t,n){var r=n(23),i=n(94),a=n(95);function o(e){var t=-1,n=null==e?0:e.length;for(this.__data__=new r;++t<n;)this.add(e[t])}o.prototype.add=o.prototype.push=i,o.prototype.has=a,e.exports=o},function(e,t){e.exports=function(e){return this.__data__.set(e,"__lodash_hash_undefined__"),this}},function(e,t){e.exports=function(e){return this.__data__.has(e)}},function(e,t){e.exports=function(e,t){for(var n=-1,r=null==e?0:e.length;++n<r;)if(t(e[n],n,e))return!0;return!1}},function(e,t){e.exports=function(e,t){return e.has(t)}},function(e,t,n){var r=n(14),i=n(99),a=n(31),o=n(35),l=n(100),s=n(101),u=r?r.prototype:void 0,c=u?u.valueOf:void 0;e.exports=function(e,t,n,r,u,d,f){switch(n){case"[object DataView]":if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case"[object ArrayBuffer]":return!(e.byteLength!=t.byteLength||!d(new i(e),new i(t)));case"[object Boolean]":case"[object Date]":case"[object Number]":return a(+e,+t);case"[object Error]":return e.name==t.name&&e.message==t.message;case"[object RegExp]":case"[object String]":return e==t+"";case"[object Map]":var h=l;case"[object Set]":var p=1&r;if(h||(h=s),e.size!=t.size&&!p)return!1;var g=f.get(e);if(g)return g==t;r|=2,f.set(e,t);var m=o(h(e),h(t),r,u,d,f);return f.delete(e),m;case"[object Symbol]":if(c)return c.call(e)==c.call(t)}return!1}},function(e,t,n){var r=n(2).Uint8Array;e.exports=r},function(e,t){e.exports=function(e){var t=-1,n=Array(e.size);return e.forEach((function(e,r){n[++t]=[r,e]})),n}},function(e,t){e.exports=function(e){var t=-1,n=Array(e.size);return e.forEach((function(e){n[++t]=e})),n}},function(e,t,n){var r=n(103),i=Object.prototype.hasOwnProperty;e.exports=function(e,t,n,a,o,l){var s=1&n,u=r(e),c=u.length;if(c!=r(t).length&&!s)return!1;for(var d=c;d--;){var f=u[d];if(!(s?f in t:i.call(t,f)))return!1}var h=l.get(e),p=l.get(t);if(h&&p)return h==t&&p==e;var g=!0;l.set(e,t),l.set(t,e);for(var m=s;++d<c;){var v=e[f=u[d]],y=t[f];if(a)var b=s?a(y,v,f,t,e,l):a(v,y,f,e,t,l);if(!(void 0===b?v===y||o(v,y,n,a,l):b)){g=!1;break}m||(m="constructor"==f)}if(g&&!m){var x=e.constructor,_=t.constructor;x==_||!("constructor"in e)||!("constructor"in t)||"function"==typeof x&&x instanceof x&&"function"==typeof _&&_ instanceof _||(g=!1)}return l.delete(e),l.delete(t),g}},function(e,t,n){var r=n(104),i=n(106),a=n(24);e.exports=function(e){return r(e,a,i)}},function(e,t,n){var r=n(105),i=n(3);e.exports=function(e,t,n){var a=t(e);return i(e)?a:r(a,n(e))}},function(e,t){e.exports=function(e,t){for(var n=-1,r=t.length,i=e.length;++n<r;)e[i+n]=t[n];return e}},function(e,t,n){var r=n(107),i=n(108),a=Object.prototype.propertyIsEnumerable,o=Object.getOwnPropertySymbols,l=o?function(e){return null==e?[]:(e=Object(e),r(o(e),(function(t){return a.call(e,t)})))}:i;e.exports=l},function(e,t){e.exports=function(e,t){for(var n=-1,r=null==e?0:e.length,i=0,a=[];++n<r;){var o=e[n];t(o,n,e)&&(a[i++]=o)}return a}},function(e,t){e.exports=function(){return[]}},function(e,t,n){var r=n(110),i=n(36),a=n(3),o=n(37),l=n(38),s=n(39),u=Object.prototype.hasOwnProperty;e.exports=function(e,t){var n=a(e),c=!n&&i(e),d=!n&&!c&&o(e),f=!n&&!c&&!d&&s(e),h=n||c||d||f,p=h?r(e.length,String):[],g=p.length;for(var m in e)!t&&!u.call(e,m)||h&&("length"==m||d&&("offset"==m||"parent"==m)||f&&("buffer"==m||"byteLength"==m||"byteOffset"==m)||l(m,g))||p.push(m);return p}},function(e,t){e.exports=function(e,t){for(var n=-1,r=Array(e);++n<e;)r[n]=t(n);return r}},function(e,t,n){var r=n(10),i=n(11);e.exports=function(e){return i(e)&&"[object Arguments]"==r(e)}},function(e,t){e.exports=function(){return!1}},function(e,t,n){var r=n(10),i=n(25),a=n(11),o={};o["[object Float32Array]"]=o["[object Float64Array]"]=o["[object Int8Array]"]=o["[object Int16Array]"]=o["[object Int32Array]"]=o["[object Uint8Array]"]=o["[object Uint8ClampedArray]"]=o["[object Uint16Array]"]=o["[object Uint32Array]"]=!0,o["[object Arguments]"]=o["[object Array]"]=o["[object ArrayBuffer]"]=o["[object Boolean]"]=o["[object DataView]"]=o["[object Date]"]=o["[object Error]"]=o["[object Function]"]=o["[object Map]"]=o["[object Number]"]=o["[object Object]"]=o["[object RegExp]"]=o["[object Set]"]=o["[object String]"]=o["[object WeakMap]"]=!1,e.exports=function(e){return a(e)&&i(e.length)&&!!o[r(e)]}},function(e,t){e.exports=function(e){return function(t){return e(t)}}},function(e,t,n){(function(e){var r=n(33),i=t&&!t.nodeType&&t,a=i&&"object"==typeof e&&e&&!e.nodeType&&e,o=a&&a.exports===i&&r.process,l=function(){try{var e=a&&a.require&&a.require("util").types;return e||o&&o.binding&&o.binding("util")}catch(t){}}();e.exports=l}).call(this,n(19)(e))},function(e,t,n){var r=n(117),i=n(118),a=Object.prototype.hasOwnProperty;e.exports=function(e){if(!r(e))return i(e);var t=[];for(var n in Object(e))a.call(e,n)&&"constructor"!=n&&t.push(n);return t}},function(e,t){var n=Object.prototype;e.exports=function(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||n)}},function(e,t,n){var r=n(119)(Object.keys,Object);e.exports=r},function(e,t){e.exports=function(e,t){return function(n){return e(t(n))}}},function(e,t,n){var r=n(121),i=n(21),a=n(122),o=n(123),l=n(124),s=n(10),u=n(34),c=u(r),d=u(i),f=u(a),h=u(o),p=u(l),g=s;(r&&"[object DataView]"!=g(new r(new ArrayBuffer(1)))||i&&"[object Map]"!=g(new i)||a&&"[object Promise]"!=g(a.resolve())||o&&"[object Set]"!=g(new o)||l&&"[object WeakMap]"!=g(new l))&&(g=function(e){var t=s(e),n="[object Object]"==t?e.constructor:void 0,r=n?u(n):"";if(r)switch(r){case c:return"[object DataView]";case d:return"[object Map]";case f:return"[object Promise]";case h:return"[object Set]";case p:return"[object WeakMap]"}return t}),e.exports=g},function(e,t,n){var r=n(9)(n(2),"DataView");e.exports=r},function(e,t,n){var r=n(9)(n(2),"Promise");e.exports=r},function(e,t,n){var r=n(9)(n(2),"Set");e.exports=r},function(e,t,n){var r=n(9)(n(2),"WeakMap");e.exports=r},function(e,t,n){var r=n(126),i=n(128)((function(e,t,n){r(e,n,t)}));e.exports=i},function(e,t,n){var r=n(127);e.exports=function(e,t,n){"__proto__"==t&&r?r(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n}},function(e,t,n){var r=n(9),i=function(){try{var e=r(Object,"defineProperty");return e({},"",{}),e}catch(t){}}();e.exports=i},function(e,t,n){var r=n(129),i=n(130),a=n(136),o=n(3);e.exports=function(e,t){return function(n,l){var s=o(n)?r:i,u=t?t():{};return s(n,e,a(l,2),u)}}},function(e,t){e.exports=function(e,t,n,r){for(var i=-1,a=null==e?0:e.length;++i<a;){var o=e[i];t(r,o,n(o),e)}return r}},function(e,t,n){var r=n(131);e.exports=function(e,t,n,i){return r(e,(function(e,r,a){t(i,e,n(e),a)})),i}},function(e,t,n){var r=n(132),i=n(135)(r);e.exports=i},function(e,t,n){var r=n(133),i=n(24);e.exports=function(e,t){return e&&r(e,t,i)}},function(e,t,n){var r=n(134)();e.exports=r},function(e,t){e.exports=function(e){return function(t,n,r){for(var i=-1,a=Object(t),o=r(t),l=o.length;l--;){var s=o[e?l:++i];if(!1===n(a[s],s,a))break}return t}}},function(e,t,n){var r=n(40);e.exports=function(e,t){return function(n,i){if(null==n)return n;if(!r(n))return e(n,i);for(var a=n.length,o=t?a:-1,l=Object(n);(t?o--:++o<a)&&!1!==i(l[o],o,l););return n}}},function(e,t,n){var r=n(137),i=n(140),a=n(151),o=n(3),l=n(152);e.exports=function(e){return"function"==typeof e?e:null==e?a:"object"==typeof e?o(e)?i(e[0],e[1]):r(e):l(e)}},function(e,t,n){var r=n(138),i=n(139),a=n(42);e.exports=function(e){var t=i(e);return 1==t.length&&t[0][2]?a(t[0][0],t[0][1]):function(n){return n===e||r(n,e,t)}}},function(e,t,n){var r=n(30),i=n(20);e.exports=function(e,t,n,a){var o=n.length,l=o,s=!a;if(null==e)return!l;for(e=Object(e);o--;){var u=n[o];if(s&&u[2]?u[1]!==e[u[0]]:!(u[0]in e))return!1}for(;++o<l;){var c=(u=n[o])[0],d=e[c],f=u[1];if(s&&u[2]){if(void 0===d&&!(c in e))return!1}else{var h=new r;if(a)var p=a(d,f,c,e,t,h);if(!(void 0===p?i(f,d,3,a,h):p))return!1}}return!0}},function(e,t,n){var r=n(41),i=n(24);e.exports=function(e){for(var t=i(e),n=t.length;n--;){var a=t[n],o=e[a];t[n]=[a,o,r(o)]}return t}},function(e,t,n){var r=n(20),i=n(141),a=n(148),o=n(26),l=n(41),s=n(42),u=n(17);e.exports=function(e,t){return o(e)&&l(t)?s(u(e),t):function(n){var o=i(n,e);return void 0===o&&o===t?a(n,e):r(t,o,3)}}},function(e,t,n){var r=n(43);e.exports=function(e,t,n){var i=null==e?void 0:r(e,t);return void 0===i?n:i}},function(e,t,n){var r=n(143),i=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,a=/\\(\\)?/g,o=r((function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(i,(function(e,n,r,i){t.push(r?i.replace(a,"$1"):n||e)})),t}));e.exports=o},function(e,t,n){var r=n(144);e.exports=function(e){var t=r(e,(function(e){return 500===n.size&&n.clear(),e})),n=t.cache;return t}},function(e,t,n){var r=n(23);function i(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw new TypeError("Expected a function");var n=function n(){var r=arguments,i=t?t.apply(this,r):r[0],a=n.cache;if(a.has(i))return a.get(i);var o=e.apply(this,r);return n.cache=a.set(i,o)||a,o};return n.cache=new(i.Cache||r),n}i.Cache=r,e.exports=i},function(e,t,n){var r=n(146);e.exports=function(e){return null==e?"":r(e)}},function(e,t,n){var r=n(14),i=n(147),a=n(3),o=n(27),l=r?r.prototype:void 0,s=l?l.toString:void 0;e.exports=function e(t){if("string"==typeof t)return t;if(a(t))return i(t,e)+"";if(o(t))return s?s.call(t):"";var n=t+"";return"0"==n&&1/t==-1/0?"-0":n}},function(e,t){e.exports=function(e,t){for(var n=-1,r=null==e?0:e.length,i=Array(r);++n<r;)i[n]=t(e[n],n,e);return i}},function(e,t,n){var r=n(149),i=n(150);e.exports=function(e,t){return null!=e&&i(e,t,r)}},function(e,t){e.exports=function(e,t){return null!=e&&t in Object(e)}},function(e,t,n){var r=n(44),i=n(36),a=n(3),o=n(38),l=n(25),s=n(17);e.exports=function(e,t,n){for(var u=-1,c=(t=r(t,e)).length,d=!1;++u<c;){var f=s(t[u]);if(!(d=null!=e&&n(e,f)))break;e=e[f]}return d||++u!=c?d:!!(c=null==e?0:e.length)&&l(c)&&o(f,c)&&(a(e)||i(e))}},function(e,t){e.exports=function(e){return e}},function(e,t,n){var r=n(153),i=n(154),a=n(26),o=n(17);e.exports=function(e){return a(e)?r(o(e)):i(e)}},function(e,t){e.exports=function(e){return function(t){return null==t?void 0:t[e]}}},function(e,t,n){var r=n(43);e.exports=function(e){return function(t){return r(t,e)}}}]]);
//# sourceMappingURL=2.c6c4604e.chunk.js.map