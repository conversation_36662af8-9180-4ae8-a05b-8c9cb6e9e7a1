import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix, roc_curve, auc
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import torchaudio
import torchvision.models as models
from torch.nn import functional as F

# Thêm các thư viện cần thiết cho XAI
from captum.attr import IntegratedGradients, GradientShap, DeepLift, LayerGradCam
from captum.attr import visualization as viz
import matplotlib.pyplot as plt
import seaborn as sns
import os

# Set the path to your dataset
DATASET_PATH = "D:/AI_Thao/Project/PSG-audio/dataset/PSG-AUDIO/APNEA_EDF"
# Backup paths in case the primary path doesn't exist
BACKUP_PATHS = [
    "D:/AI_Thao/Project/PSG-audio/PSG-AUDIO/APNEA_EDF",
    "D:/AI_Thao/Project/dataset/PSG-AUDIO/APNEA_EDF",
    "dataset/PSG-AUDIO/APNEA_EDF",
    "PSG-AUDIO/APNEA_EDF",
    "APNEA_EDF"
]

def find_dataset_path():
    """Find the correct path to the dataset."""
    if os.path.exists(DATASET_PATH):
        return DATASET_PATH
    
    for path in BACKUP_PATHS:
        if os.path.exists(path):
            print(f"Using dataset path: {path}")
            return path
    
    print("Could not find dataset path. Please check the directory structure.")
    return None

def load_npy_files(dataset_path):
    """Load .npy files and their labels from the dataset directory."""
    if dataset_path is None:
        return [], []
    
    data_files = []
    labels = []
    
    # Iterate through patient directories
    for patient_dir in os.listdir(dataset_path):
        patient_path = os.path.join(dataset_path, patient_dir)
        if not os.path.isdir(patient_path):
            continue
        
        # Look for apnea and non-apnea files
        for filename in os.listdir(patient_path):
            file_path = os.path.join(patient_path, filename)
            if filename.endswith('_ap.npy'):
                data_files.append(file_path)
                labels.append(1)  # 1 for apnea
            elif filename.endswith('_nap.npy'):
                data_files.append(file_path)
                labels.append(0)  # 0 for normal
    
    print(f"Loaded {len(data_files)} files: {len([l for l in labels if l == 1])} apnea, {len([l for l in labels if l == 0])} normal")
    return data_files, labels

class ApneaDataset(Dataset):
    """PyTorch Dataset for Apnea data using .npy files."""
    def __init__(self, data_files, labels):
        self.data_files = data_files
        self.labels = labels
        # Định nghĩa mel spectrogram transform
        self.mel_transform = torchaudio.transforms.MelSpectrogram(
            sample_rate=16000,
            n_fft=1024,
            hop_length=512,
            n_mels=128,
            f_min=0,
            f_max=8000,
            power=2.0
        )
        
    def __len__(self):
        return len(self.labels)
    
    def __getitem__(self, idx):
        file_path = self.data_files[idx]
        label = self.labels[idx]
        
        # Load .npy file
        data = np.load(file_path)
        
        # Xử lý dữ liệu 1D (dạng tín hiệu âm thanh)
        if len(data.shape) == 1:
            # Giới hạn độ dài tín hiệu
            seq_length = min(data.shape[0], 16000*5)  # Giới hạn 5 giây ở 16kHz
            data = data[:seq_length]
            
            # Pad nếu tín hiệu quá ngắn
            if data.shape[0] < seq_length:
                data = np.pad(data, (0, seq_length - data.shape[0]), 'constant')
            
            # Chuyển đổi sang tensor
            waveform = torch.FloatTensor(data).unsqueeze(0)  # Thêm chiều kênh (1, seq_length)
            
            # Tính mel spectrogram
            mel_spec = self.mel_transform(waveform)  # (1, n_mels, time)
            
            # Chuyển đổi sang dB scale
            mel_spec = torchaudio.transforms.AmplitudeToDB()(mel_spec)
            
            # Đảm bảo kích thước phù hợp (cắt hoặc pad)
            if mel_spec.shape[2] > 128:
                mel_spec = mel_spec[:, :, :128]
            elif mel_spec.shape[2] < 128:
                pad_size = 128 - mel_spec.shape[2]
                mel_spec = F.pad(mel_spec, (0, pad_size))
            
            # Chuẩn hóa
            mel_spec = (mel_spec - mel_spec.min()) / (mel_spec.max() - mel_spec.min() + 1e-8)
            
            # Chuyển đổi thành 3 kênh cho ConvNeXt
            mel_spec = mel_spec.repeat(3, 1, 1)  # (3, n_mels, time)
            
        # Xử lý dữ liệu 2D (đã là spectrogram)
        else:
            # Đảm bảo kích thước phù hợp
            if data.shape[0] > 128 or data.shape[1] > 128:
                # Lấy mẫu đơn giản
                data = data[::max(1, data.shape[0]//128), ::max(1, data.shape[1]//128)]
                data = data[:128, :128]
            
            # Pad nếu quá nhỏ
            if data.shape[0] < 128 or data.shape[1] < 128:
                padded = np.zeros((128, 128))
                padded[:data.shape[0], :data.shape[1]] = data
                data = padded
            
            # Chuẩn hóa
            data = (data - np.min(data)) / (np.max(data) - np.min(data) + 1e-8)
            
            # Chuyển đổi sang tensor và thêm 3 kênh
            mel_spec = torch.FloatTensor(data).unsqueeze(0).repeat(3, 1, 1)  # (3, 128, 128)
        
        return mel_spec, torch.FloatTensor([label])

# Add DropPath class that was missing
class DropPath(nn.Module):
    """Drop paths (Stochastic Depth) per sample."""
    def __init__(self, drop_prob=0.):
        super(DropPath, self).__init__()
        self.drop_prob = drop_prob

    def forward(self, x):
        if self.drop_prob == 0. or not self.training:
            return x
        keep_prob = 1 - self.drop_prob
        # Work with different dimensions for convolutional and linear layers
        shape = (x.shape[0],) + (1,) * (x.ndim - 1)
        random_tensor = keep_prob + torch.rand(shape, dtype=x.dtype, device=x.device)
        random_tensor.floor_()  # binarize
        output = x.div(keep_prob) * random_tensor
        return output

# ConvNeXt Block implementation
class LayerNorm(nn.Module):
    def __init__(self, normalized_shape, eps=1e-6):
        super().__init__()
        self.weight = nn.Parameter(torch.ones(normalized_shape))
        self.bias = nn.Parameter(torch.zeros(normalized_shape))
        self.eps = eps
    
    def forward(self, x):
        # x có shape (N, H, W, C)
        mean = x.mean(dim=-1, keepdim=True)
        var = x.var(dim=-1, unbiased=False, keepdim=True)
        x = (x - mean) / torch.sqrt(var + self.eps)
        x = self.weight * x + self.bias
        return x

class ConvNeXtBlock(nn.Module):
    def __init__(self, dim, drop_path=0., layer_scale_init_value=1e-6):
        super().__init__()
        self.dwconv = nn.Conv2d(dim, dim, kernel_size=7, padding=3, groups=dim)
        # Sử dụng BatchNorm2d thay vì LayerNorm tùy chỉnh
        self.norm = nn.BatchNorm2d(dim)
        # Thay đổi cấu trúc để tránh chuyển đổi permute
        self.pwconv1 = nn.Conv2d(dim, 4 * dim, kernel_size=1)
        self.act = nn.GELU()
        self.pwconv2 = nn.Conv2d(4 * dim, dim, kernel_size=1)
        self.gamma = nn.Parameter(layer_scale_init_value * torch.ones((dim, 1, 1)), 
                                  requires_grad=True) if layer_scale_init_value > 0 else None
        self.drop_path = DropPath(drop_path) if drop_path > 0. else nn.Identity()

    def forward(self, x):
        input = x
        x = self.dwconv(x)
        x = self.norm(x)
        x = self.pwconv1(x)
        x = self.act(x)
        x = self.pwconv2(x)
        if self.gamma is not None:
            x = self.gamma * x
        x = input + self.drop_path(x)
        return x

# Define ConvNeXtLSTM model
class ConvNeXtLSTM(nn.Module):
    def __init__(self, num_classes=1):
        super(ConvNeXtLSTM, self).__init__()
        
        # Sử dụng BatchNorm thay vì LayerNorm để tránh vấn đề kích thước
        self.convnext_layers = nn.Sequential(
            nn.Conv2d(3, 64, kernel_size=4, stride=4),
            nn.BatchNorm2d(64),  # Thay thế LayerNorm bằng BatchNorm2d
            nn.GELU(),
            ConvNeXtBlock(64),
            nn.MaxPool2d(2, 2),
            ConvNeXtBlock(64),
            nn.MaxPool2d(2, 2),
            ConvNeXtBlock(64),
            nn.AdaptiveAvgPool2d((4, 4))
        )
        
        # LSTM layer
        self.lstm = nn.LSTM(
            input_size=64 * 4 * 4,  # Flattened feature map
            hidden_size=128,
            num_layers=1,
            batch_first=True
        )
        
        # Fully connected layer
        self.fc = nn.Sequential(
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(64, num_classes),
            nn.Sigmoid()
        )
        
    def forward(self, x):
        # ConvNeXt feature extraction
        batch_size = x.size(0)
        x = self.convnext_layers(x)
        
        # Reshape for LSTM
        x = x.view(batch_size, 1, -1)  # (batch, seq_len=1, features)
        
        # LSTM
        x, _ = self.lstm(x)
        x = x[:, -1, :]  # Take the output of the last time step
        
        # Classification
        x = self.fc(x)
        return x

def prepare_dataset():
    """Prepare dataset for training."""
    dataset_path = find_dataset_path()
    data_files, labels = load_npy_files(dataset_path)
    
    if len(data_files) == 0:
        raise ValueError("No data files found. Please check the dataset path.")
    
    # Split into training and testing sets
    train_files, test_files, train_labels, test_labels = train_test_split(
        data_files, labels, test_size=0.2, random_state=42, stratify=labels
    )
    
    # Create datasets
    train_dataset = ApneaDataset(train_files, train_labels)
    test_dataset = ApneaDataset(test_files, test_labels)
    
    return train_dataset, test_dataset

def train_and_evaluate():
    """Train and evaluate the model."""
    # Set device
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")
    
    # Prepare data
    try:
        train_dataset, test_dataset = prepare_dataset()
    except Exception as e:
        print(f"Error preparing dataset: {e}")
        return None, None
    
    # Create dataloaders
    train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=32, shuffle=False)
    
    # Initialize model
    model = ConvNeXtLSTM().to(device)
    
    # Define loss function and optimizer
    criterion = nn.BCELoss()
    optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=1e-4)
    
    # Learning rate scheduler
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.5, patience=5, verbose=True, min_lr=1e-6
    )
    
    # Early stopping
    best_val_loss = float('inf')
    patience = 10
    patience_counter = 0
    best_model_state = None
    
    # Training loop
    num_epochs = 50  # Tăng số epoch lên cao hơn
    train_losses = []
    val_losses = []
    train_accuracies = []
    val_accuracies = []
    
    for epoch in range(num_epochs):
        # Training phase
        model.train()
        running_loss = 0.0
        correct = 0
        total = 0
        
        for inputs, labels in train_loader:
            inputs, labels = inputs.to(device), labels.to(device)
            
            # Zero the parameter gradients
            optimizer.zero_grad()
            
            # Forward pass
            outputs = model(inputs)
            loss = criterion(outputs, labels)
            
            # Backward pass and optimize
            loss.backward()
            # Gradient clipping để tránh exploding gradients
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            running_loss += loss.item() * inputs.size(0)
            
            # Calculate accuracy
            predicted = (outputs > 0.5).float()
            total += labels.size(0)
            correct += (predicted == labels).sum().item()
        
        epoch_loss = running_loss / len(train_loader.dataset)
        epoch_acc = 100 * correct / total
        train_losses.append(epoch_loss)
        train_accuracies.append(epoch_acc)
        
        # Validation phase
        model.eval()
        val_loss = 0.0
        correct = 0
        total = 0
        
        with torch.no_grad():
            for inputs, labels in test_loader:
                inputs, labels = inputs.to(device), labels.to(device)
                
                outputs = model(inputs)
                loss = criterion(outputs, labels)
                
                val_loss += loss.item() * inputs.size(0)
                
                predicted = (outputs > 0.5).float()
                total += labels.size(0)
                correct += (predicted == labels).sum().item()
        
        epoch_val_loss = val_loss / len(test_loader.dataset)
        epoch_val_acc = 100 * correct / total
        val_losses.append(epoch_val_loss)
        val_accuracies.append(epoch_val_acc)
        
        # Update learning rate
        scheduler.step(epoch_val_loss)
        
        print(f'Epoch {epoch+1}/{num_epochs}, '
              f'Train Loss: {epoch_loss:.4f}, '
              f'Val Loss: {epoch_val_loss:.4f}, '
              f'Train Acc: {epoch_acc:.2f}%, '
              f'Val Acc: {epoch_val_acc:.2f}%')
        
        # Early stopping
        if epoch_val_loss < best_val_loss:
            best_val_loss = epoch_val_loss
            patience_counter = 0
            best_model_state = model.state_dict().copy()
            print(f"New best model saved with validation loss: {best_val_loss:.4f}")
        else:
            patience_counter += 1
            print(f"Early stopping counter: {patience_counter}/{patience}")
            if patience_counter >= patience:
                print("Early stopping triggered!")
                break
    
    # Load best model
    if best_model_state is not None:
        model.load_state_dict(best_model_state)
        print(f"Loaded best model with validation loss: {best_val_loss:.4f}")
    
    # Evaluate on test set
    model.eval()
    all_preds = []
    all_labels = []
    all_probs = []
    
    with torch.no_grad():
        for inputs, labels in test_loader:
            inputs, labels = inputs.to(device), labels.to(device)
            
            outputs = model(inputs)
            probs = outputs.cpu().numpy()
            predicted = (outputs > 0.5).float()
            
            all_probs.extend(probs)
            all_preds.extend(predicted.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())
    
    # Print classification report
    print("\nClassification Report:")
    print(classification_report(all_labels, all_preds))
    
    # Plot confusion matrix
    cm = confusion_matrix(all_labels, all_preds)
    plt.figure(figsize=(15, 10))
    
    plt.subplot(2, 2, 1)
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues')
    plt.title('Confusion Matrix')
    plt.xlabel('Predicted')
    plt.ylabel('True')
    
    # Plot ROC curve
    plt.subplot(2, 2, 2)
    fpr, tpr, _ = roc_curve(all_labels, all_probs)
    roc_auc = auc(fpr, tpr)
    plt.plot(fpr, tpr, label=f'ROC curve (area = {roc_auc:.2f})')
    plt.plot([0, 1], [0, 1], 'k--')
    plt.xlabel('False Positive Rate')
    plt.ylabel('True Positive Rate')
    plt.title('ROC Curve')
    plt.legend(loc='lower right')
    
    # Plot training and validation loss
    plt.subplot(2, 2, 3)
    plt.plot(train_losses, label='Training Loss')
    plt.plot(val_losses, label='Validation Loss')
    plt.title('Loss Curves')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    
    # Plot training and validation accuracy
    plt.subplot(2, 2, 4)
    plt.plot(train_accuracies, label='Training Accuracy')
    plt.plot(val_accuracies, label='Validation Accuracy')
    plt.title('Accuracy Curves')
    plt.xlabel('Epoch')
    plt.ylabel('Accuracy (%)')
    plt.legend()
    
    plt.tight_layout()
    plt.savefig('apnea_training_results.png', dpi=300)
    print("Results saved to apnea_training_results.png")
    
    # Save the model
    torch.save(model.state_dict(), "apnea_model_convnext_lstm.pth")
    print("Model saved to apnea_model_convnext_lstm.pth")
    
    return model, (train_losses, val_losses, train_accuracies, val_accuracies)

# Thêm các hàm XAI
def apply_xai_methods(model, input_tensor, target=None, layer_name=None):
    """
    Áp dụng các phương pháp XAI khác nhau để giải thích dự đoán của mô hình ConvNeXt-LSTM.
    
    Args:
        model: Mô hình ConvNeXtLSTM đã huấn luyện
        input_tensor: Tensor đầu vào cần giải thích
        target: Nhãn mục tiêu (mặc định là None, sẽ sử dụng dự đoán của mô hình)
        layer_name: Tên lớp để áp dụng Grad-CAM (mặc định là None)
        
    Returns:
        Dictionary chứa kết quả của các phương pháp XAI khác nhau
    """
    model.eval()
    input_tensor.requires_grad = True
    
    # Đảm bảo input_tensor có batch dimension
    if len(input_tensor.shape) == 3:
        input_tensor = input_tensor.unsqueeze(0)
    
    # Lấy dự đoán
    with torch.no_grad():
        output = model(input_tensor)
        pred_score = output.item()
        pred_class = 1 if pred_score > 0.5 else 0
    
    if target is None:
        target = pred_class
    
    # Khởi tạo các phương pháp XAI
    integrated_gradients = IntegratedGradients(model)
    gradient_shap = GradientShap(model)
    deep_lift = DeepLift(model)
    
    # Tạo baseline (zero tensor)
    baseline = torch.zeros_like(input_tensor)
    
    # Tính toán attribution với Integrated Gradients
    ig_attr = integrated_gradients.attribute(input_tensor, baseline, target=target)
    
    # Tính toán attribution với GradientSHAP
    gs_attr = gradient_shap.attribute(input_tensor, baseline, target=target, n_samples=50)
    
    # Tính toán attribution với DeepLift
    dl_attr = deep_lift.attribute(input_tensor, baseline, target=target)
    
    # Tính toán Grad-CAM nếu layer_name được cung cấp
    gradcam_attr = None
    if layer_name is not None:
        # Lấy lớp từ mô hình dựa trên tên
        if layer_name == "convnext":
            target_layer = model.convnext_layers[-3]  # Lấy lớp ConvNeXtBlock cuối cùng
        elif layer_name == "lstm":
            target_layer = model.lstm
        else:
            # Mặc định sử dụng lớp ConvNeXtBlock đầu tiên
            target_layer = model.convnext_layers[3]  # ConvNeXtBlock đầu tiên
        
        layer_gc = LayerGradCam(model, target_layer)
        gradcam_attr = layer_gc.attribute(input_tensor, target=target)
    
    # Trả về dictionary chứa tất cả các attribution
    return {
        "input": input_tensor.detach().cpu().numpy(),
        "integrated_gradients": ig_attr.detach().cpu().numpy(),
        "gradient_shap": gs_attr.detach().cpu().numpy(),
        "deep_lift": dl_attr.detach().cpu().numpy(),
        "grad_cam": gradcam_attr.detach().cpu().numpy() if gradcam_attr is not None else None,
        "prediction": pred_score,
        "pred_class": "Apnea" if pred_class == 1 else "Normal",
        "target": target
    }

def visualize_xai_results(xai_results, save_dir="xai_results", filename="xai_visualization"):
    """
    Hiển thị kết quả từ các phương pháp XAI khác nhau.
    
    Args:
        xai_results: Dictionary chứa kết quả từ các phương pháp XAI
        save_dir: Thư mục để lưu kết quả
        filename: Tên file để lưu
    """
    os.makedirs(save_dir, exist_ok=True)
    
    # Lấy dữ liệu từ kết quả
    input_data = xai_results["input"]
    ig_attr = xai_results["integrated_gradients"]
    gs_attr = xai_results["gradient_shap"]
    dl_attr = xai_results["deep_lift"]
    gradcam = xai_results["grad_cam"]
    pred_class = xai_results["pred_class"]
    pred_score = xai_results["prediction"]
    
    # Squeeze để loại bỏ batch dimension
    input_data = input_data.squeeze()
    ig_attr = ig_attr.squeeze()
    gs_attr = gs_attr.squeeze()
    dl_attr = dl_attr.squeeze()
    
    # Tính trung bình trên kênh (nếu có nhiều kênh)
    if len(input_data.shape) > 2:
        input_data_viz = input_data.mean(axis=0)
        ig_attr_viz = np.abs(ig_attr).mean(axis=0)
        gs_attr_viz = np.abs(gs_attr).mean(axis=0)
        dl_attr_viz = np.abs(dl_attr).mean(axis=0)
    else:
        input_data_viz = input_data
        ig_attr_viz = np.abs(ig_attr)
        gs_attr_viz = np.abs(gs_attr)
        dl_attr_viz = np.abs(dl_attr)
    
    # Chuẩn hóa để hiển thị
    def normalize_for_viz(attr):
        attr_min, attr_max = attr.min(), attr.max()
        if attr_max > attr_min:
            return (attr - attr_min) / (attr_max - attr_min)
        return attr
    
    ig_attr_viz = normalize_for_viz(ig_attr_viz)
    gs_attr_viz = normalize_for_viz(gs_attr_viz)
    dl_attr_viz = normalize_for_viz(dl_attr_viz)
    
    # Tạo hình ảnh
    plt.figure(figsize=(20, 15))
    
    # Hiển thị dữ liệu đầu vào
    plt.subplot(3, 2, 1)
    plt.imshow(input_data_viz, cmap='viridis', aspect='auto')
    plt.title(f'Input Mel Spectrogram\nPrediction: {pred_class} ({pred_score:.4f})')
    plt.colorbar(fraction=0.046, pad=0.04)
    
    # Hiển thị Integrated Gradients
    plt.subplot(3, 2, 2)
    plt.imshow(ig_attr_viz, cmap='hot', aspect='auto')
    plt.title('Integrated Gradients Attribution')
    plt.colorbar(fraction=0.046, pad=0.04)
    
    # Hiển thị Gradient SHAP
    plt.subplot(3, 2, 3)
    plt.imshow(gs_attr_viz, cmap='hot', aspect='auto')
    plt.title('Gradient SHAP Attribution')
    plt.colorbar(fraction=0.046, pad=0.04)
    
    # Hiển thị DeepLift
    plt.subplot(3, 2, 4)
    plt.imshow(dl_attr_viz, cmap='hot', aspect='auto')
    plt.title('DeepLift Attribution')
    plt.colorbar(fraction=0.046, pad=0.04)
    
    # Hiển thị Grad-CAM nếu có
    if gradcam is not None:
        gradcam = gradcam.squeeze()
        if len(gradcam.shape) > 2:
            gradcam = gradcam.mean(axis=0)
        
        gradcam = normalize_for_viz(gradcam)
        
        plt.subplot(3, 2, 5)
        plt.imshow(gradcam, cmap='jet', aspect='auto')
        plt.title('Grad-CAM Heatmap')
        plt.colorbar(fraction=0.046, pad=0.04)
        
        # Hiển thị Grad-CAM overlay
        plt.subplot(3, 2, 6)
        plt.imshow(input_data_viz, cmap='gray', aspect='auto')
        plt.imshow(gradcam, cmap='jet', alpha=0.6, aspect='auto')
        plt.title('Grad-CAM Overlay')
        plt.colorbar(fraction=0.046, pad=0.04)
    
    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, f"{filename}.png"), dpi=300)
    plt.close()
    
    print(f"XAI visualization saved to {os.path.join(save_dir, filename)}.png")

def generate_feature_importance(model, dataset, num_samples=10, save_dir="xai_results"):
    """
    Tạo biểu đồ tầm quan trọng của đặc trưng dựa trên nhiều mẫu.
    
    Args:
        model: Mô hình đã huấn luyện
        dataset: Dataset chứa các mẫu
        num_samples: Số lượng mẫu để phân tích
        save_dir: Thư mục để lưu kết quả
    """
    os.makedirs(save_dir, exist_ok=True)
    device = next(model.parameters()).device
    
    # Chọn ngẫu nhiên các mẫu từ dataset
    indices = np.random.choice(len(dataset), min(num_samples, len(dataset)), replace=False)
    
    # Khởi tạo mảng để lưu trữ tầm quan trọng của đặc trưng
    feature_importance = []
    
    # Lặp qua các mẫu đã chọn
    for idx in indices:
        input_tensor, label = dataset[idx]
        input_tensor = input_tensor.unsqueeze(0).to(device)
        label = int(label.item())
        
        # Áp dụng Integrated Gradients
        xai_results = apply_xai_methods(model, input_tensor, target=label)
        
        # Lấy attribution và tính trung bình tuyệt đối
        attr = np.abs(xai_results["integrated_gradients"].squeeze())
        if len(attr.shape) > 2:
            attr = attr.mean(axis=0)
        
        # Tính trung bình theo thời gian để có tầm quan trọng của tần số
        freq_importance = attr.mean(axis=1)
        
        # Tính trung bình theo tần số để có tầm quan trọng của thời gian
        time_importance = attr.mean(axis=0)
        
        feature_importance.append({
            "freq_importance": freq_importance,
            "time_importance": time_importance,
            "label": label
        })
    
    # Tạo biểu đồ tầm quan trọng của tần số
    plt.figure(figsize=(12, 10))
    
    # Tách tầm quan trọng theo nhãn
    normal_freq_imp = np.array([x["freq_importance"] for x in feature_importance if x["label"] == 0])
    apnea_freq_imp = np.array([x["freq_importance"] for x in feature_importance if x["label"] == 1])
    
    # Tính trung bình và độ lệch chuẩn
    if len(normal_freq_imp) > 0:
        normal_freq_mean = normal_freq_imp.mean(axis=0)
        normal_freq_std = normal_freq_imp.std(axis=0)
        
        plt.subplot(2, 1, 1)
        plt.plot(normal_freq_mean, label='Normal', color='blue')
        plt.fill_between(
            range(len(normal_freq_mean)),
            normal_freq_mean - normal_freq_std,
            normal_freq_mean + normal_freq_std,
            alpha=0.3,
            color='blue'
        )
    
    if len(apnea_freq_imp) > 0:
        apnea_freq_mean = apnea_freq_imp.mean(axis=0)
        apnea_freq_std = apnea_freq_imp.std(axis=0)
        
        plt.subplot(2, 1, 1)
        plt.plot(apnea_freq_mean, label='Apnea', color='red')
        plt.fill_between(
            range(len(apnea_freq_mean)),
            apnea_freq_mean - apnea_freq_std,
            apnea_freq_mean + apnea_freq_std,
            alpha=0.3,
            color='red'
        )
    
    plt.title('Frequency Importance')
    plt.xlabel('Mel Frequency Bin')
    plt.ylabel('Importance')
    plt.legend()
    
    # Tạo biểu đồ tầm quan trọng của thời gian
    normal_time_imp = np.array([x["time_importance"] for x in feature_importance if x["label"] == 0])
    apnea_time_imp = np.array([x["time_importance"] for x in feature_importance if x["label"] == 1])
    
    if len(normal_time_imp) > 0:
        normal_time_mean = normal_time_imp.mean(axis=0)
        normal_time_std = normal_time_imp.std(axis=0)
        
        plt.subplot(2, 1, 2)
        plt.plot(normal_time_mean, label='Normal', color='blue')
        plt.fill_between(
            range(len(normal_time_mean)),
            normal_time_mean - normal_time_std,
            normal_time_mean + normal_time_std,
            alpha=0.3,
            color='blue'
        )
    
    if len(apnea_time_imp) > 0:
        apnea_time_mean = apnea_time_imp.mean(axis=0)
        apnea_time_std = apnea_time_imp.std(axis=0)
        
        plt.subplot(2, 1, 2)
        plt.plot(apnea_time_mean, label='Apnea', color='red')
        plt.fill_between(
            range(len(apnea_time_mean)),
            apnea_time_mean - apnea_time_std,
            apnea_time_mean + apnea_time_std,
            alpha=0.3,
            color='red'
        )
    
    plt.title('Time Importance')
    plt.xlabel('Time Frame')
    plt.ylabel('Importance')
    plt.legend()
    
    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, "feature_importance.png"), dpi=300)
    plt.close()
    
    print(f"Feature importance visualization saved to {os.path.join(save_dir, 'feature_importance.png')}")

def analyze_sample_with_xai(model, file_path, save_dir="xai_results"):
    """
    Phân tích một mẫu cụ thể bằng các phương pháp XAI.
    
    Args:
        model: Mô hình ConvNeXtLSTM đã huấn luyện
        file_path: Đường dẫn đến file cần phân tích
        save_dir: Thư mục để lưu kết quả
    """
    os.makedirs(save_dir, exist_ok=True)
    device = next(model.parameters()).device
    
    # Load data
    data = np.load(file_path)
    
    # Xử lý dữ liệu 1D (dạng tín hiệu âm thanh)
    if len(data.shape) == 1:
        # Giới hạn độ dài tín hiệu
        seq_length = min(data.shape[0], 16000*5)  # Giới hạn 5 giây ở 16kHz
        data = data[:seq_length]
        
        # Pad nếu tín hiệu quá ngắn
        if data.shape[0] < seq_length:
            data = np.pad(data, (0, seq_length - data.shape[0]), 'constant')
        
        # Chuyển đổi sang tensor
        waveform = torch.FloatTensor(data).unsqueeze(0)  # Thêm chiều kênh (1, seq_length)
        
        # Tính mel spectrogram
        mel_transform = torchaudio.transforms.MelSpectrogram(
            sample_rate=16000,
            n_fft=1024,
            hop_length=512,
            n_mels=128,
            f_min=0,
            f_max=8000,
            power=2.0
        )
        mel_spec = mel_transform(waveform)
        
        # Chuyển đổi sang dB scale
        mel_spec = torchaudio.transforms.AmplitudeToDB()(mel_spec)
        
        # Đảm bảo kích thước phù hợp (cắt hoặc pad)
        if mel_spec.shape[2] > 128:
            mel_spec = mel_spec[:, :, :128]
        elif mel_spec.shape[2] < 128:
            pad_size = 128 - mel_spec.shape[2]
            mel_spec = F.pad(mel_spec, (0, pad_size))
        
        # Chuẩn hóa
        mel_spec = (mel_spec - mel_spec.min()) / (mel_spec.max() - mel_spec.min() + 1e-8)
        
        # Chuyển đổi thành 3 kênh cho ConvNeXt
        mel_spec = mel_spec.repeat(3, 1, 1)  # (3, n_mels, time)
        
    # Xử lý dữ liệu 2D (đã là spectrogram)
    else:
        # Đảm bảo kích thước phù hợp
        if data.shape[0] > 128 or data.shape[1] > 128:
            # Lấy mẫu đơn giản
            data = data[::max(1, data.shape[0]//128), ::max(1, data.shape[1]//128)]
            data = data[:128, :128]
        
        # Pad nếu quá nhỏ
        if data.shape[0] < 128 or data.shape[1] < 128:
            padded = np.zeros((128, 128))
            padded[:data.shape[0], :data.shape[1]] = data
            data = padded
        
        # Chuẩn hóa
        data = (data - np.min(data)) / (np.max(data) - np.min(data) + 1e-8)
        
        # Chuyển đổi sang tensor và thêm 3 kênh
        mel_spec = torch.FloatTensor(data).unsqueeze(0).repeat(3, 1, 1)  # (3, 128, 128)
    
    # Add batch dimension
    mel_spec = mel_spec.unsqueeze(0).to(device)
    
    # Apply XAI methods
    xai_results = apply_xai_methods(model, mel_spec, layer_name="convnext")
    
    # Get filename from path
    filename = os.path.basename(file_path).split('.')[0]
    
    # Visualize XAI results
    visualize_xai_results(xai_results, save_dir=save_dir, filename=f"{filename}_xai")
    
    return xai_results

def occlusion_sensitivity(model, input_tensor, target=None, window_size=8, stride=4):
    """
    Tính toán độ nhạy cảm của mô hình đối với việc che khuất các phần của đầu vào.
    
    Args:
        model: Mô hình đã huấn luyện
        input_tensor: Tensor đầu vào cần phân tích
        target: Nhãn mục tiêu (mặc định là None, sẽ sử dụng dự đoán của mô hình)
        window_size: Kích thước cửa sổ che khuất
        stride: Bước nhảy giữa các cửa sổ che khuất
        
    Returns:
        Tensor chứa độ nhạy cảm của mô hình đối với việc che khuất
    """
    model.eval()
    device = next(model.parameters()).device
    
    # Đảm bảo input_tensor có batch dimension
    if len(input_tensor.shape) == 3:
        input_tensor = input_tensor.unsqueeze(0)
    
    # Lấy dự đoán
    with torch.no_grad():
        output = model(input_tensor)
        pred_score = output.item()
        pred_class = 1 if pred_score > 0.5 else 0
    
    if target is None:
        target = pred_class
    
    # Tính kích thước của đầu vào
    h, w = input_tensor.shape[2:]
    
    # Tính số lượng cửa sổ theo chiều cao và chiều rộng
    h_windows = (h - window_size) // stride + 1
    w_windows = (w - window_size) // stride + 1
    
    # Tạo ma trận độ nhạy cảm
    sensitivity_map = torch.zeros(h_windows, w_windows)
    
    # Tạo ma trận che khuất
    occlusion_mask = torch.ones_like(input_tensor)
    
    # Lặp qua tất cả các cửa sổ
    for i in range(h_windows):
        for j in range(w_windows):
            # Thiết lập ma trận che khuất
            occlusion_mask[:, :, i*stride:i*stride+window_size, j*stride:j*stride+window_size] = 0
            
            # Tính toán độ nhạy cảm
            with torch.no_grad():
                occluded_output = model(input_tensor * occlusion_mask)
                occluded_score = occluded_output.item()
            
            sensitivity_map[i, j] = pred_score - occluded_score
            
            # Đặt lại ma trận che khuất
            occlusion_mask[:, :, i*stride:i*stride+window_size, j*stride:j*stride+window_size] = 1

def predict_single_file(model, file_path):
    """
    Dự đoán trên một file âm thanh đơn lẻ.
    
    Args:
        model: Mô hình đã huấn luyện
        file_path: Đường dẫn đến file cần dự đoán
        
    Returns:
        Kết quả dự đoán (0: bình thường, 1: ngưng thở) và xác suất
    """
    device = next(model.parameters()).device
    
    try:
        # Load data
        data = np.load(file_path)
        
        # Xử lý dữ liệu 1D (dạng tín hiệu âm thanh)
        if len(data.shape) == 1:
            # Giới hạn độ dài tín hiệu
            seq_length = min(data.shape[0], 16000*5)  # Giới hạn 5 giây ở 16kHz
            data = data[:seq_length]
            
            # Pad nếu tín hiệu quá ngắn
            if data.shape[0] < seq_length:
                data = np.pad(data, (0, seq_length - data.shape[0]), 'constant')
            
            # Chuyển đổi sang tensor
            waveform = torch.FloatTensor(data).unsqueeze(0)  # Thêm chiều kênh (1, seq_length)
            
            # Tính mel spectrogram
            mel_transform = torchaudio.transforms.MelSpectrogram(
                sample_rate=16000,
                n_fft=1024,
                hop_length=512,
                n_mels=128,
                f_min=0,
                f_max=8000,
                power=2.0
            )
            mel_spec = mel_transform(waveform)
            
            # Chuyển đổi sang dB scale
            mel_spec = torchaudio.transforms.AmplitudeToDB()(mel_spec)
            
            # Đảm bảo kích thước phù hợp (cắt hoặc pad)
            if mel_spec.shape[2] > 128:
                mel_spec = mel_spec[:, :, :128]
            elif mel_spec.shape[2] < 128:
                pad_size = 128 - mel_spec.shape[2]
                mel_spec = F.pad(mel_spec, (0, pad_size))
            
            # Chuẩn hóa
            mel_spec = (mel_spec - mel_spec.min()) / (mel_spec.max() - mel_spec.min() + 1e-8)
            
            # Chuyển đổi thành 3 kênh cho ConvNeXt
            mel_spec = mel_spec.repeat(3, 1, 1)  # (3, n_mels, time)
            
        # Xử lý dữ liệu 2D (đã là spectrogram)
        else:
            # Đảm bảo kích thước phù hợp
            if data.shape[0] > 128 or data.shape[1] > 128:
                # Lấy mẫu đơn giản
                data = data[::max(1, data.shape[0]//128), ::max(1, data.shape[1]//128)]
                data = data[:128, :128]
            
            # Pad nếu quá nhỏ
            if data.shape[0] < 128 or data.shape[1] < 128:
                padded = np.zeros((128, 128))
                padded[:data.shape[0], :data.shape[1]] = data
                data = padded
            
            # Chuẩn hóa
            data = (data - np.min(data)) / (np.max(data) - np.min(data) + 1e-8)
            
            # Chuyển đổi sang tensor và thêm 3 kênh
            mel_spec = torch.FloatTensor(data).unsqueeze(0).repeat(3, 1, 1)  # (3, 128, 128)
        
        # Add batch dimension
        mel_spec = mel_spec.unsqueeze(0).to(device)
        
        # Dự đoán
        model.eval()
        with torch.no_grad():
            output = model(mel_spec)
            probability = output.item()
            prediction = 1 if probability > 0.5 else 0
        
        result = "Apnea" if prediction == 1 else "Normal"
        print(f"File: {file_path}")
        print(f"Prediction: {result}")
        print(f"Probability: {probability:.4f}")
        
        # Visualize the input
        plt.figure(figsize=(10, 6))
        if len(data.shape) == 1:
            plt.subplot(2, 1, 1)
            plt.plot(data)
            plt.title('Audio Waveform')
            plt.xlabel('Sample')
            plt.ylabel('Amplitude')
            
            plt.subplot(2, 1, 2)
            plt.imshow(mel_spec.squeeze().cpu().numpy().mean(axis=0), 
                      aspect='auto', origin='lower', cmap='viridis')
            plt.title(f'Mel Spectrogram - Prediction: {result} ({probability:.4f})')
            plt.xlabel('Time')
            plt.ylabel('Mel Frequency')
            plt.colorbar()
        else:
            plt.imshow(data, aspect='auto', origin='lower', cmap='viridis')
            plt.title(f'Input Data - Prediction: {result} ({probability:.4f})')
            plt.xlabel('Time')
            plt.ylabel('Frequency')
            plt.colorbar()
        
        plt.tight_layout()
        
        # Get filename from path
        filename = os.path.basename(file_path).split('.')[0]
        plt.savefig(f'{filename}_prediction.png', dpi=300)
        plt.close()
        
        print(f"Visualization saved to {filename}_prediction.png")
        
        return prediction, probability
        
    except Exception as e:
        print(f"Error predicting file {file_path}: {e}")
        import traceback
        traceback.print_exc()
        return None, None

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Apnea Detection using ConvNeXt-LSTM')
    parser.add_argument('--train', action='store_true', help='Train the model')
    parser.add_argument('--predict', type=str, help='Predict on a single file')
    parser.add_argument('--model', type=str, default='apnea_model_convnext_lstm.pth', 
                        help='Path to the model file for prediction')
    parser.add_argument('--xai', type=str, help='Apply XAI methods to a file')
    parser.add_argument('--feature-importance', action='store_true', 
                        help='Generate feature importance visualization')
    
    args = parser.parse_args()
    
    if args.train:
        print("Training the model...")
        model, losses = train_and_evaluate()
        
        # Nếu huấn luyện thành công và yêu cầu tạo biểu đồ tầm quan trọng của đặc trưng
        if args.feature_importance and model is not None:
            print("Generating feature importance visualization...")
            _, test_dataset = prepare_dataset()
            generate_feature_importance(model, test_dataset)
    
    elif args.predict:
        if not os.path.exists(args.model):
            print(f"Model file {args.model} not found. Please train the model first.")
            exit()
        
        if not os.path.exists(args.predict):
            print(f"File {args.predict} not found.")
            exit()
        
        print(f"Predicting on file {args.predict} using model {args.model}...")
        # Load model
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        model = ConvNeXtLSTM().to(device)
        model.load_state_dict(torch.load(args.model, map_location=device))
        
        # Predict
        predict_single_file(model, args.predict)
        
    elif args.xai:
        if not os.path.exists(args.model):
            print(f"Model file {args.model} not found. Please train the model first.")
            exit()
        
        if not os.path.exists(args.xai):
            print(f"File {args.xai} not found.")
            exit()
        
        print(f"Applying XAI methods to file {args.xai} using model {args.model}...")
        # Load model
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        model = ConvNeXtLSTM().to(device)
        model.load_state_dict(torch.load(args.model, map_location=device))
        
        # Apply XAI
        analyze_sample_with_xai(model, args.xai)
        
    else:
        # Default behavior: train the model
        print("No action specified. Training the model...")
        model, losses = train_and_evaluate()
        
        # Chỉ phân tích XAI nếu đã huấn luyện thành công mô hình
        if model is not None:
            # Tìm một mẫu để phân tích XAI
            dataset_path = find_dataset_path()
            if dataset_path:
                data_files, _ = load_npy_files(dataset_path)
                if data_files and len(data_files) > 0:
                    # Chọn một file mẫu từ dataset
                    sample_file = data_files[0]
                    print(f"Analyzing sample file: {sample_file}")
                    try:
                        analyze_sample_with_xai(model, sample_file)
                    except Exception as e:
                        print(f"Error analyzing sample file: {e}")
                        import traceback
                        traceback.print_exc()
                else:
                    print("No sample files found for XAI analysis.")
            else:
                print("Dataset path not found. Cannot perform XAI analysis.")



