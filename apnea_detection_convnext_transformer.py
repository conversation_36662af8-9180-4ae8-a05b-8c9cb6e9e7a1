import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix, roc_curve, auc
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import torchaudio
import torchvision.models as models
from torch.nn import functional as F
import seaborn as sns
from captum.attr import IntegratedGradients, GradientShap, DeepLift, LayerGradCam
from captum.attr import visualization as viz
import argparse
import csv

# Đ<PERSON>nh nghĩa đường dẫn dataset
DATASET_PATH = "D:/AI_Thao/Project/PSG-audio/dataset"

# Định nghĩa DropPath (Stochastic Depth) cho ConvNeXt
class DropPath(nn.Module):
    def __init__(self, drop_prob=0.):
        super(DropPath, self).__init__()
        self.drop_prob = drop_prob
        
    def forward(self, x):
        if self.drop_prob == 0. or not self.training:
            return x
        keep_prob = 1 - self.drop_prob
        shape = (x.shape[0],) + (1,) * (x.ndim - 1)
        random_tensor = keep_prob + torch.rand(shape, dtype=x.dtype, device=x.device)
        random_tensor.floor_()
        output = x.div(keep_prob) * random_tensor
        return output

# Định nghĩa ConvNeXt Block
class ConvNeXtBlock(nn.Module):
    def __init__(self, dim, drop_path=0., layer_scale_init_value=1e-6):
        super().__init__()
        self.dwconv = nn.Conv2d(dim, dim, kernel_size=7, padding=3, groups=dim)
        self.norm = nn.BatchNorm2d(dim)
        self.pwconv1 = nn.Conv2d(dim, 4 * dim, kernel_size=1)
        self.act = nn.GELU()
        self.pwconv2 = nn.Conv2d(4 * dim, dim, kernel_size=1)
        self.gamma = nn.Parameter(layer_scale_init_value * torch.ones((dim, 1, 1)), 
                                  requires_grad=True) if layer_scale_init_value > 0 else None
        self.drop_path = DropPath(drop_path) if drop_path > 0. else nn.Identity()

    def forward(self, x):
        input = x
        x = self.dwconv(x)
        x = self.norm(x)
        x = self.pwconv1(x)
        x = self.act(x)
        x = self.pwconv2(x)
        if self.gamma is not None:
            x = self.gamma * x
        x = input + self.drop_path(x)
        return x

# Định nghĩa Transformer Encoder Block với Multi-Head Attention
class TransformerEncoderBlock(nn.Module):
    def __init__(self, embed_dim, num_heads, mlp_ratio=4.0, qkv_bias=True, drop_rate=0.0, attn_drop_rate=0.0):
        super().__init__()
        self.norm1 = nn.LayerNorm(embed_dim)
        self.attn = nn.MultiheadAttention(embed_dim, num_heads, dropout=attn_drop_rate, batch_first=True)
        self.norm2 = nn.LayerNorm(embed_dim)
        mlp_hidden_dim = int(embed_dim * mlp_ratio)
        self.mlp = nn.Sequential(
            nn.Linear(embed_dim, mlp_hidden_dim),
            nn.GELU(),
            nn.Dropout(drop_rate),
            nn.Linear(mlp_hidden_dim, embed_dim),
            nn.Dropout(drop_rate)
        )
        
    def forward(self, x):
        # Self-attention với residual connection
        x_norm = self.norm1(x)
        attn_output, _ = self.attn(x_norm, x_norm, x_norm)
        x = x + attn_output
        
        # MLP với residual connection
        x = x + self.mlp(self.norm2(x))
        return x

# Định nghĩa Positional Encoding
class PositionalEncoding(nn.Module):
    def __init__(self, d_model, max_len=5000):
        super().__init__()
        
        # Tạo positional encoding matrix
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-np.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0)
        
        # Đăng ký buffer (không phải parameter)
        self.register_buffer('pe', pe)
        
    def forward(self, x):
        # x: [batch_size, seq_len, d_model]
        x = x + self.pe[:, :x.size(1), :]
        return x

# Định nghĩa ConvNeXt Zepto (ConvNeXt_Z) theo bài báo, với cấu trúc nhỏ gọn hơn
class ConvNeXtZepto(nn.Module):
    """
    ConvNeXt Zepto (ConvNeXt_Z) theo bài báo, với cấu trúc nhỏ gọn hơn
    """
    def __init__(self, in_channels=3, num_classes=1):
        super(ConvNeXtZepto, self).__init__()
        
        # Định nghĩa số lượng block và kích thước kênh theo bài báo
        depths = [2, 2, 6, 2]  # Số lượng block trong mỗi stage
        dims = [32, 64, 128, 256]  # Kích thước kênh trong mỗi stage
        
        # Stem layer - downsampling 4x
        self.stem = nn.Sequential(
            nn.Conv2d(in_channels, dims[0], kernel_size=4, stride=4),
            nn.BatchNorm2d(dims[0]),
            nn.GELU()
        )
        
        # 4 stages của ConvNeXt
        self.stages = nn.ModuleList()
        
        # Stage 1
        stage1 = []
        for i in range(depths[0]):
            stage1.append(ConvNeXtBlock(dims[0]))
        self.stages.append(nn.Sequential(*stage1))
        
        # Stage 2-4 với downsampling
        for i in range(1, 4):
            # Downsampling layer
            downsample = nn.Sequential(
                nn.BatchNorm2d(dims[i-1]),
                nn.Conv2d(dims[i-1], dims[i], kernel_size=2, stride=2)
            )
            
            # Tạo stage với các block
            stage = [downsample]
            for j in range(depths[i]):
                stage.append(ConvNeXtBlock(dims[i]))
            
            self.stages.append(nn.Sequential(*stage))
        
        # Global average pooling và classifier
        self.norm = nn.BatchNorm2d(dims[-1])
        
    def forward(self, x):
        # Stem
        x = self.stem(x)
        
        # Stages
        for stage in self.stages:
            x = stage(x)
        
        # Normalization
        x = self.norm(x)
        
        return x

# Định nghĩa mô hình ConvNeXt-Transformer
class ConvNeXtTransformerV2(nn.Module):
    """
    Mô hình ConvNeXt + Transformer theo cấu trúc bài báo
    với số lượng tham số được tối ưu (~2.5M)
    """
    def __init__(self, num_classes=1):
        super(ConvNeXtTransformerV2, self).__init__()
        
        # ConvNeXt Zepto backbone (~2.2M params)
        self.convnext = ConvNeXtZepto(in_channels=3)
        
        # Giảm kích thước embedding xuống 128 để giảm số lượng tham số
        self.projection = nn.Linear(256, 128)
        
        # Transformer Encoder với số lượng tham số giảm (~0.2-0.3M params)
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=128,  # Giảm từ 256 xuống 128
            nhead=2,      # Giảm từ 8 xuống 2
            dim_feedforward=512,  # Giảm từ 1024 xuống 512
            dropout=0.1,
            activation='gelu',
            batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(
            encoder_layer,
            num_layers=1  # Giảm từ 6 xuống 1
        )
        
        # Positional encoding
        self.pos_encoder = PositionalEncoding(d_model=128)
        
        # Classification head (~0.1M params)
        self.classifier = nn.Sequential(
            nn.Linear(128, 64),
            nn.GELU(),
            nn.Dropout(0.2),
            nn.Linear(64, num_classes),
            nn.Sigmoid()
        )
        
        # In số lượng tham số
        self._print_params()
        
    def _print_params(self):
        """In số lượng tham số của từng phần trong mô hình"""
        convnext_params = sum(p.numel() for p in self.convnext.parameters())
        projection_params = sum(p.numel() for p in self.projection.parameters())
        transformer_params = sum(p.numel() for p in self.transformer_encoder.parameters())
        classifier_params = sum(p.numel() for p in self.classifier.parameters())
        total_params = convnext_params + projection_params + transformer_params + classifier_params
        
        print(f"ConvNeXt_Z params: {convnext_params:,} (~{convnext_params/1e6:.1f}M)")
        print(f"Projection params: {projection_params:,}")
        print(f"Transformer params: {transformer_params:,} (~{transformer_params/1e6:.1f}M)")
        print(f"Classifier params: {classifier_params:,}")
        print(f"Total params: {total_params:,} (~{total_params/1e6:.1f}M)")
        
    def forward(self, x):
        # ConvNeXt feature extraction
        batch_size = x.size(0)
        x = self.convnext(x)  # [B, 256, H, W]
        
        # Average pooling trong chiều H để giữ thông tin thời gian
        # Theo bài báo, đầu ra của ConvNeXt có kích thước [256, 2, 27]
        x = x.mean(dim=2)  # [B, 256, W]
        
        # Permute để phù hợp với Transformer: [B, W, 256]
        x = x.permute(0, 2, 1)
        
        # Giảm kích thước embedding
        x = self.projection(x)  # [B, W, 128]
        
        # Thêm positional encoding
        x = self.pos_encoder(x)
        
        # Áp dụng Transformer Encoder
        x = self.transformer_encoder(x)
        
        # Global pooling (mean over sequence dimension)
        x = x.mean(dim=1)
        
        # Classification
        x = self.classifier(x)
        return x

# Dataset class
class ApneaDataset(Dataset):
    """PyTorch Dataset for Apnea data using .npy files."""
    def __init__(self, data_files, labels):
        self.data_files = data_files
        self.labels = labels
        # Định nghĩa mel spectrogram transform
        self.mel_transform = torchaudio.transforms.MelSpectrogram(
            sample_rate=16000,
            n_fft=1024,
            hop_length=512,
            n_mels=128,
            f_min=0,
            f_max=8000,
            power=2.0
        )
        
    def __len__(self):
        return len(self.labels)
    
    def __getitem__(self, idx):
        file_path = self.data_files[idx]
        label = self.labels[idx]
        
        # Load .npy file
        data = np.load(file_path)
        
        # Xử lý dữ liệu 1D (dạng tín hiệu âm thanh)
        if len(data.shape) == 1:
            # Kiểm tra độ dài tối thiểu
            min_length = 1024  # Độ dài tối thiểu phải lớn hơn n_fft
            
            # Giới hạn độ dài tín hiệu
            seq_length = min(data.shape[0], 16000*5)  # Giới hạn 5 giây ở 16kHz
            data = data[:seq_length]
            
            # Kiểm tra và đảm bảo độ dài tối thiểu
            if data.shape[0] < min_length:
                print(f"Warning: File {file_path} has length {data.shape[0]} < {min_length}. Padding to minimum length.")
                data = np.pad(data, (0, min_length - data.shape[0]), 'constant')
            
            # Pad nếu tín hiệu quá ngắn
            if data.shape[0] < seq_length:
                data = np.pad(data, (0, seq_length - data.shape[0]), 'constant')
            
            # Chuyển đổi sang tensor
            waveform = torch.FloatTensor(data).unsqueeze(0)  # Thêm chiều kênh (1, seq_length)
            
            try:
                # Tính mel spectrogram
                mel_spec = self.mel_transform(waveform)  # (1, n_mels, time)
                
                # Chuyển đổi sang dB scale
                mel_spec = torchaudio.transforms.AmplitudeToDB()(mel_spec)
                
                # Đảm bảo kích thước phù hợp (cắt hoặc pad)
                if mel_spec.shape[2] > 128:
                    mel_spec = mel_spec[:, :, :128]
                elif mel_spec.shape[2] < 128:
                    pad_size = 128 - mel_spec.shape[2]
                    mel_spec = F.pad(mel_spec, (0, pad_size))
                
                # Chuẩn hóa
                mel_spec = (mel_spec - mel_spec.min()) / (mel_spec.max() - mel_spec.min() + 1e-8)
                
                # Chuyển đổi thành 3 kênh cho ConvNeXt
                mel_spec = mel_spec.repeat(3, 1, 1)  # (3, n_mels, time)
            except Exception as e:
                print(f"Error processing audio file {file_path}: {e}")
                # Tạo một mel spectrogram trống nếu xử lý thất bại
                mel_spec = torch.zeros((3, 128, 128))
            
        # Xử lý dữ liệu 2D (đã là spectrogram)
        else:
            # Đảm bảo kích thước phù hợp
            if data.shape[0] > 128 or data.shape[1] > 128:
                # Lấy mẫu đơn giản
                data = data[::max(1, data.shape[0]//128), ::max(1, data.shape[1]//128)]
                data = data[:128, :128]
            
            # Pad nếu quá nhỏ
            if data.shape[0] < 128 or data.shape[1] < 128:
                padded = np.zeros((128, 128))
                padded[:data.shape[0], :data.shape[1]] = data
                data = padded
            
            # Chuẩn hóa
            data = (data - np.min(data)) / (np.max(data) - np.min(data) + 1e-8)
            
            # Chuyển đổi sang tensor và thêm 3 kênh
            mel_spec = torch.FloatTensor(data).unsqueeze(0).repeat(3, 1, 1)  # (3, 128, 128)
        
        return mel_spec, torch.FloatTensor([label])

# Hàm tìm đường dẫn dataset
def find_dataset_path():
    """Tìm đường dẫn dataset."""
    if os.path.exists(DATASET_PATH):
        return DATASET_PATH
    
    # Thử tìm trong thư mục hiện tại
    current_dir = os.getcwd()
    for root, dirs, files in os.walk(current_dir):
        if "dataset" in dirs:
            return os.path.join(root, "dataset")
    
    return None

# Hàm load dữ liệu
def load_npy_files(dataset_path):
    """Load all .npy files from the dataset directory."""
    data_files = []
    labels = []
    
    # Tìm tất cả các file .npy
    for root, dirs, files in os.walk(dataset_path):
        for file in files:
            if file.endswith('.npy'):
                file_path = os.path.join(root, file)
                
                # Xác định nhãn dựa trên tên file
                if '_ap.npy' in file.lower():
                    label = 1  # Apnea
                elif '_nap.npy' in file.lower():
                    label = 0  # Normal
                else:
                    # Nếu không thể xác định từ tên file, thử xác định từ đường dẫn
                    if 'apnea' in root.lower() and 'non' not in root.lower() and 'normal' not in root.lower():
                        label = 1  # Apnea
                    else:
                        label = 0  # Normal
                
                data_files.append(file_path)
                labels.append(label)
    
    print(f"Loaded {len(data_files)} files: {len([l for l in labels if l == 1])} apnea, {len([l for l in labels if l == 0])} normal")
    
    # Kiểm tra nếu tất cả các nhãn đều giống nhau
    if len(set(labels)) == 1:
        print("WARNING: All files have the same label! Check your dataset structure.")
        print("Looking at file names for more information:")
        for i in range(min(5, len(data_files))):
            print(f"  - {os.path.basename(data_files[i])}")
    
    return data_files, labels

# Hàm chuẩn bị dataset
def prepare_dataset():
    """Prepare the dataset for training and testing."""
    # Find dataset path
    dataset_path = find_dataset_path()
    if dataset_path is None:
        raise ValueError("Dataset path not found. Please check the path.")
    
    # Load data
    data_files, labels = load_npy_files(dataset_path)
    
    # Split data
    train_files, test_files, train_labels, test_labels = train_test_split(
        data_files, labels, test_size=0.2, random_state=42, stratify=labels
    )
    
    # Create datasets
    train_dataset = ApneaDataset(train_files, train_labels)
    test_dataset = ApneaDataset(test_files, test_labels)
    
    return train_dataset, test_dataset

# Hàm huấn luyện và đánh giá mô hình
def train_and_evaluate():
    """Train and evaluate the model."""
    # Set device
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")
    
    # Prepare data
    try:
        train_dataset, test_dataset = prepare_dataset()
    except Exception as e:
        print(f"Error preparing dataset: {e}")
        return None, None
    
    # Create dataloaders
    train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=32, shuffle=False)
    
    # Initialize model - sử dụng mô hình tối ưu
    model = ConvNeXtTransformerV2(num_classes=1).to(device)
    
    # Print model summary
    print(f"Model architecture:\n{model}")
    total_params = sum(p.numel() for p in model.parameters())
    print(f"Total parameters: {total_params:,}")
    
    # Define loss function and optimizer
    criterion = nn.BCELoss()
    optimizer = optim.AdamW(model.parameters(), lr=0.0005, weight_decay=0.01)
    
    # Learning rate scheduler
    scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
        optimizer, T_0=10, T_mult=2, eta_min=1e-6
    )
    
    # Early stopping
    best_val_loss = float('inf')
    patience = 15
    patience_counter = 0
    best_model_state = None
    
    # Training loop
    num_epochs = 100  # Tăng số epoch lên cao hơn
    train_losses = []
    val_losses = []
    train_accuracies = []
    val_accuracies = []
    
    for epoch in range(num_epochs):
        # Training phase
        model.train()
        running_loss = 0.0
        correct = 0
        total = 0
        
        for inputs, labels in train_loader:
            inputs, labels = inputs.to(device), labels.to(device)
            
            # Zero the parameter gradients
            optimizer.zero_grad()
            
            # Forward pass
            outputs = model(inputs)
            loss = criterion(outputs, labels)
            
            # Backward pass and optimize
            loss.backward()
            # Gradient clipping để tránh exploding gradients
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            running_loss += loss.item() * inputs.size(0)
            
            # Calculate accuracy
            predicted = (outputs > 0.5).float()
            total += labels.size(0)
            correct += (predicted == labels).sum().item()
        
        epoch_loss = running_loss / len(train_loader.dataset)
        epoch_acc = 100 * correct / total
        train_losses.append(epoch_loss)
        train_accuracies.append(epoch_acc)
        
        # Validation phase
        model.eval()
        val_loss = 0.0
        correct = 0
        total = 0
        
        with torch.no_grad():
            for inputs, labels in test_loader:
                inputs, labels = inputs.to(device), labels.to(device)
                
                outputs = model(inputs)
                loss = criterion(outputs, labels)
                
                val_loss += loss.item() * inputs.size(0)
                
                predicted = (outputs > 0.5).float()
                total += labels.size(0)
                correct += (predicted == labels).sum().item()
        
        epoch_val_loss = val_loss / len(test_loader.dataset)
        epoch_val_acc = 100 * correct / total
        val_losses.append(epoch_val_loss)
        val_accuracies.append(epoch_val_acc)
        
        # Update learning rate
        scheduler.step()
        current_lr = scheduler.get_last_lr()[0]
        
        print(f'Epoch {epoch+1}/{num_epochs}, '
              f'LR: {current_lr:.6f}, '
              f'Train Loss: {epoch_loss:.4f}, '
              f'Val Loss: {epoch_val_loss:.4f}, '
              f'Train Acc: {epoch_acc:.2f}%, '
              f'Val Acc: {epoch_val_acc:.2f}%')
        
        # Early stopping
        if epoch_val_loss < best_val_loss:
            best_val_loss = epoch_val_loss
            patience_counter = 0
            best_model_state = model.state_dict().copy()
            print(f"New best model saved with validation loss: {best_val_loss:.4f}")
        else:
            patience_counter += 1
            print(f"Early stopping counter: {patience_counter}/{patience}")
            if patience_counter >= patience:
                print("Early stopping triggered!")
                break
    
    return model, {
        'train_losses': train_losses,
        'val_losses': val_losses,
        'train_accuracies': train_accuracies,
        'val_accuracies': val_accuracies
    }

# Hàm dự đoán cho một file
def predict_single_file(model, file_path):
    """Dự đoán nhãn cho một file âm thanh."""
    dataset = ApneaDataset([file_path], [0])  # Nhãn không quan trọng ở đây
    input_tensor, _ = dataset[0]
    input_tensor = input_tensor.unsqueeze(0).to(next(model.parameters()).device)
    
    with torch.no_grad():
        output = model(input_tensor)
        prob = output.item()
        pred = 1 if prob > 0.5 else 0
    
    return pred, prob

# Hàm áp dụng các phương pháp XAI
def apply_xai_methods(model, input_tensor, layer_name="convnext"):
    """Áp dụng các phương pháp XAI cho một layer cụ thể."""
    device = next(model.parameters()).device
    input_tensor = input_tensor.to(device)
    input_tensor.requires_grad = True
    
    # Chuẩn bị các phương pháp XAI
    ig = IntegratedGradients(model)
    gs = GradientShap(model)
    dl = DeepLift(model)
    
    # Tạo baseline cho các phương pháp XAI
    baseline = torch.zeros_like(input_tensor).to(device)
    baselines_dist = torch.randn(10, *input_tensor.shape[1:]).unsqueeze(0).to(device) * 0.001
    
    # Áp dụng XAI cho ConvNeXt layer
    if layer_name == "convnext":
        # Trong mô hình ConvNeXtTransformerV2, convnext là một thuộc tính trực tiếp
        if hasattr(model, 'convnext'):
            target_layer = model.convnext
        else:
            # Tìm layer cuối cùng của ConvNeXt
            target_layer = None
            for name, module in model.named_modules():
                if 'convnext' in name.lower() and isinstance(module, nn.Module) and not isinstance(module, nn.Sequential):
                    target_layer = module
                    break
            
            if target_layer is None:
                print("Warning: Could not find ConvNeXt layer. Using model as target.")
                target_layer = model
    
    # Áp dụng XAI cho Transformer layer
    elif layer_name == "transformer":
        # Trong mô hình ConvNeXtTransformerV2, transformer_encoder là một thuộc tính trực tiếp
        if hasattr(model, 'transformer_encoder'):
            target_layer = model.transformer_encoder
        else:
            # Tìm layer Transformer
            target_layer = None
            for name, module in model.named_modules():
                if 'transformer' in name.lower() and isinstance(module, nn.Module) and not isinstance(module, nn.Sequential):
                    target_layer = module
                    break
            
            if target_layer is None:
                print("Warning: Could not find Transformer layer. Using model as target.")
                target_layer = model
    else:
        raise ValueError("Layer name must be 'convnext' or 'transformer'")
    
    print(f"Using target layer: {target_layer.__class__.__name__}")
    
    # Tính toán các thuộc tính
    xai_results = {}
    
    try:
        # IntegratedGradients
        ig_attr = ig.attribute(input_tensor, baselines=baseline, target=0, n_steps=50)
        xai_results['ig_attr'] = ig_attr
    except Exception as e:
        print(f"Error in IntegratedGradients: {e}")
        xai_results['ig_attr'] = torch.zeros_like(input_tensor)
    
    try:
        # GradientShap - Xử lý lỗi detach
        gs_attr = gs.attribute(input_tensor, baselines=baselines_dist, target=0, n_samples=10)
        xai_results['gs_attr'] = gs_attr
    except Exception as e:
        print(f"Error in GradientShap: {e}")
        xai_results['gs_attr'] = torch.zeros_like(input_tensor)
    
    try:
        # DeepLift
        dl_attr = dl.attribute(input_tensor, baselines=baseline, target=0)
        xai_results['dl_attr'] = dl_attr
    except Exception as e:
        print(f"Error in DeepLift: {e}")
        xai_results['dl_attr'] = torch.zeros_like(input_tensor)
    
    # Tính toán Grad-CAM
    try:
        if target_layer is not None:
            gradcam = LayerGradCam(model, target_layer)
            gradcam_attr = gradcam.attribute(input_tensor, target=0)
            xai_results['gradcam'] = gradcam_attr
        else:
            xai_results['gradcam'] = None
    except Exception as e:
        print(f"Error in Grad-CAM: {e}")
        xai_results['gradcam'] = None
    
    return xai_results

# Hàm vẽ hình ảnh XAI
def visualize_xai_results(xai_results, filename, save_dir="xai_visualizations"):
    """Vẽ hình ảnh XAI từ các thuộc tính."""
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
    
    input_data_viz = input_tensor.squeeze().cpu().numpy()
    ig_attr_viz = xai_results['ig_attr'].squeeze().cpu().numpy()
    gs_attr_viz = xai_results['gs_attr'].squeeze().cpu().numpy()
    dl_attr_viz = xai_results['dl_attr'].squeeze().cpu().numpy()
    gradcam = xai_results['gradcam']
    
    # Chuẩn hóa để hiển thị
    def normalize(data):
        return (data - data.min()) / (data.max() - data.min() + 1e-8)
    
    input_data_viz = normalize(input_data_viz)
    ig_attr_viz = normalize(ig_attr_viz)
    gs_attr_viz = normalize(gs_attr_viz)
    dl_attr_viz = normalize(dl_attr_viz)
    
    # Tạo hình ảnh
    plt.figure(figsize=(15, 12))
    
    # Hiển thị dữ liệu đầu vào
    plt.subplot(2, 3, 1)
    plt.imshow(input_data_viz, cmap='viridis', aspect='auto')
    plt.title(f'Input - Prediction: {pred_class} ({pred_score:.4f})')
    plt.colorbar()
    
    # Hiển thị Integrated Gradients
    plt.subplot(2, 3, 2)
    plt.imshow(ig_attr_viz, cmap='hot', aspect='auto')
    plt.title('Integrated Gradients')
    plt.colorbar()
    
    # Hiển thị GradientSHAP
    plt.subplot(2, 3, 3)
    plt.imshow(gs_attr_viz, cmap='hot', aspect='auto')
    plt.title('Gradient SHAP')
    plt.colorbar()
    
    # Hiển thị DeepLift
    plt.subplot(2, 3, 4)
    plt.imshow(dl_attr_viz, cmap='hot', aspect='auto')
    plt.title('DeepLift')
    plt.colorbar()
    
    # Hiển thị Grad-CAM nếu có
    if gradcam is not None:
        gradcam = gradcam.squeeze()
        if len(gradcam.shape) > 2:
            gradcam = gradcam.mean(axis=0)
        gradcam = normalize(gradcam)
        
        plt.subplot(2, 3, 5)
        plt.imshow(gradcam, cmap='jet', aspect='auto')
        plt.title('Grad-CAM')
        plt.colorbar()
        
        # Hiển thị overlay Grad-CAM lên input
        plt.subplot(2, 3, 6)
        plt.imshow(input_data_viz, cmap='gray', aspect='auto')
        plt.imshow(gradcam, cmap='jet', alpha=0.5, aspect='auto')
        plt.title('Grad-CAM Overlay')
        plt.colorbar()
    
    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, f"{filename}.png"), dpi=300)
    plt.close()
    
    print(f"XAI visualization saved to {os.path.join(save_dir, filename)}.png")

# Hàm tính AHI (Apnea-Hypopnea Index) theo bài báo
def estimate_ahi(predictions, segment_duration=30):
    """
    Ước tính chỉ số AHI dựa trên dự đoán của mô hình.
    
    Args:
        predictions: List các dự đoán (0: bình thường, 1: ngưng thở)
        segment_duration: Thời lượng của mỗi đoạn âm thanh (giây)
        
    Returns:
        Ước tính AHI (số sự kiện ngưng thở trên mỗi giờ)
    """
    # Đếm số lượng đoạn được dự đoán là ngưng thở
    num_apnea_segments = sum(predictions)
    
    # Đếm số lượng block liên tiếp của các đoạn ngưng thở
    apnea_blocks = []
    current_block = []
    
    for i, pred in enumerate(predictions):
        if pred == 1:
            current_block.append(i)
        elif current_block:
            apnea_blocks.append(current_block)
            current_block = []
    
    # Thêm block cuối cùng nếu có
    if current_block:
        apnea_blocks.append(current_block)
    
    # Tính số lượng block ngưng thở
    num_apnea_blocks = len(apnea_blocks)
    
    # Tính độ dài trung bình của các block ngưng thở
    mean_apnea_block_length = np.mean([len(block) for block in apnea_blocks]) if apnea_blocks else 0
    
    # Tính tổng thời gian ghi âm (giờ)
    total_duration_hours = len(predictions) * segment_duration / 3600
    
    # Tính AHI (số sự kiện ngưng thở trên mỗi giờ)
    ahi = num_apnea_segments / total_duration_hours
    
    # In thông tin
    print(f"Total segments: {len(predictions)}")
    print(f"Apnea segments: {num_apnea_segments}")
    print(f"Apnea blocks: {num_apnea_blocks}")
    print(f"Mean apnea block length: {mean_apnea_block_length:.2f} segments")
    print(f"Total duration: {total_duration_hours:.2f} hours")
    print(f"Estimated AHI: {ahi:.2f} events/hour")
    
    return ahi

# Hàm phân tích tham số dòng lệnh
def parse_arguments():
    """Phân tích tham số dòng lệnh."""
    parser = argparse.ArgumentParser(description='Apnea Detection using ConvNeXt-Transformer')
    
    # Tham số chung
    parser.add_argument('--mode', type=str, default='train', choices=['train', 'predict', 'xai'],
                        help='Mode to run: train, predict, or xai (default: train)')
    parser.add_argument('--model_path', type=str, default='apnea_model_convnext_transformer.pth',
                        help='Path to save/load model (default: apnea_model_convnext_transformer.pth)')
    parser.add_argument('--batch_size', type=int, default=32,
                        help='Batch size for training (default: 32)')
    parser.add_argument('--epochs', type=int, default=100,
                        help='Number of epochs for training (default: 100)')
    parser.add_argument('--learning_rate', type=float, default=0.0005,
                        help='Learning rate (default: 0.0005)')
    
    # Tham số cho chế độ dự đoán
    parser.add_argument('--input_file', type=str, default=None,
                        help='Path to input file for prediction (single file)')
    parser.add_argument('--input_dir', type=str, default=None,
                        help='Path to directory containing files for prediction')
    parser.add_argument('--output_dir', type=str, default='prediction_results',
                        help='Directory to save prediction results (default: prediction_results)')
    
    # Tham số cho chế độ XAI
    parser.add_argument('--xai_file', type=str, default=None,
                        help='Path to file for XAI analysis')
    parser.add_argument('--xai_dir', type=str, default=None,
                        help='Path to directory containing files for XAI analysis')
    parser.add_argument('--xai_output_dir', type=str, default='xai_results',
                        help='Directory to save XAI results (default: xai_results)')
    parser.add_argument('--xai_layer', type=str, default='both', choices=['convnext', 'transformer', 'both'],
                        help='Layer to apply XAI methods (default: both)')
    
    return parser.parse_args()

def predict_directory(model, input_dir, output_dir='prediction_results'):
    """Dự đoán cho tất cả các file trong một thư mục."""
    if not os.path.exists(input_dir):
        print(f"Error: Directory {input_dir} does not exist.")
        return
    
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # Tìm tất cả các file .npy trong thư mục
    files = []
    for root, _, filenames in os.walk(input_dir):
        for filename in filenames:
            if filename.endswith('.npy'):
                files.append(os.path.join(root, filename))
    
    if not files:
        print(f"No .npy files found in {input_dir}")
        return
    
    print(f"Found {len(files)} files for prediction.")
    
    # Tạo file CSV để lưu kết quả
    csv_path = os.path.join(output_dir, "predictions.csv")
    with open(csv_path, 'w', newline='') as csvfile:
        fieldnames = ['Filename', 'Prediction', 'Probability']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
    
    # Dự đoán từng file
    results = []
    for i, file_path in enumerate(files):
        print(f"[{i+1}/{len(files)}] Predicting: {file_path}")
        try:
            pred, prob = predict_single_file(model, file_path)
            if pred is not None:
                results.append((os.path.basename(file_path), "Apnea" if pred == 1 else "Normal", prob))
                
                # Ghi kết quả vào file CSV
                with open(csv_path, 'a', newline='') as csvfile:
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    writer.writerow({
                        'Filename': os.path.basename(file_path),
                        'Prediction': "Apnea" if pred == 1 else "Normal",
                        'Probability': f"{prob:.4f}"
                    })
        except Exception as e:
            print(f"Error predicting {file_path}: {e}")
    
    # Tính toán thống kê
    apnea_count = sum(1 for _, pred, _ in results if pred == "Apnea")
    normal_count = len(results) - apnea_count
    
    print("\nPrediction Summary:")
    print(f"Total files: {len(results)}")
    print(f"Apnea: {apnea_count} ({apnea_count/len(results)*100:.1f}%)")
    print(f"Normal: {normal_count} ({normal_count/len(results)*100:.1f}%)")
    print(f"Results saved to {csv_path}")
    
    return results

def load_model(model_path):
    """Tải mô hình đã huấn luyện từ file."""
    if not os.path.exists(model_path):
        print(f"Error: Model file {model_path} not found.")
        return None
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")
    
    try:
        # Khởi tạo mô hình
        model = ConvNeXtTransformerV2(num_classes=1).to(device)
        
        # Tải trọng số
        model.load_state_dict(torch.load(model_path, map_location=device))
        model.eval()
        
        print(f"Model loaded successfully from {model_path}")
        return model
    except Exception as e:
        print(f"Error loading model: {e}")
        import traceback
        traceback.print_exc()
        return None

def apply_xai_to_file(model, file_path, output_dir='xai_results', layer='both'):
    """Áp dụng XAI cho một file cụ thể."""
    if not os.path.exists(file_path):
        print(f"Error: File {file_path} does not exist.")
        return
    
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    try:
        # Chuẩn bị dữ liệu
        dataset = ApneaDataset([file_path], [0])  # Nhãn không quan trọng ở đây
        input_tensor, _ = dataset[0]
        input_tensor = input_tensor.unsqueeze(0).to(next(model.parameters()).device)
        
        # Dự đoán
        pred, prob = predict_single_file(model, file_path)
        if pred is None:
            print(f"Error: Failed to predict {file_path}")
            return
        
        pred_class = "Apnea" if pred == 1 else "Normal"
        
        # Áp dụng XAI
        if layer in ['convnext', 'both']:
            print(f"Applying XAI for ConvNeXt layer...")
            xai_results = apply_xai_methods(model, input_tensor, layer_name="convnext")
            visualize_xai_results(
                input_tensor=input_tensor,
                xai_results=xai_results,
                pred_class=pred_class,
                pred_score=prob,
                filename=f"{os.path.basename(file_path).split('.')[0]}_convnext_xai",
                save_dir=output_dir
            )
        
        if layer in ['transformer', 'both']:
            print(f"Applying XAI for Transformer layer...")
            xai_results = apply_xai_methods(model, input_tensor, layer_name="transformer")
            visualize_xai_results(
                input_tensor=input_tensor,
                xai_results=xai_results,
                pred_class=pred_class,
                pred_score=prob,
                filename=f"{os.path.basename(file_path).split('.')[0]}_transformer_xai",
                save_dir=output_dir
            )
        
        print(f"XAI analysis completed for {file_path}")
        
    except Exception as e:
        print(f"Error applying XAI to {file_path}: {e}")
        import traceback
        traceback.print_exc()

def apply_xai_to_directory(model, input_dir, output_dir='xai_results', layer='both'):
    """Áp dụng XAI cho tất cả các file trong một thư mục."""
    if not os.path.exists(input_dir):
        print(f"Error: Directory {input_dir} does not exist.")
        return
    
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # Tìm tất cả các file .npy trong thư mục
    files = []
    for root, _, filenames in os.walk(input_dir):
        for filename in filenames:
            if filename.endswith('.npy'):
                files.append(os.path.join(root, filename))
    
    if not files:
        print(f"No .npy files found in {input_dir}")
        return
    
    print(f"Found {len(files)} files for XAI analysis.")
    
    # Áp dụng XAI cho từng file
    for i, file_path in enumerate(files):
        print(f"[{i+1}/{len(files)}] Analyzing: {file_path}")
        apply_xai_to_file(model, file_path, output_dir, layer)
    
    print(f"XAI analysis completed for all files in {input_dir}")
    print(f"Results saved to {output_dir}")

def main():
    """Hàm chính để chạy mô hình."""
    # Phân tích tham số dòng lệnh
    args = parse_arguments()
    
    # Chế độ huấn luyện
    if args.mode == 'train':
        print("Starting ConvNeXt-Transformer model training and evaluation...")
        
        # Huấn luyện và đánh giá mô hình
        model, metrics = train_and_evaluate()
        
        if model is None:
            print("Error: Model training failed.")
            return
        
        # Lưu mô hình
        try:
            torch.save(model.state_dict(), args.model_path)
            print(f"Model saved to {args.model_path}")
        except Exception as e:
            print(f"Error saving model: {e}")
    
    # Chế độ dự đoán
    elif args.mode == 'predict':
        print("Starting prediction mode...")
        
        # Tải mô hình
        model = load_model(args.model_path)
        if model is None:
            return
        
        # Dự đoán cho một file cụ thể
        if args.input_file:
            if not os.path.exists(args.input_file):
                print(f"Error: File {args.input_file} does not exist.")
                return
            
            print(f"Predicting file: {args.input_file}")
            predict_single_file(model, args.input_file)
        
        # Dự đoán cho tất cả các file trong một thư mục
        elif args.input_dir:
            print(f"Predicting all files in directory: {args.input_dir}")
            predict_directory(model, args.input_dir, args.output_dir)
        
        else:
            print("Error: No input file or directory specified.")
            print("Use --input_file or --input_dir to specify input.")
    
    # Chế độ XAI
    elif args.mode == 'xai':
        print("Starting XAI analysis mode...")
        
        # Tải mô hình
        model = load_model(args.model_path)
        if model is None:
            return
        
        # Áp dụng XAI cho một file cụ thể
        if args.xai_file:
            if not os.path.exists(args.xai_file):
                print(f"Error: File {args.xai_file} does not exist.")
                return
            
            print(f"Applying XAI to file: {args.xai_file}")
            apply_xai_to_file(model, args.xai_file, args.xai_output_dir, args.xai_layer)
        
        # Áp dụng XAI cho tất cả các file trong một thư mục
        elif args.xai_dir:
            print(f"Applying XAI to all files in directory: {args.xai_dir}")
            apply_xai_to_directory(model, args.xai_dir, args.xai_output_dir, args.xai_layer)
        
        else:
            print("Error: No input file or directory specified for XAI analysis.")
            print("Use --xai_file or --xai_dir to specify input.")
    
    else:
        print(f"Error: Unknown mode '{args.mode}'")
        print("Valid modes are: train, predict, xai")

if __name__ == "__main__":
    main()





